{% extends "base.html" %}

{% block title %}Email Applicants - {{ job.title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-envelope"></i>
                        Email Applicants - {{ job.title }}
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('job_detail', job_title=job.title) }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Job
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    {% if job.cvs %}
                    <form method="POST" id="emailForm">
                        <div class="row">
                            <!-- Email Configuration -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email_type">Email Template:</label>
                                    <select class="form-control" id="email_type" name="email_type" onchange="updateTemplate()">
                                        <option value="">Select Template</option>
                                        <option value="application_received">Application Received (Bewerbung eingegangen)</option>
                                        <option value="interview_invitation">Interview Invitation (Einladung zum Vorstellungsgespräch)</option>
                                        <option value="status_update">Status Update (Status-Update)</option>
                                        <option value="custom">Custom Message (Benutzerdefinierte Nachricht)</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="subject">Email Subject:</label>
                                    <input type="text" class="form-control" id="subject" name="subject" 
                                           placeholder="Enter email subject" required>
                                </div>
                                
                                <div class="form-group" id="customMessageGroup" style="display: none;">
                                    <label for="custom_message">Custom Message:</label>
                                    <textarea class="form-control" id="custom_message" name="custom_message" 
                                              rows="8" placeholder="Enter your custom email message here...
Available variables:
{name} - Candidate name
{job_title} - Job title
{status} - Application status
{additional_info} - Additional information"></textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label>Email Preview:</label>
                                    <div id="emailPreview" class="border p-3" style="background-color: #f8f9fa; min-height: 200px;">
                                        <em>Select a template to see preview</em>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Applicant Selection -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Select Applicants:</label>
                                    <div class="mb-2">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAll()">
                                            Select All
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="selectNone()">
                                            Select None
                                        </button>
                                    </div>
                                    
                                    <div class="applicant-list" style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                                        {% for cv in job.cvs %}
                                        <div class="form-check mb-2">
                                            <input class="form-check-input applicant-checkbox" type="checkbox" 
                                                   name="selected_cvs" value="{{ cv.id }}" id="cv_{{ cv.id }}">
                                            <label class="form-check-label" for="cv_{{ cv.id }}">
                                                <strong>{{ cv.candidate_name or 'Unknown' }}</strong><br>
                                                <small class="text-muted">{{ cv.filename }}</small><br>
                                                <small class="text-info">
                                                    {% set email = extract_email_from_content(cv.content) %}
                                                    {% if email %}
                                                        <i class="fas fa-envelope"></i> {{ email }}
                                                    {% else %}
                                                        <i class="fas fa-exclamation-triangle text-warning"></i> No email found
                                                    {% endif %}
                                                </small>
                                            </label>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <span id="selectedCount">0</span> of {{ job.cvs|length }} applicants selected
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>Note:</strong> Only applicants with valid email addresses will receive emails. 
                                    Make sure to configure your email settings in the system before sending.
                                </div>
                                
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane"></i> Send Emails
                                </button>
                                <a href="{{ url_for('job_detail', job_title=job.title) }}" class="btn btn-secondary btn-lg ml-2">
                                    Cancel
                                </a>
                            </div>
                        </div>
                    </form>
                    {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        No applicants found for this job. Upload some CVs first before sending emails.
                    </div>
                    <a href="{{ url_for('upload_cv', job=job.title) }}" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Upload CVs
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Email templates
const templates = {
    'application_received': {
        'subject': 'Bewerbung erhalten - {{ job.title }}',
        'content': `<h2>Bewerbung eingegangen</h2>
<p>Sehr geehrte(r) {name},</p>
<p>vielen Dank für Ihre Bewerbung für die Position <b>{job_title}</b> in unserem Unternehmen.</p>
<p>Wir haben Ihre Bewerbung erhalten und werden sie in Kürze prüfen.</p>
<p>Mit freundlichen Grüßen,<br>HR-Team</p>`
    },
    'interview_invitation': {
        'subject': 'Einladung zum Vorstellungsgespräch - {{ job.title }}',
        'content': `<h2>Einladung zum Vorstellungsgespräch</h2>
<p>Sehr geehrte(r) {name},</p>
<p>wir freuen uns, Sie zu einem Vorstellungsgespräch für die Position <b>{job_title}</b> einzuladen.</p>
<p>Bitte bestätigen Sie Ihre Teilnahme durch eine Antwort auf diese E-Mail.</p>
<p>Mit freundlichen Grüßen,<br>HR-Team</p>`
    },
    'status_update': {
        'subject': 'Status-Update Ihrer Bewerbung - {{ job.title }}',
        'content': `<h2>Aktualisierung des Bewerbungsstatus</h2>
<p>Sehr geehrte(r) {name},</p>
<p>wir möchten Sie über den aktuellen Status Ihrer Bewerbung für die Position <b>{job_title}</b> informieren.</p>
<p>{additional_info}</p>
<p>Mit freundlichen Grüßen,<br>HR-Team</p>`
    }
};

function updateTemplate() {
    const emailType = document.getElementById('email_type').value;
    const subjectField = document.getElementById('subject');
    const customMessageGroup = document.getElementById('customMessageGroup');
    const emailPreview = document.getElementById('emailPreview');
    
    if (emailType === 'custom') {
        customMessageGroup.style.display = 'block';
        subjectField.value = '';
        emailPreview.innerHTML = '<em>Enter your custom message above</em>';
    } else if (emailType && templates[emailType]) {
        customMessageGroup.style.display = 'none';
        subjectField.value = templates[emailType].subject;
        emailPreview.innerHTML = templates[emailType].content;
    } else {
        customMessageGroup.style.display = 'none';
        subjectField.value = '';
        emailPreview.innerHTML = '<em>Select a template to see preview</em>';
    }
}

function selectAll() {
    const checkboxes = document.querySelectorAll('.applicant-checkbox');
    checkboxes.forEach(cb => cb.checked = true);
    updateSelectedCount();
}

function selectNone() {
    const checkboxes = document.querySelectorAll('.applicant-checkbox');
    checkboxes.forEach(cb => cb.checked = false);
    updateSelectedCount();
}

function updateSelectedCount() {
    const checkboxes = document.querySelectorAll('.applicant-checkbox:checked');
    document.getElementById('selectedCount').textContent = checkboxes.length;
}

// Update count when checkboxes change
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('applicant-checkbox')) {
        updateSelectedCount();
    }
});

// Update custom message preview
document.getElementById('custom_message').addEventListener('input', function() {
    const emailType = document.getElementById('email_type').value;
    if (emailType === 'custom') {
        const preview = document.getElementById('emailPreview');
        const message = this.value || '<em>Enter your custom message above</em>';
        preview.innerHTML = message.replace(/\n/g, '<br>');
    }
});

// Initialize
updateSelectedCount();
</script>
{% endblock %}
