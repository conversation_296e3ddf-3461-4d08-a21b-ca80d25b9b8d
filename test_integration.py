#!/usr/bin/env python3
"""
Integration test for the Universal Domain Matcher with existing CV matching system
"""

from matcher import CVMatcher
import tempfile
import os

def test_integration():
    """Test that the new universal matcher integrates properly with existing system"""
    
    print("Testing Universal Domain Matcher Integration")
    print("=" * 50)
    
    # Test job descriptions for different domains
    test_jobs = {
        "CNC Job": """
        CNC-Programmierer (m/w/d)
        - CNC-Programmierung mit Fanuc/Heidenhain
        - Serienfertigung und Qualitätskontrolle
        - Mindestens 3 Jahre Berufserfahrung
        - Ausbildung als Zerspanungsmechaniker
        """,
        
        "Software Job": """
        Java Developer (m/w/d)
        - Java and Spring Boot development
        - REST API and microservices
        - 5+ years experience required
        - Computer Science degree preferred
        """,
        
        "Finance Job": """
        Financial Analyst (m/w/d)
        - Financial reporting and analysis
        - SAP FI/CO experience
        - Bachelor in Finance required
        - 3+ years experience
        """
    }
    
    # Test CV content
    test_cv_content = """
    Max Mustermann
    CNC-Programmierer
    
    Berufserfahrung:
    2020-heute: CNC-Programmierer bei Precision Parts GmbH
    - Programmierung von CNC-Maschinen (Fanuc)
    - Serienfertigung und Qualitätskontrolle
    - Erstmusterprüfung und VDA-Berichte
    
    2017-2020: Zerspanungsmechaniker bei Manufacturing Corp
    - Drehen und Fräsen
    - Werkzeugeinstellung
    
    Ausbildung:
    2014-2017: Ausbildung Zerspanungsmechaniker (IHK)
    """
    
    # Initialize matcher with universal domain matching
    matcher = CVMatcher(use_enhanced_matching=False, use_domain_specific=True)
    
    print(f"Matcher initialized successfully: {matcher.use_domain_specific}")
    print(f"Domain matcher type: {type(matcher.domain_matcher).__name__}")
    
    # Test matching against different job types
    for job_name, job_desc in test_jobs.items():
        print(f"\nTesting {job_name}:")
        
        try:
            # Test with content-based matching
            score = matcher.calculate_match_score_from_content(job_desc, test_cv_content)
            print(f"  Match Score: {score:.1f}%")
            
            # Get detailed explanation
            explanation = matcher.get_match_explanation(job_desc, test_cv_content)
            print(f"  Overall: {explanation['overall_score']:.1f}%")
            print(f"  Skills: {explanation['skill_score']:.1f}%")
            print(f"  Keywords: {explanation['keyword_score']:.1f}%")
            
        except Exception as e:
            print(f"  Error: {e}")
    
    # Test file-based matching (create temporary CV file)
    print(f"\nTesting file-based matching:")
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as temp_file:
        temp_file.write(test_cv_content)
        temp_file_path = temp_file.name
    
    try:
        # Test file-based matching
        score = matcher.calculate_match_score(temp_file_path, test_jobs["CNC Job"])
        print(f"  File-based CNC match: {score:.1f}%")
        
    except Exception as e:
        print(f"  File-based matching error: {e}")
    finally:
        # Clean up temporary file
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
    
    # Test fallback behavior
    print(f"\nTesting fallback behavior:")
    
    # Test with domain matching disabled
    fallback_matcher = CVMatcher(use_enhanced_matching=False, use_domain_specific=False)
    fallback_score = fallback_matcher.calculate_match_score_from_content(
        test_jobs["CNC Job"], test_cv_content
    )
    print(f"  Fallback matcher score: {fallback_score:.1f}%")
    
    print(f"\nIntegration test completed successfully!")
    print(f"The Universal Domain Matcher is properly integrated and working.")

def test_domain_detection():
    """Test domain detection across different job types"""
    
    print(f"\nTesting Domain Detection:")
    print("-" * 30)
    
    from domain_specific_matcher import UniversalDomainMatcher
    
    matcher = UniversalDomainMatcher()
    
    test_descriptions = {
        "Manufacturing": "CNC-Programmierung, Zerspanung, Qualitätskontrolle, Fanuc",
        "Software": "Java development, Spring Boot, REST API, microservices",
        "Finance": "Financial analysis, SAP FI/CO, reporting, IFRS compliance",
        "Healthcare": "Patient care, medical devices, clinical trials, HIPAA",
        "Sales": "Sales experience, CRM, lead generation, customer relationship",
        "General": "Project management, team leadership, communication skills"
    }
    
    for expected_domain, description in test_descriptions.items():
        job_req = matcher.extract_job_requirements(description)
        detected_domain = job_req.domain
        
        print(f"  {expected_domain:12} -> {detected_domain:12} ({'✓' if detected_domain.lower() == expected_domain.lower() else '✗'})")
        print(f"    Core competencies: {job_req.core_competencies[:3]}...")  # Show first 3

if __name__ == "__main__":
    test_integration()
    test_domain_detection()
