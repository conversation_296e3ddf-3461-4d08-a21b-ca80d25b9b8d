#!/usr/bin/env python3
"""
Test the live matching system to see what scores are being produced
"""

from matcher import <PERSON><PERSON><PERSON>atch<PERSON>
from cv_extractor import CVDataExtractor
from hr_database_working import HRDatabase


def test_live_system():
    """Test the exact same system the web app uses"""
    print("🔍 Testing Live Matching System")
    print("=" * 50)
    
    # Initialize the same components as the web app
    hr_db = HRDatabase()
    cv_matcher = CVMatcher()
    
    # Get the test4 job (the one you were testing)
    job = hr_db.get_job_by_title("test4")
    if not job:
        print("❌ Job 'test4' not found")
        return
    
    print(f"📋 Job: {job.title}")
    print(f"📝 Description: {job.description[:200]}...")
    print()
    
    # Get CVs for this job
    cvs = hr_db.get_cvs_for_job("test4")
    if not cvs:
        print("❌ No CVs found for job 'test4'")
        return
    
    print(f"📄 Found {len(cvs)} CVs:")
    for cv in cvs:
        print(f"   - {cv.filename}")
    print()
    
    # Run the exact same matching as the web app
    cv_contents = [cv.content for cv in cvs]
    match_results = cv_matcher.match(job.description, cv_contents)
    
    print("🎯 Match Results (same as web app):")
    print("-" * 40)
    
    # Format results exactly like the web app
    formatted_results = []
    for i, (score, _) in enumerate(match_results):
        cv = cvs[i]
        candidate_name = getattr(cv, 'candidate_name', None) or 'Unknown'
        formatted_results.append({
            'filename': cv.filename,
            'candidate_name': candidate_name,
            'score': float(score) * 100  # Convert to percentage
        })
    
    # Sort by score (like the web template does)
    formatted_results.sort(key=lambda x: x['score'], reverse=True)
    
    for i, result in enumerate(formatted_results, 1):
        print(f"{i}. {result['candidate_name']} ({result['filename']}): {result['score']:.2f}%")
    
    # Check specific candidates
    maria_result = next((r for r in formatted_results if 'Maria' in r['filename']), None)
    max_result = next((r for r in formatted_results if 'Max' in r['filename']), None)
    
    if maria_result and max_result:
        print(f"\n🔍 Specific Analysis:")
        print(f"   Maria Schmidt: {maria_result['score']:.2f}%")
        print(f"   Max Müller: {max_result['score']:.2f}%")
        
        if maria_result['score'] > max_result['score']:
            print("   ✅ Ranking is correct: Maria > Max")
        else:
            print("   ❌ Ranking is incorrect: Max > Maria")
            print("   🔧 This suggests the improved algorithm isn't being used")
    
    # Test individual score calculation
    print(f"\n🧪 Individual Score Testing:")
    print("-" * 30)
    
    for cv in cvs:
        if 'Maria' in cv.filename or 'Max' in cv.filename:
            score = cv_matcher.calculate_match_score(cv.content, job.description)
            print(f"{cv.filename}: {score:.2f}%")
            
            # Get detailed breakdown
            tf_idf = cv_matcher.calculate_tf_idf_similarity(job.description, cv.content)
            keyword = cv_matcher.calculate_keyword_match(job.description, cv.content)
            skill = cv_matcher.calculate_skill_match(job.description, cv.content)
            
            print(f"   TF-IDF: {tf_idf*100:.1f}%, Keywords: {keyword*100:.1f}%, Skills: {skill*100:.1f}%")


def test_with_java_job():
    """Test with a Java job description"""
    print("\n🎯 Testing with Java Job Description")
    print("=" * 50)
    
    cv_matcher = CVMatcher()
    extractor = CVDataExtractor()
    
    java_job = """
    Java Software Developer Position
    
    Requirements:
    - Java programming (3+ years)
    - Spring Boot framework
    - PostgreSQL database
    - REST API development
    - Git, Jenkins, CI/CD
    - Scrum methodology
    - Bachelor's degree in Computer Science
    - German and English language skills
    """
    
    # Test with Maria and Max CVs
    maria_cv = "uploads/CV_Maria_Schmidt_80.pdf"
    max_cv = "uploads/CV_Max_Mueller_20.pdf"
    
    for cv_file in [maria_cv, max_cv]:
        if not os.path.exists(cv_file):
            print(f"❌ {cv_file} not found")
            continue
            
        cv_content = extractor.extract_text_from_file(cv_file)
        if not cv_content:
            print(f"❌ Could not extract content from {cv_file}")
            continue
        
        score = cv_matcher.calculate_match_score(cv_content, java_job)
        name = "Maria Schmidt" if "Maria" in cv_file else "Max Müller"
        
        print(f"{name}: {score:.2f}%")
        
        # Detailed breakdown
        tf_idf = cv_matcher.calculate_tf_idf_similarity(java_job, cv_content)
        keyword = cv_matcher.calculate_keyword_match(java_job, cv_content)
        skill = cv_matcher.calculate_skill_match(java_job, cv_content)
        
        print(f"   TF-IDF: {tf_idf*100:.1f}%, Keywords: {keyword*100:.1f}%, Skills: {skill*100:.1f}%")


if __name__ == "__main__":
    import os
    test_live_system()
    test_with_java_job()
