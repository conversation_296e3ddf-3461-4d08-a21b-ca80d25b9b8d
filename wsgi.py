#!/usr/bin/env python3
"""
WSGI entry point for BAUCH HR Management System
For production deployment with Gunicorn, uWSGI, or similar WSGI servers
"""

import os
import sys
from pathlib import Path

# Add the application directory to Python path
app_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(app_dir))

# Set environment variables for production
os.environ.setdefault('FLASK_ENV', 'production')
os.environ.setdefault('FLASK_DEBUG', 'False')

# Import the Flask application
from app import app

# Configure for production
app.config.update(
    SECRET_KEY=os.environ.get('SECRET_KEY', 'your-secret-key-change-this-in-production'),
    DEBUG=False,
    TESTING=False,
    # Database configuration
    DATABASE_URL=os.environ.get('DATABASE_URL', f'sqlite:///{app_dir}/hr_database.db'),
    # Upload configuration
    UPLOAD_FOLDER=os.environ.get('UPLOAD_FOLDER', str(app_dir / 'uploads')),
    MAX_CONTENT_LENGTH=16 * 1024 * 1024,  # 16MB max file size
    # Security configuration
    SESSION_COOKIE_SECURE=True,
    SESSION_COOKIE_HTTPONLY=True,
    SESSION_COOKIE_SAMESITE='Lax',
    PERMANENT_SESSION_LIFETIME=3600,  # 1 hour
)

# Ensure upload directory exists
upload_dir = Path(app.config['UPLOAD_FOLDER'])
upload_dir.mkdir(exist_ok=True)

if __name__ == "__main__":
    # For development only
    app.run(host='0.0.0.0', port=5000, debug=False)
else:
    # For production WSGI servers
    application = app
