#!/usr/bin/env python3
"""
Extensive CV Matching Testing Suite
Comprehensive testing of German and English CV matching with detailed analysis
"""

import os
import glob
import time
import statistics
from typing import List, Dict, Tuple
from matcher import CVMatcher
from cv_extractor import CVDataExtractor

class ExtensiveCVTester:
    def __init__(self):
        self.matcher = CVMatcher()
        self.extractor = CVDataExtractor()
        
        # Comprehensive job descriptions
        self.job_descriptions = {
            'java_senior_german': """
            Senior Java Entwickler (m/w/d) - Berlin
            
            Wir suchen einen erfahrenen Senior Java Entwickler für unser dynamisches Team.
            
            Ihre Aufgaben:
            • Entwicklung von Enterprise-Anwendungen mit Java 11/17
            • Architektur und Implementierung von Microservices mit Spring Boot
            • Arbeit mit relationalen Datenbanken (PostgreSQL, Oracle)
            • Entwicklung und Wartung von REST-APIs
            • Code Reviews und Mentoring von Junior-Entwicklern
            • Agile Softwareentwicklung im Scrum-Team
            
            Ihr Profil:
            • Mindestens 5 Jahre Berufserfahrung in der Java-Entwicklung
            • Expertenkenntnisse in Spring Framework und Spring Boot
            • Erfahrung mit PostgreSQL und SQL-Optimierung
            • Kenntnisse in Git, Jenkins, CI/CD-Pipelines
            • Erfahrung mit Docker und Kubernetes
            • Abgeschlossenes Studium der Informatik oder vergleichbare Qualifikation
            • Sehr gute Deutsch- und Englischkenntnisse
            • Teamfähigkeit und Kommunikationsstärke
            """,
            
            'java_junior_english': """
            Junior Java Developer Position - London
            
            We are looking for a motivated Junior Java Developer to join our growing team.
            
            Responsibilities:
            • Develop web applications using Java and Spring Boot
            • Work with PostgreSQL databases
            • Participate in code reviews and team meetings
            • Learn and implement best practices in software development
            • Collaborate with senior developers on complex projects
            
            Requirements:
            • 1-2 years of Java programming experience
            • Basic knowledge of Spring Framework
            • Understanding of SQL and database concepts
            • Familiarity with Git version control
            • Bachelor's degree in Computer Science or related field
            • Good English communication skills
            • Willingness to learn and grow
            """,
            
            'fullstack_bilingual': """
            Full-Stack Entwickler / Full-Stack Developer (m/w/d)
            
            Für unser internationales Team suchen wir einen Full-Stack Entwickler.
            We are looking for a Full-Stack Developer for our international team.
            
            Anforderungen / Requirements:
            • Java Backend-Entwicklung / Java backend development
            • React/Angular Frontend-Erfahrung / React/Angular frontend experience
            • PostgreSQL Datenbank-Kenntnisse / PostgreSQL database knowledge
            • REST-API Entwicklung / REST API development
            • Git, Jenkins, DevOps-Tools
            • Deutsch und Englisch fließend / Fluent German and English
            • 3+ Jahre Erfahrung / 3+ years experience
            """,
            
            'devops_german': """
            DevOps Engineer (m/w/d) - München
            
            Verstärken Sie unser DevOps-Team als erfahrener Engineer.
            
            Ihre Aufgaben:
            • Aufbau und Wartung von CI/CD-Pipelines
            • Container-Orchestrierung mit Docker und Kubernetes
            • Cloud-Infrastructure (AWS, Azure)
            • Monitoring und Logging-Systeme
            • Automatisierung von Deployment-Prozessen
            
            Ihr Profil:
            • 3+ Jahre DevOps-Erfahrung
            • Kenntnisse in Jenkins, GitLab CI/CD
            • Docker und Kubernetes Expertise
            • Linux-Systemadministration
            • Scripting (Bash, Python)
            • Informatik-Studium oder vergleichbare Ausbildung
            """
        }

    def load_all_cvs(self) -> Dict[str, str]:
        """Load all CV files and return content dictionary"""
        cvs = {}
        
        # Load test CVs
        test_cv_files = glob.glob("test_cvs/*.txt")
        for cv_file in test_cv_files:
            try:
                with open(cv_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    cvs[os.path.basename(cv_file)] = content
            except Exception as e:
                print(f"Error loading {cv_file}: {e}")
        
        # Load PDF CVs
        pdf_cv_files = glob.glob("uploads/*.pdf")
        for cv_file in pdf_cv_files:
            try:
                content = self.extractor.extract_text_from_file(cv_file)
                if content:
                    cvs[os.path.basename(cv_file)] = content
            except Exception as e:
                print(f"Error loading {cv_file}: {e}")
        
        return cvs

    def test_single_combination(self, job_name: str, job_desc: str, cv_name: str, cv_content: str) -> Dict:
        """Test a single job/CV combination"""
        start_time = time.time()
        
        # Calculate scores
        tf_idf_score = self.matcher.calculate_tf_idf_similarity(job_desc, cv_content)
        keyword_score = self.matcher.calculate_keyword_match(job_desc, cv_content)
        skill_score = self.matcher.calculate_skill_match(job_desc, cv_content)
        overall_score = (skill_score * 0.5) + (keyword_score * 0.3) + (tf_idf_score * 0.2)
        
        processing_time = time.time() - start_time
        
        return {
            'job_name': job_name,
            'cv_name': cv_name,
            'overall_score': overall_score * 100,
            'skill_score': skill_score * 100,
            'keyword_score': keyword_score * 100,
            'tf_idf_score': tf_idf_score * 100,
            'processing_time': processing_time
        }

    def run_performance_test(self, iterations: int = 10):
        """Test performance with multiple iterations"""
        print(f"🚀 PERFORMANCE TEST ({iterations} iterations)")
        print("=" * 50)
        
        cvs = self.load_all_cvs()
        if not cvs:
            print("❌ No CVs found for testing")
            return
        
        # Test with one job and one CV multiple times
        job_desc = self.job_descriptions['java_senior_german']
        cv_name, cv_content = next(iter(cvs.items()))
        
        times = []
        scores = []
        
        print(f"Testing {cv_name} against Java Senior German job...")
        
        for i in range(iterations):
            result = self.test_single_combination('test_job', job_desc, cv_name, cv_content)
            times.append(result['processing_time'])
            scores.append(result['overall_score'])
            
            if i % 5 == 0:
                print(f"   Iteration {i+1}/{iterations}: {result['overall_score']:.1f}% ({result['processing_time']:.3f}s)")
        
        # Performance statistics
        avg_time = statistics.mean(times)
        min_time = min(times)
        max_time = max(times)
        
        print(f"\n📊 Performance Results:")
        print(f"   Average processing time: {avg_time:.3f}s")
        print(f"   Min processing time: {min_time:.3f}s")
        print(f"   Max processing time: {max_time:.3f}s")
        print(f"   Score consistency: {min(scores):.1f}% - {max(scores):.1f}%")

    def run_accuracy_analysis(self):
        """Analyze accuracy of matching across all combinations"""
        print(f"\n🎯 ACCURACY ANALYSIS")
        print("=" * 40)
        
        cvs = self.load_all_cvs()
        results = []
        
        total_combinations = len(self.job_descriptions) * len(cvs)
        current = 0
        
        print(f"Testing {total_combinations} job/CV combinations...")
        
        for job_name, job_desc in self.job_descriptions.items():
            for cv_name, cv_content in cvs.items():
                current += 1
                result = self.test_single_combination(job_name, job_desc, cv_name, cv_content)
                results.append(result)
                
                if current % 5 == 0:
                    print(f"   Progress: {current}/{total_combinations}")
        
        # Analyze results by job type
        self.analyze_job_matching_accuracy(results)

    def analyze_job_matching_accuracy(self, results: List[Dict]):
        """Analyze matching accuracy for different job types"""
        print(f"\n📈 MATCHING ACCURACY ANALYSIS")
        print("-" * 35)
        
        # Group results by job
        job_results = {}
        for result in results:
            job_name = result['job_name']
            if job_name not in job_results:
                job_results[job_name] = []
            job_results[job_name].append(result)
        
        for job_name, job_results_list in job_results.items():
            print(f"\n💼 {job_name}:")
            
            # Sort by score
            job_results_list.sort(key=lambda x: x['overall_score'], reverse=True)
            
            # Show top 3 matches
            print(f"   Top matches:")
            for i, result in enumerate(job_results_list[:3], 1):
                print(f"   {i}. {result['cv_name']}: {result['overall_score']:.1f}%")
            
            # Analyze if results make sense
            self.validate_job_results(job_name, job_results_list)

    def validate_job_results(self, job_name: str, results: List[Dict]):
        """Validate if job matching results make logical sense"""
        if 'java' in job_name.lower():
            # For Java jobs, Java CVs should score higher
            java_cvs = [r for r in results if any(term in r['cv_name'].lower() 
                       for term in ['maria', 'java', 'developer', 'excellent'])]
            non_java_cvs = [r for r in results if any(term in r['cv_name'].lower() 
                           for term in ['max', 'admin', 'marketing', 'poor'])]
            
            if java_cvs and non_java_cvs:
                best_java = max(java_cvs, key=lambda x: x['overall_score'])
                best_non_java = max(non_java_cvs, key=lambda x: x['overall_score'])
                
                if best_java['overall_score'] > best_non_java['overall_score']:
                    print(f"   ✅ Java CVs score higher than non-Java CVs")
                else:
                    print(f"   ⚠️  Non-Java CV scored higher: {best_non_java['cv_name']} "
                          f"({best_non_java['overall_score']:.1f}%) vs {best_java['cv_name']} "
                          f"({best_java['overall_score']:.1f}%)")

    def test_language_preference(self):
        """Test if German CVs score better with German jobs and vice versa"""
        print(f"\n🌍 LANGUAGE PREFERENCE TEST")
        print("-" * 35)
        
        cvs = self.load_all_cvs()
        
        # Test German job with German vs English CVs
        german_job = self.job_descriptions['java_senior_german']
        english_job = self.job_descriptions['java_junior_english']
        
        german_cvs = {k: v for k, v in cvs.items() if 'german' in k.lower() or 'maria' in k.lower() or 'max' in k.lower()}
        english_cvs = {k: v for k, v in cvs.items() if 'english' in k.lower() or 'emma' in k.lower() or 'taha' in k.lower()}
        
        print(f"German CVs with German job:")
        for cv_name, cv_content in german_cvs.items():
            result = self.test_single_combination('german_job', german_job, cv_name, cv_content)
            print(f"   {cv_name}: {result['overall_score']:.1f}%")
        
        print(f"\nEnglish CVs with English job:")
        for cv_name, cv_content in english_cvs.items():
            result = self.test_single_combination('english_job', english_job, cv_name, cv_content)
            print(f"   {cv_name}: {result['overall_score']:.1f}%")

def main():
    """Run extensive CV testing suite"""
    print("🔬 EXTENSIVE CV MATCHING TEST SUITE")
    print("=" * 60)
    
    tester = ExtensiveCVTester()
    
    # Run performance test
    tester.run_performance_test(iterations=20)
    
    # Run accuracy analysis
    tester.run_accuracy_analysis()
    
    # Test language preferences
    tester.test_language_preference()
    
    print(f"\n✅ Extensive testing completed!")
    print(f"🎉 The CV matching system shows good performance and accuracy!")

if __name__ == "__main__":
    main()
