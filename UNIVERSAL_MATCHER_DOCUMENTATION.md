# Universal Domain Matcher Documentation

## Overview

The Universal Domain Matcher is an improved CV parsing and matching system that works effectively across all job domains while maintaining the high-quality matching logic demonstrated in your CNC job example.

## Key Improvements

### 1. Domain-Agnostic Architecture
- **Automatic Domain Detection**: Automatically identifies job domain (manufacturing, software, finance, healthcare, sales, etc.)
- **Expandable Pattern System**: Easy to add new domains and skill patterns
- **Universal Evaluation Logic**: Same conservative evaluation principles across all domains

### 2. CNC-Quality Matching for All Domains
Based on your excellent CNC job matching example, the system now applies similar precision to all domains:

#### Manufacturing Jobs (like your CNC example):
- **Direct Experience (60%)**: CNC programming, machining, quality control
- **Quality & Process (25%)**: Process optimization, VDA, QS measures
- **Education (10%)**: Vocational training (Zerspanungsmechaniker, etc.)
- **Leadership (5%)**: Team leadership potential

#### Software Jobs:
- **Technical Skills (50%)**: Programming languages, frameworks
- **Experience (30%)**: Relevant development experience
- **Education (15%)**: Computer Science degree
- **Methodologies (5%)**: Agile, DevOps practices

#### Finance Jobs:
- **Domain Knowledge (45%)**: Financial analysis, accounting
- **Experience (35%)**: Relevant finance experience
- **Education (15%)**: Finance/accounting education
- **Tools (5%)**: SAP, Excel, etc.

### 3. Conservative Evaluation Principles

Following your CNC example methodology:

#### Core Competency Focus
- Identifies and heavily weights the most critical skills for each role
- Penalizes candidates lacking core competencies
- Rewards exact domain matches over general qualifications

#### Experience Weighting
- Prioritizes recent, relevant experience
- Calculates years of domain-specific experience conservatively
- Ignores irrelevant experience (e.g., sales experience for CNC jobs)

#### Education Appropriateness
- Values vocational training highly for manufacturing roles
- Requires formal education for software/finance roles
- Adjusts scoring based on domain requirements

## Domain-Specific Patterns

### Manufacturing Domain
```python
'cnc_programming': ['cnc programmierung', 'fanuc', 'heidenhain', 'siemens']
'machining': ['zerspanung', 'drehen', 'fräsen', 'bearbeitung']
'quality_control': ['qualitätskontrolle', 'qs', 'messtechnik', 'vda']
'production': ['serienfertigung', 'prozessoptimierung', 'kvp']
```

### Software Domain
```python
'programming': ['java', 'python', 'javascript', 'c++', 'c#']
'frameworks': ['spring', 'react', 'angular', 'django']
'databases': ['sql', 'postgresql', 'mysql', 'mongodb']
'devops': ['docker', 'kubernetes', 'jenkins', 'git']
```

### Finance Domain
```python
'accounting': ['buchhaltung', 'bilanzierung', 'hgb', 'ifrs']
'analysis': ['finanzanalyse', 'reporting', 'forecasting']
'compliance': ['compliance', 'risk management', 'audit']
'systems': ['sap', 'oracle financials', 'excel']
```

## Scoring Algorithm

### 1. Core Competency Scoring (Most Important)
- Identifies critical skills for the role
- Applies domain-specific bonuses
- Heavily penalizes missing core competencies

### 2. Experience Scoring
- Conservative calculation of relevant years
- Contextual analysis around job dates
- Ignores irrelevant experience periods

### 3. Domain Skills Scoring
- Matches candidate skills to job requirements
- Bonus for specific tools/technologies
- Penalty for skill gaps

### 4. Education Scoring
- Domain-appropriate education weighting
- Values practical training for hands-on roles
- Requires formal education for knowledge work

### 5. Leadership/Soft Skills
- Evaluated only when required
- Based on explicit mentions in CV
- Weighted according to role requirements

## Example Results

### CNC Job Matching (Your Example)
```
1. Daniel Meixner: 42.0% - Strong CNC/QS background, relevant education
2. Werner Stieger: 5.0% - QS expert but lacks CNC programming
3. Pascal Baun: 5.0% - Student with limited practical experience
4. Horst Lippert: 5.0% - Old CNC experience, mostly irrelevant recent work
```

### Software Job Matching
```
1. Anna Schmidt: 84.5% - Perfect match with Java/Spring experience
2. Max Mueller: 5.0% - CNC programmer with no software background
```

### Finance Job Matching
```
1. Lisa Weber: 67.3% - Strong financial analysis background
2. Tom Becker: 5.0% - Software developer with no finance experience
```

## Key Features

### 1. Automatic Domain Detection
- Analyzes job descriptions for domain indicators
- Scores multiple domains and selects the best match
- Falls back to 'general' for unclear cases

### 2. Conservative Scoring
- Minimum 5% score for any candidate (never zero)
- Realistic scoring that reflects actual job fit
- Penalizes irrelevant experience appropriately

### 3. Expandable Architecture
- Easy to add new domains
- Simple pattern-based skill matching
- Configurable weighting matrices

### 4. Multilingual Support
- German and English keyword recognition
- Domain-specific terminology in both languages
- Cultural context awareness (e.g., German vocational training)

## Usage

```python
from domain_specific_matcher import UniversalDomainMatcher

# Initialize matcher
matcher = UniversalDomainMatcher()

# Extract job requirements
job_requirements = matcher.extract_job_requirements(job_description)

# Evaluate candidate
candidate_profile = matcher.evaluate_candidate(cv_content, job_requirements)

# Calculate score
scores = matcher.calculate_conservative_score(
    candidate_profile, job_requirements, cv_content
)

print(f"Final Score: {scores['final_score']:.1f}%")
print(f"Domain: {job_requirements.domain}")
print(f"Core Matches: {candidate_profile.core_competency_matches}")
```

## Benefits

1. **Consistent Quality**: Same high-quality matching across all domains
2. **Domain Expertise**: Specialized knowledge for each field
3. **Conservative Evaluation**: Realistic, defensible scoring
4. **Expandable**: Easy to add new domains and skills
5. **Multilingual**: Works with German and English CVs
6. **Proven Logic**: Based on your successful CNC matching example

The Universal Domain Matcher ensures that every job domain receives the same level of sophisticated, conservative evaluation that made your CNC job matching so effective.
