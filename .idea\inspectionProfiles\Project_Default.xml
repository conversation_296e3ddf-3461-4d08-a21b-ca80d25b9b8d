<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="10">
            <item index="0" class="java.lang.String" itemvalue="mediapipe" />
            <item index="1" class="java.lang.String" itemvalue="numpy" />
            <item index="2" class="java.lang.String" itemvalue="faiss-gpu" />
            <item index="3" class="java.lang.String" itemvalue="gstreamer-python" />
            <item index="4" class="java.lang.String" itemvalue="openexr" />
            <item index="5" class="java.lang.String" itemvalue="plyfile" />
            <item index="6" class="java.lang.String" itemvalue="paho-mqtt" />
            <item index="7" class="java.lang.String" itemvalue="pytorch_msssim" />
            <item index="8" class="java.lang.String" itemvalue="pytorch_ssim" />
            <item index="9" class="java.lang.String" itemvalue="lpips" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>