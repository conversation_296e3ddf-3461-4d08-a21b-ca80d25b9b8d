# BAUCH HR Management System - Bilingual Edition

## Overview

This is an enhanced version of the BAUCH HR Management System with comprehensive German and English language support. The system can automatically detect, process, and match CVs and job descriptions in both languages.

## 🌟 New Bilingual Features

### Language Detection
- **Automatic Language Detection**: Automatically detects whether CVs and job descriptions are in German, English, or mixed
- **Confidence Scoring**: Provides confidence scores for language detection
- **Bilingual Content Support**: Handles documents that contain both German and English content

### Enhanced CV Processing
- **Bilingual CV Extraction**: Extracts information from CVs in both German and English
- **German-specific Patterns**: Recognizes German name patterns, phone formats, and experience descriptions
- **Skill Recognition**: Identifies both German and English technical and soft skills
- **Education Parsing**: Understands German and English education systems and terminology

### Advanced Matching Algorithm
- **Bilingual Matching**: Matches CVs to jobs regardless of language combination
- **Language Compatibility Bonus**: Provides bonus scores for language compatibility
- **Enhanced Skill Matching**: Recognizes equivalent skills in both languages
- **Detailed Scoring**: Provides breakdown of matching scores by component

### User Interface
- **Language Switcher**: Easy toggle between German and English interface
- **Localized Content**: All UI elements translated appropriately
- **Session Persistence**: Remembers language preference across sessions

## 🚀 Installation

### Prerequisites
- Python 3.8 or higher
- pip package manager
- Internet connection (for downloading language models)

### Quick Installation
Run the automated installation script:
```bash
python install_bilingual_support.py
```

### Manual Installation
1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

2. Download spaCy language models:
```bash
python -m spacy download en_core_web_sm
python -m spacy download de_core_news_sm
```

3. Verify installation:
```bash
python -c "import spacy; spacy.load('en_core_web_sm'); spacy.load('de_core_news_sm'); print('✓ Installation successful')"
```

## 📋 Dependencies

### Core Dependencies
- **Flask 2.3.3**: Web framework
- **spaCy 3.7.2**: Natural language processing
- **langdetect 1.0.9**: Language detection
- **googletrans 4.0.0rc1**: Translation services
- **scikit-learn 1.3.2**: Machine learning for text similarity

### Language Models
- **en_core_web_sm**: English language model for spaCy
- **de_core_news_sm**: German language model for spaCy

## 🔧 Configuration

### Language Settings
The system can be configured in `config.py`:

```python
# Default language
DEFAULT_LANGUAGE = 'en'  # or 'de'

# Supported languages
SUPPORTED_LANGUAGES = ['en', 'de']

# Language detection confidence threshold
LANGUAGE_DETECTION_MIN_CONFIDENCE = 0.6
```

### Matching Algorithm Weights
Customize the matching algorithm weights:

```python
MATCHING_WEIGHTS = {
    'tf_idf': 0.35,        # Content similarity
    'keyword': 0.25,       # Keyword matching
    'skill': 0.35,         # Skill matching
    'language_bonus': 0.05 # Language compatibility bonus
}
```

## 🎯 Usage

### Language Switching
1. Click the language dropdown in the top navigation
2. Select "English" or "Deutsch"
3. The interface will immediately switch languages

### Uploading Bilingual CVs
1. Navigate to "CVs" → "Upload CV"
2. Select a job and enter candidate name
3. Upload PDF or DOCX file in any supported language
4. The system will automatically detect the language and extract information

### Bilingual Job Matching
1. Go to "Match" section
2. Select a job (in any language)
3. The system will match all CVs regardless of their language
4. View detailed scoring breakdown including language compatibility

### Viewing Match Results
The enhanced match results show:
- **Overall Match Score**: Combined score from all factors
- **Content Similarity**: TF-IDF based similarity
- **Keyword Match**: Direct keyword overlap
- **Skill Match**: Technical and soft skill alignment
- **Language Bonus**: Compatibility bonus/penalty
- **Language Information**: Detected languages for job and CV

## 📊 Features in Detail

### Language Detection
```python
from language_detector import LanguageDetector

detector = LanguageDetector()
language, confidence = detector.detect_language(text)
print(f"Detected: {language} (confidence: {confidence})")
```

### Bilingual CV Extraction
```python
from bilingual_cv_extractor import BilingualCVExtractor

extractor = BilingualCVExtractor()
data = extractor.extract_cv_data_bilingual(
    file_path, 
    ['name', 'email', 'phone', 'experience', 'skills', 'education']
)
```

### Enhanced Matching
```python
from bilingual_matcher import BilingualCVMatcher

matcher = BilingualCVMatcher()
results = matcher.match_bilingual(job_description, cv_contents)
```

## 🔍 Supported Content Types

### German Content Recognition
- **Names**: German name patterns with umlauts (ä, ö, ü, ß)
- **Phone Numbers**: German phone number formats (+49, 0xxx)
- **Experience**: German experience patterns ("Jahre Berufserfahrung")
- **Education**: German education system terms
- **Skills**: German technical and soft skill terminology

### English Content Recognition
- **Standard Patterns**: Traditional English CV patterns
- **International Formats**: Various English-speaking country formats
- **Technical Skills**: Comprehensive English skill database
- **Experience Levels**: Junior, Mid-level, Senior classifications

## 🎨 UI Translations

### Navigation
- Home / Startseite
- Jobs / Stellenanzeigen
- CVs / Lebensläufe
- Match / Abgleich

### Common Actions
- Add / Hinzufügen
- Edit / Bearbeiten
- Delete / Löschen
- Upload / Hochladen
- Search / Suchen

### Status Messages
- Success / Erfolgreich
- Error / Fehler
- Loading / Lädt...

## 🧪 Testing

### Test Files
The installation script creates sample test files:
- `test_files/german_cv_test.txt`: Sample German CV
- `test_files/english_cv_test.txt`: Sample English CV

### Manual Testing
1. Upload German and English CVs
2. Create jobs in both languages
3. Test matching across language combinations
4. Verify language detection accuracy
5. Check UI translation completeness

## 🔧 Troubleshooting

### Common Issues

**Language models not found:**
```bash
python -m spacy download en_core_web_sm
python -m spacy download de_core_news_sm
```

**Translation service errors:**
- Check internet connection
- Verify googletrans installation
- Consider using offline translation for production

**Low language detection confidence:**
- Ensure text has sufficient content (>50 words recommended)
- Check for mixed-language content
- Review language indicator patterns

### Performance Optimization
- Enable caching for translations
- Use local translation services for production
- Optimize spaCy model loading

## 📈 Future Enhancements

### Planned Features
- Additional language support (French, Spanish, Italian)
- Improved translation caching
- Custom skill dictionaries per industry
- Advanced bilingual search functionality
- Export capabilities in multiple languages

### Contributing
To add support for additional languages:
1. Add language code to `SUPPORTED_LANGUAGES`
2. Create language-specific skill dictionaries
3. Add UI translations
4. Update language detection patterns
5. Test thoroughly with native content

## 📞 Support

For issues related to bilingual functionality:
1. Check the troubleshooting section
2. Verify all dependencies are installed
3. Test with sample files provided
4. Review configuration settings

## 📄 License

This bilingual enhancement maintains the same license as the original BAUCH HR Management System.

---

**Note**: This bilingual system significantly enhances the original HR management capabilities by providing seamless German-English language support, making it ideal for international companies or German organizations working with English-speaking candidates.
