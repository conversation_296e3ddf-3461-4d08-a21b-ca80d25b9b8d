#!/usr/bin/env python3
"""
Simple test of the matcher fixes
"""

try:
    from matcher import CVMatcher
    print("✅ Matcher imported successfully")
    
    matcher = CVMatcher()
    print("✅ Matcher created successfully")
    
    # Test the new method
    test_cv = "2020-2024 CNC Programmierung bei Firma XYZ. Fanuc <PERSON>ung, Heidenhain Programmierung."
    relevant_years = matcher._years_of_relevant_experience(test_cv.lower(), [])
    print(f"✅ Relevant years calculation: {relevant_years}")
    
    # Test skill matching
    job_desc = "CNC Fräser gesucht mit Fanuc Erfahrung"
    cv_content = "Erfahrung mit CNC Fräsen und Fanuc Steuerung seit 2020"
    
    skill_score = matcher.calculate_skill_match(job_desc, cv_content)
    print(f"✅ Skill score: {skill_score}")
    
    print("✅ All basic tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
