"""
Email Service Demo
Demonstrates email service functionality without sending real emails
"""

import os
import json
from email_service import EmailService


def demo_email_configuration():
    """Demonstrate email service configuration options"""
    print("📧 Email Service Configuration Demo")
    print("-" * 50)
    
    # Method 1: Direct configuration
    print("1. Direct Configuration:")
    service1 = EmailService(
        mail_server='smtp.gmail.com',
        mail_port=587,
        mail_username='<EMAIL>',
        mail_password='your-app-password',
        default_sender='<EMAIL>'
    )
    print(f"   Server: {service1.mail_server}")
    print(f"   Port: {service1.mail_port}")
    print(f"   Username: {service1.mail_username}")
    print(f"   Default Sender: {service1.default_sender}")
    
    # Method 2: Environment variables (recommended for production)
    print("\n2. Environment Variables Configuration:")
    print("   Set these environment variables:")
    print("   MAIL_SERVER=smtp.gmail.com")
    print("   MAIL_PORT=587")
    print("   MAIL_USERNAME=<EMAIL>")
    print("   MAIL_PASSWORD=your-app-password")
    print("   MAIL_DEFAULT_SENDER=<EMAIL>")
    
    return service1


def demo_email_templates():
    """Demonstrate email template functionality"""
    print("\n📝 Email Templates Demo")
    print("-" * 50)
    
    service = EmailService()
    
    # Get English templates
    print("1. English Templates:")
    en_templates = service.get_default_templates('en')
    for template_name in en_templates.keys():
        print(f"   ✓ {template_name}")
    
    # Get German templates
    print("\n2. German Templates:")
    de_templates = service.get_default_templates('de')
    for template_name in de_templates.keys():
        print(f"   ✓ {template_name}")
    
    # Show sample template
    print("\n3. Sample Template (Application Received - English):")
    sample_template = en_templates['application_received']
    print("   Template content:")
    print("   " + sample_template.strip().replace('\n', '\n   '))
    
    return en_templates, de_templates


def demo_template_formatting():
    """Demonstrate template variable substitution"""
    print("\n🔧 Template Formatting Demo")
    print("-" * 50)
    
    service = EmailService()
    templates = service.get_default_templates('en')
    
    # Sample data for template formatting
    candidate_data = {
        'name': 'John Smith',
        'job_title': 'Senior Python Developer',
        'interview_date': 'March 15, 2024',
        'interview_time': '2:00 PM',
        'interview_location': 'Conference Room A, 5th Floor'
    }
    
    print("1. Candidate Data:")
    for key, value in candidate_data.items():
        print(f"   {key}: {value}")
    
    print("\n2. Formatted Interview Invitation:")
    interview_template = templates['interview_invitation']
    formatted_email = interview_template.format(**candidate_data)
    
    # Show formatted result (truncated for display)
    lines = formatted_email.strip().split('\n')
    for i, line in enumerate(lines[:15]):  # Show first 15 lines
        print(f"   {line}")
    if len(lines) > 15:
        print("   ...")
    
    return formatted_email


def demo_bulk_email_preparation():
    """Demonstrate bulk email preparation"""
    print("\n📬 Bulk Email Preparation Demo")
    print("-" * 50)
    
    # Sample recipients data
    recipients = [
        {
            'email': '<EMAIL>',
            'name': 'John Smith',
            'job_title': 'Senior Python Developer',
            'status': 'under review'
        },
        {
            'email': '<EMAIL>',
            'name': 'Jane Doe',
            'job_title': 'Frontend Developer',
            'status': 'interview scheduled'
        },
        {
            'email': '<EMAIL>',
            'name': 'Bob Wilson',
            'job_title': 'DevOps Engineer',
            'status': 'accepted'
        }
    ]
    
    print("1. Recipients Data:")
    for i, recipient in enumerate(recipients, 1):
        print(f"   {i}. {recipient['name']} ({recipient['email']})")
        print(f"      Job: {recipient['job_title']}")
        print(f"      Status: {recipient['status']}")
    
    # Demonstrate template preparation
    service = EmailService()
    templates = service.get_default_templates('en')
    
    print("\n2. Email Preparation (Status Update Template):")
    status_template = templates['status_update']
    
    for recipient in recipients:
        # Add additional info based on status
        if recipient['status'] == 'under review':
            recipient['additional_info'] = 'We will contact you within 5 business days.'
        elif recipient['status'] == 'interview scheduled':
            recipient['additional_info'] = 'Please check your email for interview details.'
        elif recipient['status'] == 'accepted':
            recipient['additional_info'] = 'Congratulations! We will send you the offer letter soon.'
        
        print(f"\n   Email for {recipient['name']}:")
        try:
            formatted = status_template.format(**recipient)
            # Show just the key parts
            if 'Dear {name}' in status_template:
                name_line = f"Dear {recipient['name']}"
                print(f"      {name_line}")
            print(f"      Job: {recipient['job_title']}")
            print(f"      Status: {recipient['status']}")
            print(f"      Info: {recipient['additional_info']}")
        except KeyError as e:
            print(f"      ❌ Missing template variable: {e}")
    
    return recipients


def demo_error_handling():
    """Demonstrate error handling scenarios"""
    print("\n⚠️  Error Handling Demo")
    print("-" * 50)
    
    print("1. Incomplete Configuration:")
    incomplete_service = EmailService(mail_server='smtp.test.com')
    print("   ❌ Missing username and password")
    print("   Result: Email sending will fail gracefully")
    
    print("\n2. Missing Template Variables:")
    service = EmailService()
    template = "Hello {name}, your {missing_variable} is ready!"
    recipient = {'email': '<EMAIL>', 'name': 'John'}
    
    try:
        formatted = template.format(**recipient)
        print("   ✓ Template formatted successfully")
    except KeyError as e:
        print(f"   ❌ Missing variable: {e}")
        print("   Result: Bulk email will mark this recipient as failed")
    
    print("\n3. Invalid Email Addresses:")
    invalid_recipients = [
        {'name': 'No Email User'},  # Missing email
        {'email': 'invalid-email', 'name': 'Invalid Email'},
        {'email': '<EMAIL>', 'name': 'Valid User'}
    ]
    
    print("   Recipients:")
    for recipient in invalid_recipients:
        email = recipient.get('email', 'MISSING')
        name = recipient.get('name', 'UNKNOWN')
        status = '✓' if 'email' in recipient and '@' in recipient['email'] else '❌'
        print(f"      {status} {name}: {email}")


def demo_production_setup():
    """Show production setup recommendations"""
    print("\n🚀 Production Setup Recommendations")
    print("-" * 50)
    
    print("1. Environment Variables (.env file):")
    env_vars = [
        "MAIL_SERVER=smtp.gmail.com",
        "MAIL_PORT=587",
        "MAIL_USERNAME=<EMAIL>",
        "MAIL_PASSWORD=your-app-specific-password",
        "MAIL_DEFAULT_SENDER=<EMAIL>"
    ]
    
    for var in env_vars:
        print(f"   {var}")
    
    print("\n2. Gmail App Password Setup:")
    print("   • Enable 2-factor authentication on your Gmail account")
    print("   • Go to Google Account settings > Security > App passwords")
    print("   • Generate an app password for 'Mail'")
    print("   • Use this app password instead of your regular password")
    
    print("\n3. Alternative Email Providers:")
    providers = [
        ("Gmail", "smtp.gmail.com", "587", "Requires app password"),
        ("Outlook", "smtp-mail.outlook.com", "587", "Supports OAuth2"),
        ("SendGrid", "smtp.sendgrid.net", "587", "Professional email service"),
        ("Mailgun", "smtp.mailgun.org", "587", "Developer-friendly API")
    ]
    
    for name, server, port, note in providers:
        print(f"   • {name}: {server}:{port} - {note}")
    
    print("\n4. Security Best Practices:")
    practices = [
        "Never commit email credentials to version control",
        "Use environment variables for all sensitive configuration",
        "Consider using OAuth2 instead of passwords when possible",
        "Implement rate limiting for bulk email sending",
        "Monitor email delivery rates and bounces",
        "Use dedicated email service for high-volume sending"
    ]
    
    for practice in practices:
        print(f"   • {practice}")


def main():
    """Run the email service demo"""
    print("📧 BAUCH HR Management System - Email Service Demo")
    print("=" * 60)
    
    try:
        # Run all demo sections
        service = demo_email_configuration()
        en_templates, de_templates = demo_email_templates()
        formatted_email = demo_template_formatting()
        recipients = demo_bulk_email_preparation()
        demo_error_handling()
        demo_production_setup()
        
        print("\n" + "=" * 60)
        print("✅ Email Service Demo Completed Successfully!")
        print("\nNext Steps:")
        print("1. Run the test suite: python test_email_service.py")
        print("2. Configure your email settings in environment variables")
        print("3. Test with a small group before bulk sending")
        print("4. Integrate with your HR application")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        return False


if __name__ == '__main__':
    main()
