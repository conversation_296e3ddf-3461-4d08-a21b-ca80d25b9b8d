#!/usr/bin/env python3
"""
Test the new job-specific matcher with the CNC job to achieve the expected ranking:
1. <PERSON> (80%)
2. <PERSON><PERSON> (65%) 
3. <PERSON> (45%)
4. <PERSON> (35%)
"""

from job_specific_matcher import JobSpecificMatcher
from matcher import CVMatcher

def test_cnc_job_specific_ranking():
    """Test the job-specific matcher with CNC job and candidates"""
    print("=" * 60)
    print("TESTING JOB-SPECIFIC CNC RANKING")
    print("=" * 60)
    
    # CNC Job Description (from database)
    cnc_job_description = """
    Ihre Tätigkeit:
     Selbstständiges Programmieren, Rüsten und Einfahren der CNC-Maschinen sowie Behebung von
    Störungen.
     Erstbemusterung und Übergabe in die Serienproduktion (Klein-, und Mittelserien) mit Abstimmung
    der QS-Maßnahmen.
     Maschinenbetreuung und Prozessoptimierung.
     Perspektive / Aufstiegsmöglichkeit zur Schichtleitung mit Verantwortung für mehrere Maschinen und       
    Bediener/Werker.
    Ihr Profil:
     abgeschlossene Berufsausbildung in einem Metallverarbeitenden Beruf z.B. als
    Zerspanungsmechaniker/-in, Industriemechaniker/-in, Feinwerkmechaniker/-in.
     Berufserfahrung im Bereich der mechanischen Bearbeitung / Zerspanung.
     CNC-Programmierkenntnisse (z.B. Fanuc oder Heidenhain)
    Wir erwarten von Ihnen
     Begeisterung für Ihren Beruf als Facharbeiter
     Verantwortungsbewusstsein und persönliche/fachliche Kompetenz
     Gewissenhafte, präzises und selbständiges Arbeiten mit hohem Qualitätsbewusstsein
    """
    
    # Candidate CVs (from database)
    candidates = {
        "Daniel Meixner": """
        Daniel Meixner
        Sedlmayrstraße 14
        Tel.: 0172 / 6145619
        80634 München
        E-Mail: <EMAIL>
        
        Bewerbung als Qualitätsfachkraft / QS-Messtechniker
        
        In meiner aktuellen Position bei der Messner GmbH bin ich für die Serienbegleitende
        Qualitätskontrolle, die Erstellung von Prüfprotokollen und Erstmusterprüfberichten nach VDA
        sowie die Programmierung von CNC-Messmaschinen verantwortlich. Zudem bearbeite ich
        Reklamationen, führe interne Prozessaudits durch und überwache die Prüfmittel.
        
        Berufserfahrung:
        2019-heute: QS-Spezialist bei Messner GmbH
        - Serienbegleitende Qualitätskontrolle
        - CNC-Programmierung von Messmaschinen (Mitutoyo)
        - Erstmusterprüfberichten nach VDA
        - Prozessoptimierung und KVP
        - Reklamationsbearbeitung
        - Interne Prozessaudits
        
        2015-2019: Fertigungsmechaniker bei Audi
        - NC-Drehen und Fräsen
        - Maschinenbedienung und Rüsten
        - Qualitätskontrolle
        
        Ausbildung:
        2012-2015: Ausbildung Fertigungsmechaniker (IHK)
        """,
        
        "Horst Lippert": """
        Lippert Horst
        Richard-Strauss-Straße 38
        85057 Ingolstadt
        
        Bewerbung auf die Stelle als Qualitätsfachkraft QS / Messtechniker
        
        Als Flugzeugmechaniker habe ich Kenntnisse in der Reparatur und Instandhaltung von
        mechanischen, elektrischen und hydraulischen Systemen erworben. Außerdem bin ich als
        Flugzeugmechaniker Experte für das Durchführen von Strukturarbeiten an Flugzeugteilen,
        wie z.B. das Bearbeiten von Aluminium- und Kunststoffteilen (Bohren, Biegen).
        Darüber hinaus besitze ich als CNC-Fachkraft gute Kenntnisse im Drehen/ Fräsen.
        
        Berufserfahrung:
        2020-heute: Verkäufer bei Einzelhandel GmbH
        2015-2020: Bürokaufmann bei Verwaltung AG
        2010-2015: Metallfacharbeiter bei Maschinenbau Schmidt
        - CNC-Drehen und Fräsen
        - Werkzeugeinstellung
        - Strukturarbeiten
        
        1995-2010: Verschiedene Tätigkeiten im Vertrieb
        
        Ausbildung:
        1990-1993: Ausbildung Flugzeugmechaniker (IHK)
        Weiterbildung: CNC-Fachkraft Drehen/Fräsen (2010)
        """,
        
        "Pascal Baun": """
        PASCAL BAUN
        BEWERBUNG ALS QUALITÄTSFACHKRAFT QS / MESSTECHNIKER
        
        Westring 2
        84048 Mainburg
        Telefon: 0172/3621842
        E-Mail: <EMAIL>
        
        Derzeit befinde ich mich im letzten Jahr meiner Weiterbildung zum Maschinenbau-Techniker 
        an der Technikerschule Ingolstadt, die ich voraussichtlich am 23.07.2025 abschließen werde.
        
        Während meiner Ausbildung, sowie durch schulische Fächer wie Messtechnik oder Qualitäts-
        und Umweltmanagement habe ich bereits erste praktische Erfahrungen in diesem Bereich
        gesammelt.
        
        Berufserfahrung:
        2022-heute: Werkstudent bei Engineering Solutions
        - CAD-Konstruktion
        - Projektunterstützung
        
        2020-2022: Spritzguss-Einrichter bei Kunststoff GmbH
        - Maschineneinrichtung
        - Qualitätskontrolle
        - Spritzgussverfahren
        
        Ausbildung:
        2023-heute: Technikerschule Maschinenbau (laufend)
        2017-2020: Ausbildung Verfahrensmechaniker Kunststoff/Kautschuktechnik
        """,
        
        "Werner Stieger": """
        Werner Stieger
        
        BERUFSERFAHRUNG
        3D Messtechniker für KMGs sowie digitaler Messtechnik, messtronik GmbH, 2015-Heute
        ▪ Technische Leitung von Kundenaufträgen
        ▪ Definition und Koordination von Aufträgen unter Berücksichtigung von Kosten und Meilensteinen
        ▪ Erstellung von Messprogrammen für KMGs mit Quindos 7
        ▪ Individualschulungen für die Mitarbeiter unserer Kunden
        ▪ Produktionsbegleitende Qualitätssicherung
        ▪ Projektbezogene Betreuung nationaler als auch internationaler Kunden
        ▪ Digitalisierung & Auswertung mit Computertomograph
        ▪ Termingerechte Durchführung von Messaufgaben
        ▪ Erstellung von EMPBs und Prüfplänen nach VDA
        ▪ Statistische, analytische & grafische Auswertungen der Messergebnisse
        ▪ Co-Trainer für DGQ-Schulungen
        ▪ Anwendung der Geometrischen Produktspezifikationen nach ISO-GPS-System
        
        Rentenfachberater, ERGO Beratung und Vertrieb AG, Augsburg – 2013-2015
        ▪ Teilnahme an produktspezifischen Schulungen und Tests
        
        Ausbildung:
        2005-2010: Studium Geographie (Diplom)
        """,
        
        "Frank Reichelt": """
        Frank Reichelt
        Silberacker 10 A
        Telefon: 0152 / 56567354
        06343 Mansfeld
        Mail: <EMAIL>
        
        Bewerbung für Qualitätsfachkraft QS / Messtechniker
        
        Meine Tätigkeit war es die Überwachung der Produktion in einem Unternehmen für Stanzteile, 
        für die Herstellung von Automotiven. Die Teile werden vermessen und begutachtet, nach 
        Oberflächen und Beschnitt Punkten Eigenschaften, sowie nach Form und Lage Toleranzen.
        Auswertung von Statistiken über Q-Sat, sowie werden Programme für die Zeiss OPTISCHES 3D-
        Scanbox ATOS 5 geschrieben. Erstbemusterung, Messmittel Überwachung. SAP Grundkenntnisse.
        
        In den folgenden 12 Jahren sammelte ich Erfahrung in der Wartung, Instandsetzung, und
        Reparatur von Maschinen und Anlagen.
        
        Berufserfahrung:
        2018-heute: QS-Techniker bei Automotive Parts
        - Produktionsüberwachung Stanzteile
        - 3D-Scanbox ATOS 5 Programmierung
        - Erstbemusterung
        - Messmittelüberwachung
        - SAP Grundkenntnisse
        
        2006-2018: Wartung und Instandsetzung
        - Maschinenwartung
        - Reparaturarbeiten
        """
    }
    
    # Initialize job-specific matcher
    matcher = JobSpecificMatcher()
    
    print("=== JOB REQUIREMENTS ANALYSIS ===")
    job_requirements = matcher.extract_job_requirements(cnc_job_description)
    print(f"Extracted Skills ({len(job_requirements.skill_set)}):")
    for i, skill in enumerate(job_requirements.skill_set, 1):
        print(f"  {i:2d}. {skill}")
    print(f"\nMin Experience: {job_requirements.min_experience_years} years")
    print(f"Critical Certifications: {job_requirements.critical_certifications}")
    print()
    
    # Evaluate all candidates
    print("=== CANDIDATE EVALUATIONS ===")
    evaluations = []
    
    for name, cv_content in candidates.items():
        evaluation = matcher.evaluate_candidate(cv_content, job_requirements)
        evaluations.append(evaluation)
        
        print(f"\n{name}:")
        print(f"  Experience Points: {evaluation.experience_points}")
        print(f"  Education Points: {evaluation.education_points}")
        print(f"  Bonus Points: {evaluation.bonus_points}")
        print(f"  Total Score: {evaluation.total_score}")
        print(f"  Education: {evaluation.education_detected}")
        print(f"  Top Skills: {[k for k, v in evaluation.skill_breakdown.items() if v >= 2]}")
    
    # Rank candidates
    ranked_candidates = matcher.rank_candidates(evaluations)
    
    print("\n" + "=" * 60)
    print("FINAL RANKING")
    print("=" * 60)
    
    for i, evaluation in enumerate(ranked_candidates, 1):
        # Convert to percentage for display
        max_possible = len(job_requirements.skill_set) * 3 + 10 + 5
        percentage = (evaluation.total_score / max_possible) * 100 if max_possible > 0 else 0
        
        print(f"{i}. {evaluation.candidate_name}: {percentage:.1f}% ({evaluation.total_score}/{max_possible} points)")
        
        # Add analysis based on expected results
        if evaluation.candidate_name == "Daniel Meixner":
            print("   ✅ Metallverarbeitungsausbildung (Fertigungsmechaniker)")
            print("   ✅ CNC-Messprogrammierung, Serienfertigung, VDA-Kenntnisse")
            print("   ✅ Umfangreiche QS & Messtechnik Praxis")
            print("   ❗ Keine explizite Fanuc/Heidenhain Erwähnung")
        elif evaluation.candidate_name == "Horst Lippert":
            print("   ✅ Flugzeugmechaniker (IHK), Metallbearbeitung")
            print("   ✅ CNC-Fachkraft Kurs, Drehen/Fräsen")
            print("   ❗ Teilweise veraltet, wenig aktuelle Serienproduktion")
        elif evaluation.candidate_name == "Pascal Baun":
            print("   ✅ Maschinenbau-Techniker (fast abgeschlossen)")
            print("   ❗ Nur Ferienjobs, kaum CNC-/Metallpraxis")
        elif evaluation.candidate_name == "Werner Stieger":
            print("   ✅ Langjährige QS-/Messtechnik, VDA-Kenntnisse")
            print("   ❗ Kein CNC-Programmieren, eher Dokumentation")
    
    print("\n" + "=" * 60)
    print("EXPECTED vs ACTUAL RANKING")
    print("=" * 60)
    
    expected_order = ["Daniel Meixner", "Horst Lippert", "Pascal Baun", "Werner Stieger"]
    actual_order = [eval.candidate_name for eval in ranked_candidates]
    
    print("Expected: 1. Daniel Meixner, 2. Horst Lippert, 3. Pascal Baun, 4. Werner Stieger")
    print(f"Actual:   {', '.join([f'{i+1}. {name}' for i, name in enumerate(actual_order)])}")
    
    if actual_order == expected_order:
        print("✅ SUCCESS: Ranking matches expected results!")
    else:
        print("❌ MISMATCH: Ranking needs adjustment")
        
        # Show what needs to be fixed
        for i, expected_name in enumerate(expected_order):
            actual_pos = actual_order.index(expected_name) if expected_name in actual_order else -1
            if actual_pos != i:
                print(f"   {expected_name}: Expected #{i+1}, Got #{actual_pos+1}")

def test_integrated_matcher():
    """Test the integrated CVMatcher with job-specific matching"""
    print("\n" + "=" * 60)
    print("TESTING INTEGRATED CV MATCHER")
    print("=" * 60)
    
    # Test with the new integrated matcher
    cv_matcher = CVMatcher(use_job_specific=True, use_domain_specific=False, use_enhanced_matching=False)
    
    cnc_job_description = """
    CNC-Programmierer (m/w/d)
    - Selbstständiges Programmieren, Rüsten und Einfahren der CNC-Maschinen
    - Erstbemusterung und Übergabe in die Serienproduktion
    - Maschinenbetreuung und Prozessoptimierung
    - Ausbildung als Zerspanungsmechaniker/Industriemechaniker
    - CNC-Programmierkenntnisse (Fanuc oder Heidenhain)
    """
    
    daniel_cv = """
    Daniel Meixner - QS-Spezialist
    2019-heute: Serienbegleitende Qualitätskontrolle, CNC-Programmierung Messmaschinen,
    VDA-Berichte, Erstmusterprüfung, Prozessoptimierung
    2015-2019: Fertigungsmechaniker, NC-Drehen/Fräsen
    Ausbildung: Fertigungsmechaniker (IHK)
    """
    
    score = cv_matcher.calculate_match_score_from_content(cnc_job_description, daniel_cv)
    print(f"Daniel Meixner integrated score: {score:.1f}%")
    
    if score >= 75:
        print("✅ Integrated matcher working correctly!")
    else:
        print("❌ Integrated matcher needs adjustment")

if __name__ == "__main__":
    test_cnc_job_specific_ranking()
    test_integrated_matcher()
