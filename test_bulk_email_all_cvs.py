#!/usr/bin/env python3
"""
🚀 Test Bulk Email to All CVs
=============================
Send test emails to all CVs with valid email addresses
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import required modules
from hr_database_working import HRDatabase
from app_german import extract_email_from_cv_content
from email_service import EmailService

def test_bulk_email_to_all_cvs():
    """Send test emails to all CVs with valid email addresses"""
    print("🚀 Testing Bulk Email to All CVs")
    print("=" * 40)
    
    # Initialize database and email service
    hr_db = HRDatabase()
    email_service = EmailService(
        mail_server=os.environ.get('SMTP_HOST'),
        mail_port=int(os.environ.get('SMTP_PORT', 587)),
        mail_username=os.environ.get('SMTP_USERNAME'),
        mail_password=os.environ.get('SMTP_PASSWORD'),
        mail_use_tls=True,
        default_sender=os.environ.get('DEFAULT_SENDER_EMAIL')
    )
    
    # Get all jobs and CVs
    jobs = hr_db.get_all_jobs()
    all_recipients = []
    
    print("📋 Collecting recipients from all jobs...")
    
    for job in jobs:
        print(f"\n📋 Job: {job.title}")
        cvs = hr_db.get_cvs_for_job(job.title)
        
        for cv in cvs:
            email = extract_email_from_cv_content(cv.content)
            if email:
                recipient = {
                    'email': email,
                    'name': getattr(cv, 'candidate_name', 'Candidate'),
                    'job_title': job.title,
                    'cv_filename': cv.filename
                }
                all_recipients.append(recipient)
                print(f"   ✅ {recipient['name']} ({email}) - {cv.filename}")
            else:
                print(f"   ❌ {getattr(cv, 'candidate_name', 'Unknown')} - No email found in {cv.filename}")
    
    print(f"\n📊 Total recipients found: {len(all_recipients)}")
    
    if not all_recipients:
        print("❌ No recipients found!")
        return
    
    # Prepare email content
    subject = "🎉 BAUCH HR System - Bulk Email Test"
    template = """
    <html>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #2c5aa0;">🎉 BAUCH HR Management System</h2>
            <h3 style="color: #28a745;">Bulk Email Test - SUCCESS!</h3>
            
            <p>Dear {name},</p>
            
            <p>This is a test email from the BAUCH HR Management System to verify that bulk email functionality is working correctly.</p>
            
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h4 style="margin-top: 0; color: #495057;">📋 Your Application Details:</h4>
                <ul style="margin-bottom: 0;">
                    <li><strong>Job Position:</strong> {job_title}</li>
                    <li><strong>CV File:</strong> {cv_filename}</li>
                    <li><strong>Email:</strong> {email}</li>
                    <li><strong>Status:</strong> Application received and under review</li>
                </ul>
            </div>
            
            <p><strong>✅ Email System Status:</strong> Fully operational!</p>
            
            <p>This confirms that the BAUCH HR system can successfully send emails to all applicants with valid email addresses.</p>
            
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #dee2e6;">
            
            <p style="color: #6c757d; font-size: 14px;">
                Best regards,<br>
                <strong>BAUCH HR Team</strong><br>
                <em>Professional HR Management System</em>
            </p>
        </div>
    </body>
    </html>
    """
    
    print(f"\n📤 Sending emails to {len(all_recipients)} recipients...")
    print("-" * 50)
    
    # Send bulk emails
    try:
        result = email_service.send_bulk_emails(
            recipients=all_recipients,
            subject=subject,
            template=template,
            async_send=False  # Send synchronously to see results immediately
        )
        
        print(f"\n🎉 Bulk Email Results:")
        print(f"   ✅ Successfully sent: {result['success']}")
        print(f"   ❌ Failed: {result['failed']}")
        print(f"   📊 Total attempted: {result['success'] + result['failed']}")
        
        # Show detailed results
        if 'recipients' in result:
            print(f"\n📋 Detailed Results:")
            for recipient_result in result['recipients']:
                status_icon = "✅" if recipient_result['status'] == 'sent' else "❌"
                print(f"   {status_icon} {recipient_result['email']} - {recipient_result['status']}")
                if 'error' in recipient_result:
                    print(f"      Error: {recipient_result['error']}")
        
        if result['success'] > 0:
            print(f"\n🎯 Check your inboxes! {result['success']} emails were sent successfully.")
            print("   This proves the bulk email system is working for all CVs with valid emails.")
        
        return result['success'] > 0
        
    except Exception as e:
        print(f"❌ Error sending bulk emails: {e}")
        return False

def main():
    """Main function"""
    success = test_bulk_email_to_all_cvs()
    
    if success:
        print("\n🎉 CONCLUSION: Bulk email system is working!")
        print("   Emails are being sent to ALL CVs with valid email addresses.")
        print("   If you're only seeing emails for 'taha', check:")
        print("   1. Are you selecting all CVs in the email interface?")
        print("   2. Are other email addresses going to spam folders?")
        print("   3. Are you checking the correct email accounts?")
    else:
        print("\n❌ Bulk email system needs debugging")

if __name__ == "__main__":
    main()
