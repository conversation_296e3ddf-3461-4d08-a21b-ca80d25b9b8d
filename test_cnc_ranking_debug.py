#!/usr/bin/env python3
"""
Debug CNC ranking to understand why candidates are ranked incorrectly
"""

from hr_database_working import HRDatabase
from matcher import <PERSON><PERSON><PERSON><PERSON><PERSON>

def debug_cnc_ranking():
    """Debug the CNC ranking issue"""
    print("=== DEBUGGING CNC RANKING ISSUE ===")
    
    try:
        db = HRDatabase()
        matcher = CVMatcher(use_enhanced_matching=False, use_domain_specific=False, use_job_specific=False)

        # Get CNC job and CVs
        cnc_job = db.get_job_by_title('C<PERSON> Fräser')
        if not cnc_job:
            print("CNC job not found!")
            return

        cvs = db.get_cvs_for_job('C<PERSON> Fräser')
        print(f"Found {len(cvs)} CVs for CNC job")
        print(f"Job Description: {cnc_job.description[:200]}...")
        print()

        # Analyze each CV in detail
        results = []
        for cv in cvs:
            try:
                # Get detailed breakdown
                explanation = matcher.get_match_explanation(cnc_job.description, cv.content)
                
                # Calculate individual scores
                tf_idf_score = matcher.calculate_tf_idf_similarity(cnc_job.description, cv.content)
                keyword_score = matcher.calculate_keyword_match(cnc_job.description, cv.content)
                skill_score = matcher.calculate_skill_match(cnc_job.description, cv.content)
                
                # Calculate overall score with manufacturing weights
                overall_score = (skill_score * 0.6) + (keyword_score * 0.25) + (tf_idf_score * 0.15)
                
                results.append({
                    'name': cv.candidate_name or 'Unknown',
                    'overall': overall_score * 100,
                    'skill': skill_score * 100,
                    'keyword': keyword_score * 100,
                    'tfidf': tf_idf_score * 100,
                    'cv_content': cv.content,
                    'cv_preview': cv.content[:500] + "..." if len(cv.content) > 500 else cv.content
                })
                
            except Exception as e:
                print(f"Error processing CV {cv.candidate_name}: {e}")

        # Sort by overall score
        results.sort(key=lambda x: x['overall'], reverse=True)
        
        print("=== CURRENT RANKING (PROBLEMATIC) ===")
        for i, result in enumerate(results, 1):
            print(f"\n{i}. {result['name']}")
            print(f"   Overall: {result['overall']:.1f}% (Skill: {result['skill']:.1f}%, Keyword: {result['keyword']:.1f}%, TF-IDF: {result['tfidf']:.1f}%)")
            print(f"   CV Preview: {result['cv_preview'][:200]}...")

            # Analyze why this candidate got this score
            print(f"   Analysis:")
            cv_lower = result['cv_content'].lower()

            if 'cnc' in cv_lower:
                cnc_contexts = []
                lines = result['cv_content'].split('\n')
                for line in lines:
                    if 'cnc' in line.lower():
                        cnc_contexts.append(line.strip())
                print(f"     ✓ Has CNC mention: {cnc_contexts[:2]}")
            else:
                print(f"     ✗ No CNC mention")

            if any(term in cv_lower for term in ['programmierung', 'programmieren']):
                print(f"     ✓ Has programming mention")
            else:
                print(f"     ✗ No programming mention")

            if any(term in cv_lower for term in ['fanuc', 'heidenhain']):
                print(f"     ✓ Has CNC controller mention")
            else:
                print(f"     ✗ No CNC controller mention")

            if any(term in cv_lower for term in ['zerspanung', 'fräsen', 'drehen']):
                machining_contexts = []
                lines = result['cv_content'].split('\n')
                for line in lines:
                    if any(term in line.lower() for term in ['zerspanung', 'fräsen', 'drehen']):
                        machining_contexts.append(line.strip())
                print(f"     ✓ Has machining mention: {machining_contexts[:2]}")
            else:
                print(f"     ✗ No machining mention")

            # Check for quality control mentions
            if any(term in cv_lower for term in ['qualität', 'messtechnik', 'qs']):
                print(f"     ✓ Has quality control mention")
            else:
                print(f"     ✗ No quality control mention")

        print("\n=== ANALYSIS ===")
        print("Based on the CV content analysis:")
        print("1. Daniel Meixner: HAS CNC experience (CNC-Messprogrammierung, NC-Drehen/Fräsen)")
        print("2. Horst Lippert: HAS CNC experience (CNC-Fachkraft, Drehen/Fräsen)")
        print("3. Werner Stieger: NO CNC experience (only 3D measurement)")
        print("4. Pascal Baun: NO CNC experience (only QS/measurement)")
        print("5. Frank Reichelt: Unclear CNC experience")
        print()
        print("CORRECTED EXPECTED RANKING:")
        print("1. Horst Lippert (direct CNC machining experience)")
        print("2. Daniel Meixner (CNC measurement programming - related but not direct machining)")
        print("3. Frank Reichelt (unclear experience)")
        print("4. Werner Stieger (QS but no CNC)")
        print("5. Pascal Baun (no CNC experience)")

    except Exception as e:
        print(f"Error in debug: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_cnc_ranking()
