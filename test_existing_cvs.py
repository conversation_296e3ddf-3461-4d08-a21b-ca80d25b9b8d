#!/usr/bin/env python3
"""
Test CV Matching with Existing CV Files
Tests the matcher with actual PDF files in the uploads directory
"""

import os
import glob
from matcher import CVMatcher
from cv_extractor import CVDataExtractor

def test_existing_cvs():
    """Test with existing CV files in uploads directory"""
    print("🚀 TESTING WITH EXISTING CV FILES")
    print("=" * 50)
    
    # Get all PDF files in uploads directory
    cv_files = glob.glob("uploads/*.pdf")
    
    if not cv_files:
        print("❌ No PDF files found in uploads directory")
        return
    
    print(f"📁 Found {len(cv_files)} CV files:")
    for cv_file in cv_files:
        print(f"   - {os.path.basename(cv_file)}")
    print()
    
    # Job descriptions for testing
    job_descriptions = {
        'Java Developer (German)': """
        Java Softwareentwickler (m/w/d)
        
        Anforderungen:
        • Mindestens 3 Jahre Berufserfahrung in der Java-Entwicklung
        • Erfahrung mit Spring Boot Framework
        • Kenntnisse in PostgreSQL-Datenbanken
        • Entwicklung von REST-Services
        • Git und Jenkins für CI/CD
        • Agile Scrum-Methoden
        • Abgeschlossenes Studium der Informatik
        • Deutsch- und Englischkenntnisse
        """,
        
        'Java Developer (English)': """
        Senior Java Developer Position
        
        Requirements:
        • 3+ years Java development experience
        • Spring Boot framework expertise
        • PostgreSQL database experience
        • REST API development
        • Git, Jenkins, CI/CD experience
        • Scrum methodology
        • Computer Science degree
        • English and German language skills
        """,
        
        'System Administrator': """
        System Administrator Position
        
        Requirements:
        • Windows Server administration
        • Active Directory management
        • Network configuration
        • IT support experience
        • ITIL knowledge
        • 2+ years experience
        • Technical certification preferred
        """
    }
    
    matcher = CVMatcher()
    extractor = CVDataExtractor()
    
    # Test each job description
    for job_title, job_desc in job_descriptions.items():
        print(f"💼 Testing Job: {job_title}")
        print("-" * 40)
        
        results = []
        
        for cv_file in cv_files:
            try:
                # Extract CV content
                cv_content = extractor.extract_text_from_file(cv_file)
                if not cv_content:
                    print(f"⚠️  Could not extract content from {os.path.basename(cv_file)}")
                    continue
                
                # Calculate scores
                tf_idf_score = matcher.calculate_tf_idf_similarity(job_desc, cv_content)
                keyword_score = matcher.calculate_keyword_match(job_desc, cv_content)
                skill_score = matcher.calculate_skill_match(job_desc, cv_content)
                
                # Overall score
                overall_score = (skill_score * 0.5) + (keyword_score * 0.3) + (tf_idf_score * 0.2)
                
                results.append({
                    'file': os.path.basename(cv_file),
                    'overall': overall_score * 100,
                    'skill': skill_score * 100,
                    'keyword': keyword_score * 100,
                    'tfidf': tf_idf_score * 100,
                    'content_preview': cv_content[:200] + "..." if len(cv_content) > 200 else cv_content
                })
                
            except Exception as e:
                print(f"❌ Error processing {os.path.basename(cv_file)}: {e}")
                continue
        
        # Sort by overall score
        results.sort(key=lambda x: x['overall'], reverse=True)
        
        # Display results
        print(f"📊 Results (sorted by overall score):")
        for i, result in enumerate(results, 1):
            print(f"{i}. {result['file']}: {result['overall']:.1f}%")
            print(f"   Skills: {result['skill']:.1f}% | "
                  f"Keywords: {result['keyword']:.1f}% | "
                  f"Content: {result['tfidf']:.1f}%")
        
        print()

def test_detailed_analysis():
    """Detailed analysis of specific CV files"""
    print("🔍 DETAILED ANALYSIS OF SPECIFIC CVS")
    print("=" * 45)
    
    # Focus on Maria and Max CVs if they exist
    target_files = [
        "uploads/CV_Maria_Schmidt_80.pdf",
        "uploads/CV_Max_Mueller_20.pdf"
    ]
    
    java_job = """
    Java Software Developer Position
    
    Requirements:
    - Java programming (3+ years)
    - Spring Boot framework
    - PostgreSQL database
    - REST API development
    - Git, Jenkins, CI/CD
    - Scrum methodology
    - Bachelor's degree in Computer Science
    - German and English language skills
    """
    
    matcher = CVMatcher()
    extractor = CVDataExtractor()
    
    for cv_file in target_files:
        if not os.path.exists(cv_file):
            print(f"❌ {cv_file} not found")
            continue
        
        print(f"📄 Analyzing: {os.path.basename(cv_file)}")
        print("-" * 30)
        
        try:
            cv_content = extractor.extract_text_from_file(cv_file)
            if not cv_content:
                print(f"❌ Could not extract content")
                continue
            
            # Show content preview
            print(f"Content preview:")
            print(f"   {cv_content[:300]}...")
            print()
            
            # Calculate detailed scores
            tf_idf = matcher.calculate_tf_idf_similarity(java_job, cv_content)
            keyword = matcher.calculate_keyword_match(java_job, cv_content)
            skill = matcher.calculate_skill_match(java_job, cv_content)
            overall = (skill * 0.5) + (keyword * 0.3) + (tf_idf * 0.2)
            
            print(f"Detailed Scores:")
            print(f"   Overall Score: {overall*100:.2f}%")
            print(f"   Skill Match: {skill*100:.2f}%")
            print(f"   Keyword Match: {keyword*100:.2f}%")
            print(f"   Content Similarity: {tf_idf*100:.2f}%")
            
            # Get explanation
            explanation = matcher.get_match_explanation(java_job, cv_content)
            print(f"   Common Keywords: {', '.join(explanation['common_keywords'][:5])}")
            print()
            
        except Exception as e:
            print(f"❌ Error analyzing {cv_file}: {e}")
            print()

def test_multilingual_capabilities():
    """Test multilingual capabilities with existing CVs"""
    print("🌍 MULTILINGUAL CAPABILITIES TEST")
    print("=" * 40)
    
    # German and English versions of the same job
    jobs = {
        'German Job': """
        Java Entwickler (m/w/d)
        
        Wir suchen einen erfahrenen Java-Entwickler.
        
        Anforderungen:
        • Java-Programmierung (3+ Jahre)
        • Spring Boot Framework
        • PostgreSQL Datenbank
        • REST-API Entwicklung
        • Git, Jenkins, CI/CD
        • Scrum Methodik
        • Informatik Studium
        """,
        
        'English Job': """
        Java Developer Position
        
        We are looking for an experienced Java developer.
        
        Requirements:
        • Java programming (3+ years)
        • Spring Boot framework
        • PostgreSQL database
        • REST API development
        • Git, Jenkins, CI/CD
        • Scrum methodology
        • Computer Science degree
        """
    }
    
    cv_files = glob.glob("uploads/*.pdf")
    matcher = CVMatcher()
    extractor = CVDataExtractor()
    
    for cv_file in cv_files[:3]:  # Test first 3 CVs
        print(f"📄 Testing: {os.path.basename(cv_file)}")
        
        try:
            cv_content = extractor.extract_text_from_file(cv_file)
            if not cv_content:
                continue
            
            for job_name, job_desc in jobs.items():
                # Calculate scores manually since calculate_match_score expects file path
                tf_idf_score = matcher.calculate_tf_idf_similarity(job_desc, cv_content)
                keyword_score = matcher.calculate_keyword_match(job_desc, cv_content)
                skill_score = matcher.calculate_skill_match(job_desc, cv_content)
                overall_score = (skill_score * 0.5) + (keyword_score * 0.3) + (tf_idf_score * 0.2)
                print(f"   {job_name}: {overall_score*100:.1f}%")
            
            print()
            
        except Exception as e:
            print(f"   Error: {e}")
            print()

def main():
    """Run all tests with existing CV files"""
    test_existing_cvs()
    test_detailed_analysis()
    test_multilingual_capabilities()
    
    print("✅ Testing with existing CV files completed!")

if __name__ == "__main__":
    main()
