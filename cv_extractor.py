import re
import os
from typing import Dict, List
import fitz  # PyMuPDF
from docx import Document


class CVDataExtractor:
    def __init__(self):
        # Common skill keywords
        self.skill_keywords = [
            # Programming languages
            'Python', 'Java', 'JavaScript', 'C++', 'C#', 'P<PERSON>', '<PERSON>', 'Go', 'Rust',
            'Type<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>ala', 'R', 'MATLAB', 'SQL',
            
            # Web technologies
            'HTML', 'CSS', 'React', 'Angular', 'Vue.js', 'Node.js', 'Express',
            'Django', 'Flask', 'Spring', 'Laravel', 'Bootstrap', 'jQuery',
            
            # Databases
            'MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'SQLite', 'Oracle',
            
            # Tools and platforms
            'Git', 'Docker', 'Kubernetes', 'AWS', 'Azure', 'Linux', 'Windows',
            'Jenkins', '<PERSON>ra', 'Confluence', 'Slack', 'Teams',
            
            # Methodologies
            'Agile', '<PERSON>rum', 'Kanban', 'DevOps', 'CI/CD', 'TDD', 'BDD',
            
            # Soft skills
            'Leadership', 'Communication', 'Teamwork', 'Problem Solving',
            'Project Management', 'Time Management', 'Critical Thinking'
        ]
        
        # Education keywords
        self.education_keywords = [
            'university', 'college', 'bachelor', 'master', 'phd', 'doctorate',
            'degree', 'diploma', 'certificate', 'education', 'school',
            'graduated', 'gpa', 'honors', 'magna cum laude', 'summa cum laude'
        ]

    def extract_text_from_pdf(self, file_path: str) -> str:
        """Extract text from PDF file"""
        try:
            doc = fitz.open(file_path)
            text = ""
            for page in doc:
                text += page.get_text()
            doc.close()
            return text
        except Exception as e:
            print(f"Error extracting text from PDF: {e}")
            return ""

    def extract_text_from_docx(self, file_path: str) -> str:
        """Extract text from DOCX file"""
        try:
            doc = Document(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text
        except Exception as e:
            print(f"Error extracting text from DOCX: {e}")
            return ""

    def extract_text_from_file(self, file_path: str) -> str:
        """Extract text from file based on extension"""
        if file_path.lower().endswith('.pdf'):
            return self.extract_text_from_pdf(file_path)
        elif file_path.lower().endswith('.docx'):
            return self.extract_text_from_docx(file_path)
        else:
            return ""

    def extract_name(self, text: str, filename: str = "") -> str:
        """Extract name from CV text"""
        # Try to find name patterns
        name_patterns = [
            r'\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)+)\b',  # Capitalized names
            r'Name:\s*([A-Z][a-z]+(?:\s+[A-Z][a-z]+)+)',
            r'Full Name:\s*([A-Z][a-z]+(?:\s+[A-Z][a-z]+)+)'
        ]
        
        for pattern in name_patterns:
            matches = re.findall(pattern, text)
            if matches:
                # Filter out common words that might be matched
                exclude_words = ['Dear Sir', 'Dear Madam', 'Yours Sincerely', 'Best Regards']
                for match in matches:
                    if not any(exclude in match for exclude in exclude_words):
                        return match.strip()
        
        # Fallback: try to extract from filename
        if filename:
            name_from_file = re.sub(r'[_\-\.]', ' ', os.path.splitext(filename)[0])
            name_from_file = re.sub(r'\b(cv|resume)\b', '', name_from_file, flags=re.IGNORECASE)
            if name_from_file.strip():
                return name_from_file.strip().title()
        
        return "Name not found"

    def extract_email(self, text: str) -> str:
        """Extract email address from CV text"""
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        matches = re.findall(email_pattern, text)
        return matches[0] if matches else "Email not found"

    def extract_phone(self, text: str) -> str:
        """Extract phone number from CV text"""
        phone_patterns = [
            r'\+?\d{1,4}[\s\-\(\)]?\d{3,4}[\s\-\(\)]?\d{3,4}[\s\-]?\d{3,4}',
            r'\(\d{3}\)\s?\d{3}[\s\-]?\d{4}',
            r'\d{3}[\s\-]\d{3}[\s\-]\d{4}'
        ]
        
        for pattern in phone_patterns:
            matches = re.findall(pattern, text)
            if matches:
                return matches[0]
        
        return "Phone not found"

    def extract_experience(self, text: str) -> str:
        """Extract work experience from CV text"""
        # Look for experience patterns
        experience_patterns = [
            r'(\d+)\+?\s*years?\s*(?:of\s*)?experience',
            r'(\d+)-(\d+)\s*years?\s*(?:of\s*)?experience',
            r'experience\s*:?\s*(\d+)\+?\s*years?',
            r'(\d+)\+?\s*years?\s*in\s*\w+'
        ]
        
        text_lower = text.lower()
        for pattern in experience_patterns:
            matches = re.findall(pattern, text_lower)
            if matches:
                if isinstance(matches[0], tuple):
                    # Range pattern (e.g., "2-4 years")
                    return f"{matches[0][0]}-{matches[0][1]} years"
                else:
                    # Single number pattern
                    return f"{matches[0]} years"
        
        # Check for experience level keywords
        if any(keyword in text_lower for keyword in ['senior', 'lead', 'manager', 'director']):
            return "Senior level (5+ years)"
        elif any(keyword in text_lower for keyword in ['mid-level', 'intermediate', 'experienced']):
            return "Mid-level (2-5 years)"
        elif any(keyword in text_lower for keyword in ['junior', 'entry', 'graduate', 'intern']):
            return "Junior level (0-2 years)"
        
        return "Experience not specified"

    def extract_skills(self, text: str) -> str:
        """Extract skills from CV text"""
        text_lower = text.lower()
        found_skills = []
        
        for skill in self.skill_keywords:
            if skill.lower() in text_lower:
                found_skills.append(skill.title())
        
        if found_skills:
            return ", ".join(found_skills[:10])  # Limit to top 10 skills
        return "Skills not specified"

    def extract_education(self, text: str) -> str:
        """Extract education information from CV text"""
        text_lower = text.lower()
        education_info = []
        
        # Look for education institutions
        lines = text.split('\n')
        for line in lines:
            line_lower = line.lower()
            if any(keyword in line_lower for keyword in self.education_keywords):
                # Clean and add the line if it looks like education info
                clean_line = line.strip()
                if clean_line and len(clean_line) > 5:
                    education_info.append(clean_line)
        
        if education_info:
            return "; ".join(education_info[:3])  # Limit to top 3 entries
        return "Education not specified"

    def extract_cv_data(self, file_path: str, fields: List[str]) -> Dict[str, str]:
        """Extract specified fields from a CV file"""
        # Extract text based on file type
        text = self.extract_text_from_file(file_path)
        if not text:
            return {"error": "Could not extract text from file"}
        
        filename = os.path.basename(file_path)
        data = {}
        
        if 'name' in fields:
            data['name'] = self.extract_name(text, filename)
        if 'email' in fields:
            data['email'] = self.extract_email(text)
        if 'phone' in fields:
            data['phone'] = self.extract_phone(text)
        if 'experience' in fields:
            data['experience'] = self.extract_experience(text)
        if 'skills' in fields:
            data['skills'] = self.extract_skills(text)
        if 'education' in fields:
            data['education'] = self.extract_education(text)
        
        return data
