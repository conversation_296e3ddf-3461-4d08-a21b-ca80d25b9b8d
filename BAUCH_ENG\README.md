# BAUCH HR Management System

A comprehensive HR management system for job posting, CV collection, candidate matching, and data extraction.

## Features

- **Job Management**: Create, edit, and manage job postings
- **CV Collection**: Upload and organize candidate CVs
- **Smart Matching**: AI-powered CV-to-job matching
- **Data Extraction**: Extract candidate information to Excel
- **User Authentication**: Secure login system
- **Responsive Design**: Works on desktop and mobile

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <your-repository-url>
cd BAUCH_ENG

# Install dependencies
pip install -r requirements.txt

# Create admin user
python create_admin_user.py

# Run the application
python app.py
```

### 2. Access the Application

- Open your browser and go to `http://localhost:5000`
- Login with:
  - **Username**: `admin`
  - **Password**: `admin123`

⚠️ **Important**: Change the default password after first login!

## Deployment on Uberspace

### 1. Upload Files

```bash
# On your local machine
git push origin main

# On Uberspace server
git clone <your-repository-url>
cd BAUCH_ENG
```

### 2. Install Dependencies

```bash
# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install requirements
pip install -r requirements.txt
```

### 3. Create Admin User

```bash
python create_admin_user.py
```

### 4. Configure for Production

Create a `.env` file:
```bash
FLASK_ENV=production
SECRET_KEY=your-super-secret-key-here
```

### 5. Run with Gunicorn

```bash
# Install gunicorn (already in requirements.txt)
pip install gunicorn

# Run the application
gunicorn -w 4 -b 0.0.0.0:8000 app:app
```

### 6. Set up Reverse Proxy (Uberspace)

Create a `.htaccess` file in your web directory:
```apache
RewriteEngine On
RewriteRule ^(.*)$ http://localhost:8000/$1 [P,L]
```

## File Structure

```
BAUCH_ENG/
├── app.py                 # Main Flask application
├── hr_database.py         # Database models and operations
├── cv_extractor.py        # CV data extraction logic
├── matcher.py             # CV-job matching algorithms
├── create_admin_user.py   # Admin user creation script
├── requirements.txt       # Python dependencies
├── README.md             # This file
├── static/               # CSS, JS, images
│   ├── css/
│   ├── js/
│   └── img/
├── templates/            # HTML templates
├── uploads/              # CV file storage
└── hr_database.db        # SQLite database (created automatically)
```

## Configuration

### Environment Variables

- `FLASK_ENV`: Set to `production` for production deployment
- `SECRET_KEY`: Change this to a secure random string
- `DATABASE_URL`: Optional custom database URL

### Security Notes

1. **Change default password** immediately after first login
2. **Set a secure SECRET_KEY** in production
3. **Use HTTPS** in production
4. **Regularly backup** the database and uploaded files

## Usage

### Adding Jobs

1. Login to the system
2. Go to "Jobs" → "Add New Job"
3. Fill in job details and responsible persons
4. Save the job

### Uploading CVs

1. Go to "CVs" → "Upload CV"
2. Select the job position
3. Enter candidate name
4. Upload PDF or DOCX file

### Extracting Data to Excel

1. Go to "Jobs" and select a job
2. Click "Extract to Excel"
3. Choose the fields to extract
4. Download the generated Excel file

### CV Matching

1. Go to "Match CVs"
2. Select a job
3. View match scores and rankings

## Troubleshooting

### Common Issues

1. **Database errors**: Delete `hr_database.db` and run `create_admin_user.py` again
2. **File upload errors**: Check that `uploads/` directory exists and is writable
3. **Template errors**: Ensure all files in `templates/` directory are present

### Logs

Check the console output for error messages and debugging information.

## Support

For support and questions, contact the BAUCH development team.

## License

© 2024 BAUCH Group. All rights reserved.
