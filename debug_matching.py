#!/usr/bin/env python3
"""
Debug CV Matching Algorithm
Analyze why Maria CV should score higher than Max CV
"""

import os
import sys
from matcher import CVMatcher
from cv_extractor import CVDataExtractor
import j<PERSON>


def extract_cv_content(cv_path):
    """Extract content from CV file"""
    extractor = CVDataExtractor()
    return extractor.extract_text_from_file(cv_path)


def analyze_cv_matching(job_description, cv_files):
    """Detailed analysis of CV matching scores"""
    print("🔍 CV Matching Analysis")
    print("=" * 60)
    
    matcher = CVMatcher()
    
    print(f"📋 Job Description:")
    print(f"   {job_description[:200]}...")
    print()
    
    results = []
    
    for cv_file in cv_files:
        if not os.path.exists(cv_file):
            print(f"❌ File not found: {cv_file}")
            continue
            
        print(f"📄 Analyzing: {os.path.basename(cv_file)}")
        print("-" * 40)
        
        # Extract CV content
        cv_content = extract_cv_content(cv_file)
        if not cv_content:
            print("❌ Could not extract content from CV")
            continue
            
        print(f"📝 CV Content Preview:")
        print(f"   {cv_content[:200]}...")
        print()
        
        # Calculate individual scores
        tf_idf_score = matcher.calculate_tf_idf_similarity(job_description, cv_content)
        keyword_score = matcher.calculate_keyword_match(job_description, cv_content)
        skill_score = matcher.calculate_skill_match(job_description, cv_content)
        
        # Calculate overall score
        overall_score = (tf_idf_score * 0.4) + (keyword_score * 0.3) + (skill_score * 0.3)
        
        print(f"📊 Scoring Breakdown:")
        print(f"   TF-IDF Similarity:  {tf_idf_score:.4f} (40% weight)")
        print(f"   Keyword Match:      {keyword_score:.4f} (30% weight)")
        print(f"   Skill Match:        {skill_score:.4f} (30% weight)")
        print(f"   Overall Score:      {overall_score:.4f} ({overall_score*100:.1f}%)")
        print()
        
        # Get detailed explanation
        explanation = matcher.get_match_explanation(job_description, cv_content)
        
        print(f"🔍 Match Details:")
        print(f"   Common Keywords: {len(explanation.get('common_keywords', []))} words")
        print(f"   Keywords: {list(explanation.get('common_keywords', []))[:10]}")
        print(f"   Matched Skills: {explanation.get('matched_skills', [])}")
        print()
        
        results.append({
            'file': os.path.basename(cv_file),
            'tf_idf': tf_idf_score,
            'keyword': keyword_score,
            'skill': skill_score,
            'overall': overall_score,
            'percentage': overall_score * 100,
            'explanation': explanation
        })
        
        print("=" * 60)
    
    # Sort results by overall score
    results.sort(key=lambda x: x['overall'], reverse=True)
    
    print("🏆 FINAL RANKING:")
    print("-" * 30)
    for i, result in enumerate(results, 1):
        print(f"{i}. {result['file']}: {result['percentage']:.1f}%")
    
    return results


def analyze_specific_issue():
    """Analyze the specific Maria vs Max issue"""
    print("\n🎯 Specific Analysis: Maria vs Max")
    print("=" * 50)
    
    # Check if the specific CV files exist
    maria_cv = "uploads/CV_Maria_Schmidt_80.pdf"
    max_cv = "uploads/CV_Max_Mueller_20.pdf"
    
    if not os.path.exists(maria_cv):
        print(f"❌ Maria's CV not found: {maria_cv}")
        return
        
    if not os.path.exists(max_cv):
        print(f"❌ Max's CV not found: {max_cv}")
        return
    
    # Sample job description (you can modify this)
    job_description = """
    Software Developer Position
    We are looking for an experienced software developer with strong skills in:
    - Python programming
    - Web development
    - Database management
    - Team collaboration
    - Project management
    
    Requirements:
    - 3+ years of experience
    - Bachelor's degree in Computer Science or related field
    - Experience with modern web frameworks
    - Strong problem-solving skills
    """
    
    print("📋 Using sample job description for analysis...")
    print()
    
    # Analyze both CVs
    results = analyze_cv_matching(job_description, [maria_cv, max_cv])
    
    if len(results) >= 2:
        maria_result = next((r for r in results if 'Maria' in r['file']), None)
        max_result = next((r for r in results if 'Max' in r['file']), None)
        
        if maria_result and max_result:
            print("\n🔍 COMPARISON ANALYSIS:")
            print("-" * 40)
            print(f"Maria's Score: {maria_result['percentage']:.1f}%")
            print(f"Max's Score:   {max_result['percentage']:.1f}%")
            print(f"Difference:    {maria_result['percentage'] - max_result['percentage']:.1f}%")
            
            if maria_result['overall'] > max_result['overall']:
                print("✅ Maria correctly scores higher than Max")
            else:
                print("❌ ISSUE: Max scores higher than Maria")
                print("\n🔧 POTENTIAL ISSUES:")
                print("1. CV content extraction problems")
                print("2. Keyword matching algorithm bias")
                print("3. Skill detection issues")
                print("4. TF-IDF calculation problems")


def test_with_custom_job():
    """Test with a custom job description"""
    print("\n📝 Custom Job Description Test")
    print("=" * 40)
    
    job_description = input("Enter job description (or press Enter for default): ").strip()
    
    if not job_description:
        job_description = """
        Senior Python Developer
        Looking for a senior Python developer with experience in web development,
        database design, and team leadership. Must have 5+ years experience.
        """
    
    cv_files = [
        "uploads/CV_Maria_Schmidt_80.pdf",
        "uploads/CV_Max_Mueller_20.pdf"
    ]
    
    # Filter existing files
    existing_files = [f for f in cv_files if os.path.exists(f)]
    
    if not existing_files:
        print("❌ No CV files found in uploads directory")
        return
    
    analyze_cv_matching(job_description, existing_files)


def main():
    """Main function"""
    print("🚀 CV Matching Debugger")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("matcher.py"):
        print("❌ Please run this script from the project root directory")
        return
    
    # Check if uploads directory exists
    if not os.path.exists("uploads"):
        print("❌ Uploads directory not found")
        return
    
    print("Available options:")
    print("1. Analyze specific Maria vs Max issue")
    print("2. Test with custom job description")
    print("3. Analyze all CVs in uploads directory")
    
    choice = input("\nSelect option (1-3): ").strip()
    
    if choice == "1":
        analyze_specific_issue()
    elif choice == "2":
        test_with_custom_job()
    elif choice == "3":
        # Get all PDF files in uploads
        cv_files = [f"uploads/{f}" for f in os.listdir("uploads") if f.endswith('.pdf')]
        if cv_files:
            job_desc = "Software developer with Python and web development experience"
            analyze_cv_matching(job_desc, cv_files)
        else:
            print("❌ No PDF files found in uploads directory")
    else:
        print("❌ Invalid option")


if __name__ == "__main__":
    main()
