#!/usr/bin/env python3
"""
Test Accurate CV Matching with Real Job Requirements
Based on the specific Java/Spring Boot job requirements
"""

import os
from matcher import CVMatcher
from cv_extractor import CVDataExtractor


def test_java_spring_job():
    """Test with the specific Java/Spring Boot job requirements"""
    
    # Exact job description based on requirements
    job_description = """
    Software Developer Position - Java/Spring Boot
    
    We are looking for an experienced Java developer with the following requirements:
    
    Required Skills:
    - Java programming (3+ years experience)
    - Spring Boot framework
    - PostgreSQL database
    - REST API development
    - Git version control
    - CI/CD pipelines (<PERSON> preferred)
    - Scrum methodology
    
    Education:
    - Bachelor's degree in Computer Science or equivalent
    
    Experience:
    - Minimum 3 years of software development experience
    
    Languages:
    - German (required)
    - English (required)
    
    Nice to have:
    - Docker, Kubernetes
    - AWS/Azure cloud experience
    - Frontend technologies (React, Angular)
    """
    
    print("🎯 Testing Java/Spring Boot Job Matching")
    print("=" * 60)
    print("📋 Job Requirements:")
    print("   - Java, Spring Boot")
    print("   - PostgreSQL")
    print("   - REST APIs, Git, CI/CD, Scrum")
    print("   - 3+ years experience")
    print("   - CS degree")
    print("   - German + English")
    print()
    
    # Test CVs
    cv_files = [
        "uploads/CV_Maria_Schmidt_80.pdf",
        "uploads/CV_Max_Mueller_20.pdf"
    ]
    
    matcher = CVMatcher()
    extractor = CVDataExtractor()
    
    results = []
    
    for cv_file in cv_files:
        if not os.path.exists(cv_file):
            print(f"❌ File not found: {cv_file}")
            continue
            
        print(f"📄 Analyzing: {os.path.basename(cv_file)}")
        print("-" * 40)
        
        # Extract CV content
        cv_content = extractor.extract_text_from_file(cv_file)
        if not cv_content:
            print("❌ Could not extract content")
            continue
        
        # Manual analysis based on your assessment
        candidate_name = "Maria Schmidt" if "Maria" in cv_file else "Max Müller"
        
        print(f"👤 Candidate: {candidate_name}")
        print(f"📝 CV Content Preview: {cv_content[:200]}...")
        print()
        
        # Calculate scores
        tf_idf_score = matcher.calculate_tf_idf_similarity(job_description, cv_content)
        keyword_score = matcher.calculate_keyword_match(job_description, cv_content)
        skill_score = matcher.calculate_skill_match(job_description, cv_content)
        
        # Overall score with improved weighting
        overall_score = (skill_score * 0.6) + (keyword_score * 0.25) + (tf_idf_score * 0.15)
        
        print(f"📊 Detailed Scoring:")
        print(f"   Skill Match:        {skill_score:.3f} ({skill_score*100:.1f}%) - Weight: 60%")
        print(f"   Keyword Match:      {keyword_score:.3f} ({keyword_score*100:.1f}%) - Weight: 25%")
        print(f"   Content Similarity: {tf_idf_score:.3f} ({tf_idf_score*100:.1f}%) - Weight: 15%")
        print(f"   OVERALL SCORE:      {overall_score:.3f} ({overall_score*100:.1f}%)")
        print()
        
        # Manual verification based on your analysis
        if "Maria" in cv_file:
            expected_score = "80-90%"
            print(f"✅ Expected Score: {expected_score} (Highly qualified)")
            print("   ✅ Java, Spring Boot")
            print("   ✅ PostgreSQL")
            print("   ✅ REST APIs, Git, Jenkins")
            print("   ✅ Scrum")
            print("   ✅ CS Degree")
            print("   ✅ 5 years experience (3+ required)")
            print("   ✅ German (C2), English (B2)")
        else:
            expected_score = "10-20%"
            print(f"❌ Expected Score: {expected_score} (Poor match)")
            print("   ❌ No Java or Spring Boot")
            print("   ❌ No PostgreSQL")
            print("   ❌ No REST, Git, CI/CD, Scrum")
            print("   ❌ No CS degree")
            print("   ❌ No software development")
            print("   ⚠️  Only partial language match")
        
        actual_percentage = overall_score * 100
        print(f"\n🎯 ACCURACY CHECK:")
        if "Maria" in cv_file:
            if actual_percentage >= 70:
                print(f"   ✅ ACCURATE: {actual_percentage:.1f}% (Expected: 80-90%)")
            else:
                print(f"   ❌ INACCURATE: {actual_percentage:.1f}% (Expected: 80-90%)")
        else:
            if actual_percentage <= 25:
                print(f"   ✅ ACCURATE: {actual_percentage:.1f}% (Expected: 10-20%)")
            else:
                print(f"   ❌ INACCURATE: {actual_percentage:.1f}% (Expected: 10-20%)")
        
        results.append({
            'name': candidate_name,
            'file': os.path.basename(cv_file),
            'score': actual_percentage,
            'expected': expected_score
        })
        
        print("=" * 60)
    
    # Final ranking
    results.sort(key=lambda x: x['score'], reverse=True)
    
    print("🏆 FINAL RANKING:")
    print("-" * 30)
    for i, result in enumerate(results, 1):
        print(f"{i}. {result['name']}: {result['score']:.1f}% (Expected: {result['expected']})")
    
    # Overall accuracy assessment
    print(f"\n📈 SYSTEM ACCURACY ASSESSMENT:")
    maria_result = next((r for r in results if 'Maria' in r['name']), None)
    max_result = next((r for r in results if 'Max' in r['name']), None)
    
    if maria_result and max_result:
        if maria_result['score'] > max_result['score']:
            print("✅ Ranking is correct: Maria scores higher than Max")
        else:
            print("❌ Ranking is incorrect: Max scores higher than Maria")
        
        if maria_result['score'] >= 70:
            print("✅ Maria's score is in acceptable range (70%+)")
        else:
            print(f"❌ Maria's score is too low: {maria_result['score']:.1f}% (should be 80-90%)")
        
        if max_result['score'] <= 25:
            print("✅ Max's score is in acceptable range (≤25%)")
        else:
            print(f"❌ Max's score is too high: {max_result['score']:.1f}% (should be 10-20%)")


def main():
    """Main function"""
    print("🚀 Accurate CV Matching Test")
    print("Testing against real job requirements")
    print()
    
    if not os.path.exists("matcher.py"):
        print("❌ Please run this script from the project root directory")
        return
    
    test_java_spring_job()


if __name__ == "__main__":
    main()
