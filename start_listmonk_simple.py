#!/usr/bin/env python3
"""
🚀 BAUCH HR - Simple Listmonk Starter
====================================
Easy way to start Listmonk email service
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def check_postgresql():
    """Check if PostgreSQL is installed and running"""
    print("🔍 Checking PostgreSQL...")
    
    try:
        # Try to connect to PostgreSQL
        result = subprocess.run(['psql', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ PostgreSQL is installed")
            return True
        else:
            print("❌ PostgreSQL not found")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ PostgreSQL not found or not in PATH")
        return False

def setup_postgresql_database():
    """Set up PostgreSQL database for Listmonk"""
    print("🔧 Setting up PostgreSQL database...")
    
    commands = [
        'createdb listmonk',
        'psql -d listmonk -c "CREATE USER listmonk_user WITH PASSWORD \'listmonk123\';"',
        'psql -d listmonk -c "GRANT ALL PRIVILEGES ON DATABASE listmonk TO listmonk_user;"'
    ]
    
    for cmd in commands:
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {cmd}")
            else:
                print(f"⚠️ {cmd} - {result.stderr}")
        except Exception as e:
            print(f"❌ Error running {cmd}: {e}")

def install_postgresql_windows():
    """Guide for installing PostgreSQL on Windows"""
    print("📥 PostgreSQL Installation Guide for Windows:")
    print("-" * 50)
    print("1. Download PostgreSQL from: https://www.postgresql.org/download/windows/")
    print("2. Run the installer and follow the setup wizard")
    print("3. Remember the password you set for the 'postgres' user")
    print("4. Make sure to add PostgreSQL to your PATH")
    print("5. Restart your command prompt/PowerShell")
    print("6. Run this script again")
    print()
    print("🔗 Direct download link:")
    print("   https://get.enterprisedb.com/postgresql/postgresql-15.4-1-windows-x64.exe")

def start_listmonk():
    """Start Listmonk service"""
    print("🚀 Starting Listmonk...")
    
    email_system_dir = Path("email_system")
    if not email_system_dir.exists():
        print("❌ email_system directory not found")
        return False
    
    listmonk_exe = email_system_dir / "listmonk.exe"
    if not listmonk_exe.exists():
        print("❌ listmonk.exe not found in email_system directory")
        return False
    
    config_file = email_system_dir / "config.toml"
    if not config_file.exists():
        print("❌ config.toml not found in email_system directory")
        return False
    
    try:
        # Change to email_system directory
        os.chdir(email_system_dir)
        
        # Install database schema (first time only)
        print("🔧 Installing Listmonk database schema...")
        install_result = subprocess.run(['./listmonk.exe', '--install', '--yes'], 
                                      capture_output=True, text=True, timeout=30)
        
        if install_result.returncode == 0:
            print("✅ Database schema installed")
        else:
            print(f"⚠️ Install output: {install_result.stderr}")
        
        # Start Listmonk
        print("🚀 Starting Listmonk server...")
        print("   Access at: http://localhost:9000")
        print("   Username: taha")
        print("   Password: taha135")
        print()
        print("Press Ctrl+C to stop Listmonk")
        
        # Run Listmonk (this will block)
        subprocess.run(['./listmonk.exe'], check=True)
        
    except KeyboardInterrupt:
        print("\n🛑 Listmonk stopped by user")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error starting Listmonk: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_listmonk_connection():
    """Test if Listmonk is running"""
    print("🔍 Testing Listmonk connection...")
    
    try:
        response = requests.get("http://localhost:9000/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ Listmonk is running!")
            print("   Access at: http://localhost:9000")
            return True
        else:
            print(f"❌ Listmonk responded with status: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Listmonk is not running")
        return False
    except Exception as e:
        print(f"❌ Error testing connection: {e}")
        return False

def main():
    """Main function"""
    print("🚀 BAUCH HR - Listmonk Email Service Starter")
    print("=" * 50)
    
    # Check if Listmonk is already running
    if test_listmonk_connection():
        print("✅ Listmonk is already running!")
        return
    
    # Check PostgreSQL
    if not check_postgresql():
        install_postgresql_windows()
        return
    
    # Setup database
    setup_postgresql_database()
    
    # Start Listmonk
    start_listmonk()

if __name__ == "__main__":
    main()
