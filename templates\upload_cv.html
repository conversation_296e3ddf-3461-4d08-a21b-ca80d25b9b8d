{% extends 'base.html' %}

{% block title %}Upload CV - HR Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-upload me-2"></i>Upload CV
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('upload_cv') }}" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="job" class="form-label">Select Job</label>
                        <select class="form-select" id="job" name="job" required>
                            <option value="" {% if not selected_job %}selected{% endif %} disabled>Choose a job...</option>
                            {% for job in jobs %}
                                <option value="{{ job.title }}" {% if selected_job == job.title %}selected{% endif %}>
                                    {{ job.title }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="candidate_name" class="form-label">Candidate Name</label>
                        <input type="text" class="form-control" id="candidate_name" name="candidate_name" 
                               value="{{ candidate_name or '' }}" required>
                        <div class="form-text">Enter the full name of the candidate.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="cv_file" class="form-label">CV File</label>
                        <input class="form-control" type="file" id="cv_file" name="cv_file" accept=".pdf,.docx" required>
                        <div class="form-text">Accepted formats: PDF, DOCX (Max size: 16MB)</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('cvs') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> Back to CVs
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-upload me-1"></i> Upload CV
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Upload Guidelines
                </h5>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li>Make sure the CV is in PDF or DOCX format.</li>
                    <li>The system will extract text from the CV for matching purposes.</li>
                    <li>For best results, ensure the CV is properly formatted and contains relevant skills and experience.</li>
                    <li>The candidate name will be used to identify the CV in the system.</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
