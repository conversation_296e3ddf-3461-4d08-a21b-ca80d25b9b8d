#!/usr/bin/env python3
"""
Comprehensive CV Field Extraction Testing Suite
Tests extraction accuracy for German and English CVs across all fields
"""

import os
import glob
import json
from typing import Dict, List, Tuple
from cv_extractor import CVDataExtractor
try:
    from bilingual_cv_extractor import BilingualCVExtractor
    BILINGUAL_AVAILABLE = True
except ImportError:
    BILINGUAL_AVAILABLE = False
    print("⚠️  Bilingual extractor not available, testing basic extractor only")

class ExtractionTestSuite:
    def __init__(self):
        self.basic_extractor = CVDataExtractor()
        if BILINGUAL_AVAILABLE:
            self.bilingual_extractor = BilingualCVExtractor()
        
        # Test cases with expected results
        self.test_cases = self.create_test_cases()
        
        # All fields to test
        self.all_fields = ['name', 'email', 'phone', 'experience', 'skills', 'education']

    def create_test_cases(self) -> List[Dict]:
        """Create comprehensive test cases for extraction"""
        return [
            {
                'name': 'German CV - Complete',
                'content': """
                LEBENSLAUF
                
                <PERSON>wicklerin
                
                Kontakt:
                E-Mail: <EMAIL>
                Telefon: +49 30 ********
                Mobil: 0171 9876543
                
                BERUFSERFAHRUNG
                5 Jahre Berufserfahrung in der Softwareentwicklung
                
                Senior Java Entwicklerin | TechCorp GmbH | 2020 - heute
                • Entwicklung von Webanwendungen mit Java und Spring Boot
                • Arbeit mit PostgreSQL-Datenbanken
                
                AUSBILDUNG
                Master of Science Informatik | TU Berlin | 2018
                Bachelor of Science Informatik | Universität München | 2016
                
                KENNTNISSE
                Java, Spring Boot, PostgreSQL, Git, Jenkins, Scrum
                """,
                'expected': {
                    'name': 'Anna Müller',
                    'email': '<EMAIL>',
                    'phone': '+49 30 ********',
                    'experience': '5 years',
                    'skills': 'Java, Spring, Git, Jenkins',
                    'education': 'Master of Science Informatik'
                },
                'language': 'german'
            },
            {
                'name': 'English CV - Complete',
                'content': """
                CURRICULUM VITAE
                
                John Smith
                Software Developer
                
                Contact:
                Email: <EMAIL>
                Phone: +44 20 7123 4567
                Mobile: +44 7890 123456
                
                PROFESSIONAL EXPERIENCE
                5+ years of software development experience
                
                Senior Java Developer | TechSolutions Ltd | 2019 - Present
                • Developed enterprise applications using Java and Spring Boot
                • Worked with PostgreSQL databases
                
                EDUCATION
                Master of Science in Computer Science | University of Cambridge | 2018
                Bachelor of Science in Computer Science | University of Oxford | 2016
                
                TECHNICAL SKILLS
                Java, Spring Boot, PostgreSQL, Git, Jenkins, Scrum
                """,
                'expected': {
                    'name': 'John Smith',
                    'email': '<EMAIL>',
                    'phone': '+44 20 7123 4567',
                    'experience': '5 years',
                    'skills': 'Java, Spring, Git, Jenkins',
                    'education': 'Master of Science in Computer Science'
                },
                'language': 'english'
            },
            {
                'name': 'German CV - Minimal',
                'content': """
                Max Weber
                <EMAIL>
                030 ********
                Java Entwickler, 3 Jahre Erfahrung
                Informatik Studium
                """,
                'expected': {
                    'name': 'Max Weber',
                    'email': '<EMAIL>',
                    'phone': '030 ********',
                    'experience': '3 years',
                    'skills': 'Java',
                    'education': 'Informatik'
                },
                'language': 'german'
            },
            {
                'name': 'German CV - Complex Names',
                'content': """
                Dr. Klaus-Dieter Müller-Schmidt
                E-Mail: <EMAIL>
                Telefon: +49 (0)89 123-456789
                
                Berufserfahrung:
                Seit 2015: Senior Softwareentwickler
                2010-2015: Java Entwickler
                
                Ausbildung:
                Promotion in Informatik, TU München, 2010
                Diplom-Informatiker, Universität Stuttgart, 2008
                
                Fähigkeiten:
                Java, Spring Framework, PostgreSQL, Maven, Git
                """,
                'expected': {
                    'name': 'Klaus-Dieter Müller-Schmidt',
                    'email': '<EMAIL>',
                    'phone': '+49 (0)89 123-456789',
                    'experience': 'Senior level',
                    'skills': 'Java, Spring',
                    'education': 'Promotion in Informatik'
                },
                'language': 'german'
            },
            {
                'name': 'English CV - Edge Cases',
                'content': """
                Sarah O'Connor-Williams
                <EMAIL>
                +****************
                
                Experience: 7+ years in software development
                
                Work History:
                2018-present: Lead Developer at StartupTech
                2015-2018: Senior Software Engineer at BigCorp
                
                Education:
                PhD Computer Science, MIT, 2015
                MS Software Engineering, Stanford, 2013
                
                Skills: Python, Django, React, AWS, Docker
                """,
                'expected': {
                    'name': "Sarah O'Connor-Williams",
                    'email': '<EMAIL>',
                    'phone': '+****************',
                    'experience': '7 years',
                    'skills': 'Python, React, Docker',
                    'education': 'PhD Computer Science'
                },
                'language': 'english'
            },
            {
                'name': 'Mixed Language CV',
                'content': """
                Maria Rodriguez-Schmidt
                Software Developer / Softwareentwicklerin
                
                Contact / Kontakt:
                Email: <EMAIL>
                Telefon: +49 30 98765432
                
                Experience / Berufserfahrung:
                4 years software development / 4 Jahre Softwareentwicklung
                
                Education / Ausbildung:
                Master Computer Science / Master Informatik
                
                Skills / Kenntnisse:
                Java, Python, Spring Boot, PostgreSQL
                """,
                'expected': {
                    'name': 'Maria Rodriguez-Schmidt',
                    'email': '<EMAIL>',
                    'phone': '+49 30 98765432',
                    'experience': '4 years',
                    'skills': 'Java, Python, Spring',
                    'education': 'Master Computer Science'
                },
                'language': 'mixed'
            }
        ]

    def test_basic_extractor(self) -> Dict:
        """Test the basic CV extractor"""
        print("🔍 TESTING BASIC CV EXTRACTOR")
        print("=" * 40)
        
        results = {
            'total_tests': 0,
            'passed_tests': 0,
            'field_results': {},
            'detailed_results': []
        }
        
        for test_case in self.test_cases:
            print(f"\n📄 Testing: {test_case['name']}")
            
            try:
                # Test extraction directly from text content
                text_content = test_case['content']
                filename = f"cv_{test_case['name'].replace(' ', '_')}.txt"

                # Extract fields directly from text
                extracted_data = {}
                extracted_data['name'] = self.basic_extractor.extract_name(text_content, filename)
                extracted_data['email'] = self.basic_extractor.extract_email(text_content)
                extracted_data['phone'] = self.basic_extractor.extract_phone(text_content)
                extracted_data['experience'] = self.basic_extractor.extract_experience(text_content)
                extracted_data['skills'] = self.basic_extractor.extract_skills(text_content)
                extracted_data['education'] = self.basic_extractor.extract_education(text_content)
                
                # Test each field
                test_result = {
                    'test_name': test_case['name'],
                    'language': test_case['language'],
                    'field_results': {},
                    'overall_success': True
                }
                
                for field in self.all_fields:
                    expected = test_case['expected'].get(field, '')
                    extracted = extracted_data.get(field, '')
                    
                    # Check if extraction was successful
                    success = self.validate_extraction(field, expected, extracted)
                    test_result['field_results'][field] = {
                        'expected': expected,
                        'extracted': extracted,
                        'success': success
                    }
                    
                    if not success:
                        test_result['overall_success'] = False
                    
                    # Update field statistics
                    if field not in results['field_results']:
                        results['field_results'][field] = {'passed': 0, 'total': 0}
                    
                    results['field_results'][field]['total'] += 1
                    if success:
                        results['field_results'][field]['passed'] += 1
                    
                    # Print result
                    status = "✅" if success else "❌"
                    print(f"   {status} {field}: '{extracted}' (expected: '{expected}')")
                
                results['detailed_results'].append(test_result)
                results['total_tests'] += 1
                if test_result['overall_success']:
                    results['passed_tests'] += 1
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        return results

    def test_bilingual_extractor(self) -> Dict:
        """Test the bilingual CV extractor if available"""
        if not BILINGUAL_AVAILABLE:
            return {'error': 'Bilingual extractor not available'}
        
        print(f"\n🌍 TESTING BILINGUAL CV EXTRACTOR")
        print("=" * 40)
        
        results = {
            'total_tests': 0,
            'passed_tests': 0,
            'field_results': {},
            'detailed_results': []
        }
        
        for test_case in self.test_cases:
            print(f"\n📄 Testing: {test_case['name']}")
            
            try:
                # Test extraction directly from text content
                text_content = test_case['content']
                filename = f"cv_{test_case['name'].replace(' ', '_')}.txt"

                # Extract fields directly from text using bilingual methods
                extracted_data = {}
                extracted_data['name'] = self.bilingual_extractor.extract_name_bilingual(text_content, filename)
                extracted_data['email'] = self.bilingual_extractor.extract_email(text_content)
                extracted_data['phone'] = self.bilingual_extractor.extract_phone_bilingual(text_content)
                extracted_data['experience'] = self.bilingual_extractor.extract_experience_bilingual(text_content)
                extracted_data['skills'] = self.bilingual_extractor.extract_skills_bilingual(text_content)
                extracted_data['education'] = self.bilingual_extractor.extract_education_bilingual(text_content)
                
                # Test each field
                test_result = {
                    'test_name': test_case['name'],
                    'language': test_case['language'],
                    'field_results': {},
                    'overall_success': True
                }
                
                for field in self.all_fields:
                    expected = test_case['expected'].get(field, '')
                    extracted = extracted_data.get(field, '')
                    
                    # Check if extraction was successful
                    success = self.validate_extraction(field, expected, extracted)
                    test_result['field_results'][field] = {
                        'expected': expected,
                        'extracted': extracted,
                        'success': success
                    }
                    
                    if not success:
                        test_result['overall_success'] = False
                    
                    # Update field statistics
                    if field not in results['field_results']:
                        results['field_results'][field] = {'passed': 0, 'total': 0}
                    
                    results['field_results'][field]['total'] += 1
                    if success:
                        results['field_results'][field]['passed'] += 1
                    
                    # Print result
                    status = "✅" if success else "❌"
                    print(f"   {status} {field}: '{extracted}' (expected: '{expected}')")
                
                results['detailed_results'].append(test_result)
                results['total_tests'] += 1
                if test_result['overall_success']:
                    results['passed_tests'] += 1
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        return results

    def validate_extraction(self, field: str, expected: str, extracted: str) -> bool:
        """Validate if extraction result is acceptable"""
        if not expected:  # No expectation set
            return True
        
        if not extracted or extracted.endswith('not found') or extracted.endswith('not specified'):
            return False
        
        expected_lower = expected.lower()
        extracted_lower = extracted.lower()
        
        if field == 'name':
            # For names, check if main parts are present
            expected_parts = expected_lower.split()
            return any(part in extracted_lower for part in expected_parts if len(part) > 2)
        
        elif field == 'email':
            # Email should be exact match or contain the expected email
            return expected_lower in extracted_lower
        
        elif field == 'phone':
            # Phone should contain the main digits
            expected_digits = ''.join(filter(str.isdigit, expected))
            extracted_digits = ''.join(filter(str.isdigit, extracted))
            return expected_digits in extracted_digits
        
        elif field == 'experience':
            # Experience should contain the number of years
            expected_numbers = ''.join(filter(str.isdigit, expected))
            extracted_numbers = ''.join(filter(str.isdigit, extracted))
            return expected_numbers in extracted_numbers if expected_numbers else True
        
        elif field == 'skills':
            # Skills should contain at least some of the expected skills
            expected_skills = [s.strip().lower() for s in expected.split(',')]
            return any(skill in extracted_lower for skill in expected_skills)
        
        elif field == 'education':
            # Education should contain key terms
            expected_terms = expected_lower.split()
            return any(term in extracted_lower for term in expected_terms if len(term) > 3)
        
        return True

def main():
    """Run comprehensive extraction tests"""
    print("🚀 COMPREHENSIVE CV FIELD EXTRACTION TEST SUITE")
    print("=" * 60)
    
    test_suite = ExtractionTestSuite()
    
    # Test basic extractor
    basic_results = test_suite.test_basic_extractor()
    
    # Test bilingual extractor if available
    bilingual_results = test_suite.test_bilingual_extractor()
    
    # Print summary
    print(f"\n📊 EXTRACTION TEST SUMMARY")
    print("=" * 35)
    
    print(f"\n🔍 Basic Extractor Results:")
    print(f"   Overall: {basic_results['passed_tests']}/{basic_results['total_tests']} tests passed")
    for field, stats in basic_results['field_results'].items():
        success_rate = (stats['passed'] / stats['total']) * 100 if stats['total'] > 0 else 0
        status = "✅" if success_rate >= 80 else "⚠️" if success_rate >= 60 else "❌"
        print(f"   {status} {field}: {stats['passed']}/{stats['total']} ({success_rate:.1f}%)")
    
    if 'error' not in bilingual_results:
        print(f"\n🌍 Bilingual Extractor Results:")
        print(f"   Overall: {bilingual_results['passed_tests']}/{bilingual_results['total_tests']} tests passed")
        for field, stats in bilingual_results['field_results'].items():
            success_rate = (stats['passed'] / stats['total']) * 100 if stats['total'] > 0 else 0
            status = "✅" if success_rate >= 80 else "⚠️" if success_rate >= 60 else "❌"
            print(f"   {status} {field}: {stats['passed']}/{stats['total']} ({success_rate:.1f}%)")
    
    print(f"\n✅ Extraction testing completed!")

if __name__ == "__main__":
    main()
