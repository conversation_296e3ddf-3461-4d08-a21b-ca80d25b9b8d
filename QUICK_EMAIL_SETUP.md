# 🚀 Quick Email Setup Guide
## Get your email system running in 5 minutes

### 📋 Prerequisites
- Your email account credentials (Gmail, Outlook, etc.)
- PostgreSQL installed (optional, can use SQLite)

---

## ⚡ Quick Setup (5 Steps)

### **Step 1: Install Dependencies**
```bash
pip install -r requirements.txt
```

### **Step 2: Run Security Setup Script**
```bash
python setup_secure_email.py
```
This will:
- Generate secure passwords
- Create `.env` file with your email credentials
- Set up Listmonk configuration

### **Step 3: Start Listmonk**
```bash
cd email_system
./listmonk --install  # First time only
./listmonk           # Start the server
```

### **Step 4: Configure Listmonk Web Interface**
1. Open: http://localhost:9000
2. Login with credentials from setup script
3. Go to Settings → SMTP
4. Verify your email settings

### **Step 5: Test Email Sending**
```bash
python test_email_integration.py
```

---

## 📧 Where Your Email Credentials Go

### **Primary Location: `.env` file**
```env
# These are YOUR email account credentials
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# This appears as the "From" address
DEFAULT_SENDER_EMAIL=<EMAIL>
DEFAULT_SENDER_NAME=BAUCH HR Team
```

### **Secondary Location: Listmonk Web Interface**
- URL: http://localhost:9000
- Go to: Settings → SMTP
- Enter the same credentials as above

---

## 🔑 Email Provider Quick Setup

### **Gmail Users:**
1. Enable 2-Factor Authentication
2. Generate App Password:
   - Google Account → Security → App passwords
   - Select "Mail" → Generate
3. Use the 16-character app password (not your regular password)

### **Outlook Users:**
1. Use your regular email and password
2. No special setup required

### **Other Providers:**
1. Get SMTP settings from your email provider
2. Usually: `mail.yourprovider.com`, port 587, TLS enabled

---

## ✅ Verification Steps

### **Check 1: Listmonk Running**
```bash
curl http://localhost:9000/api/health
# Should return: {"status":"ok"}
```

### **Check 2: Email Service Connected**
```python
from listmonk_integration import ListmonkEmailService
service = ListmonkEmailService()
# Should show: "✅ Connected to Listmonk server successfully"
```

### **Check 3: Send Test Email**
1. Go to http://localhost:9000
2. Create a test campaign
3. Send to your own email

---

## 🚨 Security Reminders

- ✅ **DO:** Use environment variables (`.env` file)
- ✅ **DO:** Use app passwords for Gmail
- ✅ **DO:** Keep `.env` file secure (don't commit to git)
- ❌ **DON'T:** Hardcode passwords in source code
- ❌ **DON'T:** Use default passwords in production

---

## 🆘 Quick Troubleshooting

### **"Authentication failed"**
- Check email credentials in `.env`
- For Gmail: Use app password, not regular password
- Verify 2FA is enabled for Gmail

### **"Connection refused"**
- Make sure Listmonk is running: `cd email_system && ./listmonk`
- Check if another service is using port 9000

### **"Import error"**
- Install dependencies: `pip install -r requirements.txt`
- Make sure you're in the correct directory

---

## 📞 Need Help?

1. **Check logs:** `logs/hr_system.log`
2. **Read full guide:** `EMAIL_SECURITY_GUIDE.md`
3. **Test configuration:** `python test_email_integration.py`

**Your email system should now be ready to send professional HR emails! 🎉**
