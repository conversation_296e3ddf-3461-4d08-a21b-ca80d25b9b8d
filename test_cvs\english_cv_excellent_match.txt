CURRICULUM VITAE

Sarah <PERSON>
Senior Software Developer

Contact:
Email: <EMAIL>
Phone: +44 20 7123 4567
Address: London, United Kingdom

PROFESSIONAL EXPERIENCE

Senior Java Developer | TechSolutions Ltd | 2019 - Present
• Developed enterprise web applications using Java and Spring Boot framework
• Worked extensively with PostgreSQL databases and database optimization
• Built and maintained REST APIs for microservices architecture
• Implemented CI/CD pipelines using Git, Jenkins, and automated testing
• Collaborated in agile Scrum teams with cross-functional members
• Mentored junior developers and conducted code reviews
• 5+ years of professional software development experience

Java Software Engineer | InnovateTech | 2017 - 2019
• Designed and implemented backend services using Java 8/11
• Developed RESTful web services and APIs
• Worked with Spring Framework, Hibernate, and JPA
• Integrated with third-party APIs and payment systems
• Participated in agile development processes and sprint planning

Junior Developer | StartupCorp | 2015 - 2017
• Learned Java programming and object-oriented design principles
• Contributed to web application development projects
• Gained experience with version control systems (Git)
• Participated in team meetings and project planning sessions

EDUCATION

Master of Science in Computer Science | University of Cambridge | 2013 - 2015
• Specialization: Software Engineering and Database Systems
• Thesis: "Optimizing Database Performance in Distributed Systems"
• Grade: First Class Honours

Bachelor of Science in Computer Science | University of Oxford | 2010 - 2013
• Core subjects: Programming, Algorithms, Data Structures
• Grade: Upper Second Class Honours (2:1)

TECHNICAL SKILLS

Programming Languages:
• Java (Expert level) - 6+ years experience
• Python (Intermediate)
• JavaScript (Intermediate)
• SQL (Advanced)

Frameworks and Technologies:
• Spring Boot, Spring Framework
• Spring Security, Spring Data JPA
• Hibernate, JPA
• REST API development
• Microservices architecture

Databases:
• PostgreSQL (Expert level)
• MySQL
• Oracle Database
• MongoDB (Basic)

Development Tools:
• Git, GitHub, GitLab
• Jenkins, CI/CD pipelines
• Docker, Kubernetes
• Maven, Gradle
• IntelliJ IDEA, Eclipse
• JIRA, Confluence

Methodologies:
• Agile development
• Scrum methodology
• Test-driven development (TDD)
• Code review processes

LANGUAGE SKILLS
• English: Native speaker
• German: Conversational (B2)
• French: Basic (A2)

CERTIFICATIONS
• Oracle Certified Professional Java SE 11 Developer
• Spring Professional Certification
• AWS Certified Developer - Associate

NOTABLE PROJECTS

E-Commerce Platform Redesign | 2021 - 2022
• Led the backend development of a complete e-commerce platform overhaul
• Implemented using Java, Spring Boot, and PostgreSQL
• Designed and built REST APIs for frontend integration
• Achieved 40% performance improvement over legacy system

Financial Services API | 2020 - 2021
• Developed secure REST APIs for financial transactions
• Implemented using Spring Boot with Spring Security
• Integrated with external payment gateways and banking systems
• Ensured compliance with financial regulations and security standards

Real-time Analytics Dashboard | 2019 - 2020
• Built backend services for real-time data processing
• Used Java with Spring Boot and PostgreSQL
• Implemented WebSocket connections for live updates
• Collaborated with frontend team using Scrum methodology
