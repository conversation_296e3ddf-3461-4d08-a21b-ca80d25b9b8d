# Flask and web framework dependencies
Flask==2.3.3
Werkzeug==2.3.7

# Database dependencies
SQLAlchemy==2.0.21
alembic==1.12.0

# Document processing dependencies
PyMuPDF==1.23.8
python-docx==0.8.11

# Excel processing dependencies
openpyxl==3.1.2

# Security dependencies
bcrypt==4.0.1

# Natural Language Processing dependencies
spacy==3.7.2
langdetect==1.0.9
googletrans==4.0.0rc1
scikit-learn==1.3.2

# German language model for spaCy (install separately with: python -m spacy download de_core_news_sm)
# English language model for spaCy (install separately with: python -m spacy download en_core_web_sm)

# Optional: For better performance and production deployment
gunicorn==21.2.0

# Environment variable management (REQUIRED for security)
python-dotenv==1.0.0

# Email service dependencies
requests==2.31.0

# Additional security dependencies
cryptography==41.0.7
