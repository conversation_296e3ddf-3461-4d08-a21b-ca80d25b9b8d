#!/usr/bin/env python3
"""
Comprehensive CV Matching Test Suite
Tests German and English CV matching accuracy with detailed analysis
"""

import os
import sys
from typing import Dict, List, Tuple
from matcher import CVMatcher
from cv_extractor import CVDataExtractor
import json

class CVMatchingTestSuite:
    def __init__(self):
        self.matcher = CVMatcher()
        self.extractor = CVDataExtractor()
        
        # Test job descriptions
        self.job_descriptions = {
            'java_developer_german': """
            Java Softwareentwickler (m/w/d)
            
            Wir suchen einen erfahrenen Java-Entwickler für unser Team.
            
            Anforderungen:
            • Mindestens 3 Jahre Berufserfahrung in der Java-Entwicklung
            • Erfahrung mit Spring Boot Framework
            • Kenntnisse in PostgreSQL-Datenbanken
            • Entwicklung von REST-Services und REST-APIs
            • Erfahrung mit Git und Jenkins für CI/CD
            • Arbeit in agilen Scrum-Teams
            • Abgeschlossenes Studium der Informatik oder vergleichbare Qualifikation
            • Sehr gute Deutsch- und Englischkenntnisse
            
            Wünschenswert:
            • Docker und Kubernetes Erfahrung
            • Cloud-Erfahrung (AWS/Azure)
            • Frontend-Technologien (React, Angular)
            """,
            
            'java_developer_english': """
            Senior Java Developer Position
            
            We are looking for an experienced Java developer to join our team.
            
            Requirements:
            • 3+ years of professional Java development experience
            • Strong experience with Spring Boot framework
            • PostgreSQL database experience
            • REST API development and web services
            • Git version control and Jenkins CI/CD experience
            • Agile Scrum methodology experience
            • Bachelor's degree in Computer Science or equivalent
            • Excellent English and German language skills
            
            Nice to have:
            • Docker and Kubernetes experience
            • Cloud platforms (AWS/Azure)
            • Frontend technologies (React, Angular)
            """,
            
            'marketing_manager_english': """
            Marketing Manager Position
            
            We are seeking a dynamic Marketing Manager to lead our marketing efforts.
            
            Requirements:
            • 3+ years of marketing experience
            • Digital marketing expertise (SEO, PPC, Social Media)
            • Experience with marketing analytics tools
            • Strong communication and leadership skills
            • Bachelor's degree in Marketing, Business, or related field
            • Excellent English language skills
            
            Preferred:
            • MBA degree
            • Experience with CRM systems
            • Creative design skills
            """
        }
        
        # Test CV files
        self.test_cvs = {
            'german_excellent': 'test_cvs/german_cv_excellent_match.txt',
            'german_poor': 'test_cvs/german_cv_poor_match.txt',
            'english_excellent': 'test_cvs/english_cv_excellent_match.txt',
            'english_poor': 'test_cvs/english_cv_poor_match.txt'
        }

    def load_cv_content(self, cv_path: str) -> str:
        """Load CV content from file"""
        try:
            with open(cv_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"Error loading CV {cv_path}: {e}")
            return ""

    def test_single_match(self, job_desc: str, cv_content: str, cv_name: str) -> Dict:
        """Test a single CV against a job description"""
        # Calculate individual scores
        tf_idf_score = self.matcher.calculate_tf_idf_similarity(job_desc, cv_content)
        keyword_score = self.matcher.calculate_keyword_match(job_desc, cv_content)
        skill_score = self.matcher.calculate_skill_match(job_desc, cv_content)
        
        # Calculate overall score (same weights as in matcher)
        overall_score = (skill_score * 0.5) + (keyword_score * 0.3) + (tf_idf_score * 0.2)
        
        return {
            'cv_name': cv_name,
            'overall_score': overall_score * 100,
            'skill_score': skill_score * 100,
            'keyword_score': keyword_score * 100,
            'tf_idf_score': tf_idf_score * 100
        }

    def run_comprehensive_test(self):
        """Run comprehensive testing of all CV/job combinations"""
        print("🚀 COMPREHENSIVE CV MATCHING TEST SUITE")
        print("=" * 60)
        
        # Test each job description against all CVs
        for job_name, job_desc in self.job_descriptions.items():
            print(f"\n💼 Testing Job: {job_name.replace('_', ' ').title()}")
            print("-" * 50)
            
            results = []
            
            # Test each CV
            for cv_name, cv_path in self.test_cvs.items():
                if not os.path.exists(cv_path):
                    print(f"⚠️  CV file not found: {cv_path}")
                    continue
                
                cv_content = self.load_cv_content(cv_path)
                if not cv_content:
                    continue
                
                result = self.test_single_match(job_desc, cv_content, cv_name)
                results.append(result)
            
            # Sort results by overall score
            results.sort(key=lambda x: x['overall_score'], reverse=True)
            
            # Display results
            print(f"📊 Results (sorted by overall score):")
            for i, result in enumerate(results, 1):
                print(f"{i}. {result['cv_name']}: {result['overall_score']:.1f}%")
                print(f"   Skills: {result['skill_score']:.1f}% | "
                      f"Keywords: {result['keyword_score']:.1f}% | "
                      f"Content: {result['tf_idf_score']:.1f}%")
            
            # Analyze results
            self.analyze_results(job_name, results)

    def analyze_results(self, job_name: str, results: List[Dict]):
        """Analyze if the results make sense"""
        print(f"\n🔍 Analysis for {job_name}:")
        
        if 'java' in job_name.lower():
            # For Java jobs, excellent matches should score higher
            excellent_scores = [r for r in results if 'excellent' in r['cv_name']]
            poor_scores = [r for r in results if 'poor' in r['cv_name']]
            
            if excellent_scores and poor_scores:
                best_excellent = max(excellent_scores, key=lambda x: x['overall_score'])
                best_poor = max(poor_scores, key=lambda x: x['overall_score'])
                
                if best_excellent['overall_score'] > best_poor['overall_score']:
                    print(f"   ✅ Correct: Excellent match ({best_excellent['overall_score']:.1f}%) > "
                          f"Poor match ({best_poor['overall_score']:.1f}%)")
                else:
                    print(f"   ❌ Issue: Poor match ({best_poor['overall_score']:.1f}%) >= "
                          f"Excellent match ({best_excellent['overall_score']:.1f}%)")
        
        elif 'marketing' in job_name.lower():
            # For marketing jobs, poor matches should score higher
            excellent_scores = [r for r in results if 'excellent' in r['cv_name']]
            poor_scores = [r for r in results if 'poor' in r['cv_name']]
            
            if excellent_scores and poor_scores:
                best_excellent = max(excellent_scores, key=lambda x: x['overall_score'])
                best_poor = max(poor_scores, key=lambda x: x['overall_score'])
                
                if best_poor['overall_score'] > best_excellent['overall_score']:
                    print(f"   ✅ Correct: Marketing CV ({best_poor['overall_score']:.1f}%) > "
                          f"Java CV ({best_excellent['overall_score']:.1f}%)")
                else:
                    print(f"   ❌ Issue: Java CV ({best_excellent['overall_score']:.1f}%) >= "
                          f"Marketing CV ({best_poor['overall_score']:.1f}%)")

    def test_language_detection(self):
        """Test if the system properly handles German vs English CVs"""
        print(f"\n🌍 LANGUAGE DETECTION TEST")
        print("-" * 40)
        
        for cv_name, cv_path in self.test_cvs.items():
            if not os.path.exists(cv_path):
                continue
                
            cv_content = self.load_cv_content(cv_path)
            if not cv_content:
                continue
            
            # Test with German job
            german_result = self.test_single_match(
                self.job_descriptions['java_developer_german'], 
                cv_content, 
                cv_name
            )
            
            # Test with English job
            english_result = self.test_single_match(
                self.job_descriptions['java_developer_english'], 
                cv_content, 
                cv_name
            )
            
            print(f"{cv_name}:")
            print(f"   German Job: {german_result['overall_score']:.1f}%")
            print(f"   English Job: {english_result['overall_score']:.1f}%")
            
            # Check if language matching affects scores appropriately
            if 'german' in cv_name and german_result['overall_score'] >= english_result['overall_score']:
                print(f"   ✅ German CV scores well with German job")
            elif 'english' in cv_name and english_result['overall_score'] >= german_result['overall_score']:
                print(f"   ✅ English CV scores well with English job")
            else:
                print(f"   ⚠️  Language preference not clearly reflected in scores")

    def test_consistency(self):
        """Test scoring consistency across multiple runs"""
        print(f"\n🔄 CONSISTENCY TEST")
        print("-" * 30)
        
        # Test with one CV/job combination multiple times
        cv_content = self.load_cv_content(self.test_cvs['german_excellent'])
        job_desc = self.job_descriptions['java_developer_german']
        
        scores = []
        for i in range(5):
            result = self.test_single_match(job_desc, cv_content, 'test')
            scores.append(result['overall_score'])
        
        print(f"5 consecutive runs with same CV/job:")
        for i, score in enumerate(scores, 1):
            print(f"   Run {i}: {score:.2f}%")
        
        # Check consistency
        score_range = max(scores) - min(scores)
        if score_range < 0.1:  # Less than 0.1% difference
            print(f"   ✅ Consistent scoring (range: {score_range:.3f}%)")
        else:
            print(f"   ⚠️  Inconsistent scoring (range: {score_range:.3f}%)")

def main():
    """Run the comprehensive test suite"""
    test_suite = CVMatchingTestSuite()
    
    # Check if test CV files exist
    missing_files = []
    for cv_name, cv_path in test_suite.test_cvs.items():
        if not os.path.exists(cv_path):
            missing_files.append(cv_path)
    
    if missing_files:
        print("❌ Missing test CV files:")
        for file in missing_files:
            print(f"   - {file}")
        print("\nPlease ensure all test CV files are created first.")
        return
    
    # Run all tests
    test_suite.run_comprehensive_test()
    test_suite.test_language_detection()
    test_suite.test_consistency()
    
    print(f"\n✅ Comprehensive testing completed!")

if __name__ == "__main__":
    main()
