#!/usr/bin/env python3
"""
Test CV Matching System with All CVs
Check if the improved matching works consistently across all candidates
"""

import os
import glob
from matcher import <PERSON>VMatcher
from cv_extractor import CVDataExtractor


def test_with_all_cvs():
    """Test matching system with all CVs in uploads directory"""
    
    # Sample job descriptions to test with
    job_descriptions = {
        "Java Developer": """
        Java Software Developer Position
        
        Requirements:
        - Java programming (3+ years)
        - Spring Boot framework
        - PostgreSQL database
        - REST API development
        - Git, Jenkins, CI/CD
        - Scrum methodology
        - Bachelor's degree in Computer Science
        - German and English language skills
        """,
        
        "Python Developer": """
        Python Software Developer Position
        
        Requirements:
        - Python programming (2+ years)
        - Django or Flask framework
        - Database experience (MySQL/PostgreSQL)
        - REST API development
        - Git version control
        - Agile development
        - Computer Science degree preferred
        """,
        
        "System Administrator": """
        System Administrator Position
        
        Requirements:
        - Windows Server administration
        - Active Directory management
        - Network configuration
        - IT support experience
        - ITIL knowledge
        - 2+ years experience
        - Technical certification preferred
        """
    }
    
    print("🚀 Testing CV Matching System with All CVs")
    print("=" * 60)
    
    # Get all PDF files in uploads directory
    cv_files = glob.glob("uploads/*.pdf")
    
    if not cv_files:
        print("❌ No PDF files found in uploads directory")
        return
    
    print(f"📁 Found {len(cv_files)} CV files:")
    for cv_file in cv_files:
        print(f"   - {os.path.basename(cv_file)}")
    print()
    
    matcher = CVMatcher()
    extractor = CVDataExtractor()
    
    # Test each job description
    for job_title, job_desc in job_descriptions.items():
        print(f"💼 Testing Job: {job_title}")
        print("-" * 50)
        
        results = []
        
        for cv_file in cv_files:
            try:
                # Extract CV content
                cv_content = extractor.extract_text_from_file(cv_file)
                if not cv_content:
                    print(f"⚠️  Could not extract content from {os.path.basename(cv_file)}")
                    continue
                
                # Calculate scores
                tf_idf_score = matcher.calculate_tf_idf_similarity(job_desc, cv_content)
                keyword_score = matcher.calculate_keyword_match(job_desc, cv_content)
                skill_score = matcher.calculate_skill_match(job_desc, cv_content)
                
                # Overall score
                overall_score = (skill_score * 0.5) + (keyword_score * 0.3) + (tf_idf_score * 0.2)
                
                results.append({
                    'file': os.path.basename(cv_file),
                    'name': extract_name_from_filename(cv_file),
                    'overall': overall_score * 100,
                    'skill': skill_score * 100,
                    'keyword': keyword_score * 100,
                    'tfidf': tf_idf_score * 100
                })
                
            except Exception as e:
                print(f"❌ Error processing {os.path.basename(cv_file)}: {e}")
        
        # Sort by overall score
        results.sort(key=lambda x: x['overall'], reverse=True)
        
        # Display results
        print(f"📊 Results for {job_title}:")
        print(f"{'Rank':<4} {'Candidate':<20} {'Overall':<8} {'Skills':<8} {'Keywords':<8} {'Content':<8}")
        print("-" * 60)
        
        for i, result in enumerate(results, 1):
            print(f"{i:<4} {result['name']:<20} {result['overall']:<7.1f}% {result['skill']:<7.1f}% {result['keyword']:<7.1f}% {result['tfidf']:<7.1f}%")
        
        # Analysis
        print(f"\n🔍 Analysis for {job_title}:")
        if results:
            best_match = results[0]
            worst_match = results[-1]
            
            print(f"   Best match: {best_match['name']} ({best_match['overall']:.1f}%)")
            print(f"   Worst match: {worst_match['name']} ({worst_match['overall']:.1f}%)")
            
            # Check if results make sense
            if job_title == "Java Developer":
                maria_result = next((r for r in results if 'Maria' in r['name']), None)
                max_result = next((r for r in results if 'Max' in r['name']), None)
                
                if maria_result and max_result:
                    print(f"   Maria Schmidt: {maria_result['overall']:.1f}% (Expected: High for Java job)")
                    print(f"   Max Müller: {max_result['overall']:.1f}% (Expected: Low for Java job)")
                    
                    if maria_result['overall'] > max_result['overall']:
                        print("   ✅ Ranking correct: Maria > Max for Java position")
                    else:
                        print("   ❌ Ranking incorrect: Max > Maria for Java position")
            
            elif job_title == "System Administrator":
                maria_result = next((r for r in results if 'Maria' in r['name']), None)
                max_result = next((r for r in results if 'Max' in r['name']), None)
                
                if maria_result and max_result:
                    print(f"   Maria Schmidt: {maria_result['overall']:.1f}% (Expected: Low for SysAdmin job)")
                    print(f"   Max Müller: {max_result['overall']:.1f}% (Expected: High for SysAdmin job)")
                    
                    if max_result['overall'] > maria_result['overall']:
                        print("   ✅ Ranking correct: Max > Maria for SysAdmin position")
                    else:
                        print("   ❌ Ranking incorrect: Maria > Max for SysAdmin position")
        
        print("\n" + "=" * 60)


def extract_name_from_filename(filepath):
    """Extract candidate name from filename"""
    filename = os.path.basename(filepath)
    
    # Common patterns
    if 'Maria' in filename:
        return 'Maria Schmidt'
    elif 'Max' in filename:
        return 'Max Müller'
    elif 'Emma' in filename:
        return 'Emma Brooks'
    elif 'Taha' in filename:
        return 'Taha Mughal'
    else:
        # Extract from filename
        name = filename.replace('CV_', '').replace('.pdf', '').replace('_', ' ')
        return name


def test_consistency():
    """Test consistency of scoring across multiple runs"""
    print("\n🔄 Testing Scoring Consistency")
    print("-" * 40)
    
    job_desc = """
    Software Developer - Java/Spring Boot
    Requirements: Java, Spring Boot, PostgreSQL, 3+ years experience
    """
    
    cv_file = "uploads/CV_Maria_Schmidt_80.pdf"
    
    if not os.path.exists(cv_file):
        print("❌ Maria's CV not found for consistency test")
        return
    
    matcher = CVMatcher()
    extractor = CVDataExtractor()
    cv_content = extractor.extract_text_from_file(cv_file)
    
    scores = []
    for i in range(5):
        skill_score = matcher.calculate_skill_match(job_desc, cv_content)
        keyword_score = matcher.calculate_keyword_match(job_desc, cv_content)
        tf_idf_score = matcher.calculate_tf_idf_similarity(job_desc, cv_content)
        overall = (skill_score * 0.5) + (keyword_score * 0.3) + (tf_idf_score * 0.2)
        scores.append(overall * 100)
    
    print(f"📊 Consistency Test Results (5 runs):")
    for i, score in enumerate(scores, 1):
        print(f"   Run {i}: {score:.2f}%")
    
    avg_score = sum(scores) / len(scores)
    max_deviation = max(abs(score - avg_score) for score in scores)
    
    print(f"   Average: {avg_score:.2f}%")
    print(f"   Max deviation: {max_deviation:.2f}%")
    
    if max_deviation < 0.1:
        print("   ✅ Scoring is consistent (deviation < 0.1%)")
    else:
        print("   ⚠️  Scoring has some variation")


def main():
    """Main function"""
    if not os.path.exists("uploads"):
        print("❌ Uploads directory not found")
        return
    
    test_with_all_cvs()
    test_consistency()


if __name__ == "__main__":
    main()
