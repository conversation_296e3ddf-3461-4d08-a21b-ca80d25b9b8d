#!/usr/bin/env python3
"""
Installation script for bilingual HR management system
Installs required dependencies and downloads spaCy language models
"""

import subprocess
import sys
import os


def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed:")
        print(f"Error: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("✗ Python 3.8 or higher is required")
        return False
    print(f"✓ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True


def install_requirements():
    """Install Python packages from requirements.txt"""
    if not os.path.exists('requirements.txt'):
        print("✗ requirements.txt not found")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Installing Python packages"
    )


def download_spacy_models():
    """Download required spaCy language models"""
    models = [
        ('en_core_web_sm', 'English language model'),
        ('de_core_news_sm', 'German language model')
    ]
    
    success = True
    for model, description in models:
        if not run_command(
            f"{sys.executable} -m spacy download {model}",
            f"Downloading {description}"
        ):
            success = False
    
    return success


def verify_installation():
    """Verify that all components are installed correctly"""
    print("\nVerifying installation...")
    
    # Test imports
    try:
        import spacy
        import langdetect
        import googletrans
        import sklearn
        print("✓ All required packages imported successfully")
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    
    # Test spaCy models
    try:
        nlp_en = spacy.load('en_core_web_sm')
        nlp_de = spacy.load('de_core_news_sm')
        print("✓ spaCy language models loaded successfully")
    except OSError as e:
        print(f"✗ spaCy model error: {e}")
        return False
    
    # Test language detection
    try:
        from langdetect import detect
        test_text = "This is a test sentence."
        detected = detect(test_text)
        print(f"✓ Language detection working (detected: {detected})")
    except Exception as e:
        print(f"✗ Language detection error: {e}")
        return False
    
    return True


def create_test_files():
    """Create test files to verify bilingual functionality"""
    print("\nCreating test files...")
    
    # Test German CV content
    german_cv = """
    Lebenslauf
    
    Name: Max Mustermann
    E-Mail: <EMAIL>
    Telefon: +49 123 456789
    
    Berufserfahrung:
    - 5 Jahre Softwareentwicklung
    - Python, Java, JavaScript
    - Projektmanagement
    - Teamleitung
    
    Ausbildung:
    - Master Informatik, Technische Universität München
    - Bachelor Informatik, Universität Stuttgart
    
    Fähigkeiten:
    - Programmierung: Python, Java, C++
    - Webentwicklung: HTML, CSS, JavaScript, React
    - Datenbanken: MySQL, PostgreSQL
    - Agile Entwicklung, Scrum
    """
    
    # Test English CV content
    english_cv = """
    Resume
    
    Name: John Smith
    Email: <EMAIL>
    Phone: ****** 123 4567
    
    Experience:
    - 3 years software development
    - Python, JavaScript, React
    - Project management
    - Team collaboration
    
    Education:
    - Bachelor Computer Science, MIT
    - Master Software Engineering, Stanford
    
    Skills:
    - Programming: Python, JavaScript, Java
    - Web Development: React, Node.js, HTML, CSS
    - Databases: MongoDB, PostgreSQL
    - Agile methodologies, Scrum
    """
    
    try:
        os.makedirs('test_files', exist_ok=True)
        
        with open('test_files/german_cv_test.txt', 'w', encoding='utf-8') as f:
            f.write(german_cv)
        
        with open('test_files/english_cv_test.txt', 'w', encoding='utf-8') as f:
            f.write(english_cv)
        
        print("✓ Test files created in 'test_files' directory")
        return True
    except Exception as e:
        print(f"✗ Error creating test files: {e}")
        return False


def test_bilingual_functionality():
    """Test the bilingual functionality with sample data"""
    print("\nTesting bilingual functionality...")
    
    try:
        # Test language detection
        from language_detector import LanguageDetector
        detector = LanguageDetector()
        
        german_text = "Ich bin ein Softwareentwickler mit 5 Jahren Berufserfahrung."
        english_text = "I am a software developer with 5 years of experience."
        
        de_lang, de_conf = detector.detect_language(german_text)
        en_lang, en_conf = detector.detect_language(english_text)
        
        print(f"✓ German text detected as: {de_lang} (confidence: {de_conf:.2f})")
        print(f"✓ English text detected as: {en_lang} (confidence: {en_conf:.2f})")
        
        # Test skill extraction
        from german_skills import extract_german_skills_from_text
        skills = extract_german_skills_from_text(german_text + " Python Java React")
        print(f"✓ Skills extracted: {skills}")
        
        print("✓ Bilingual functionality test completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ Bilingual functionality test failed: {e}")
        return False


def main():
    """Main installation function"""
    print("BAUCH HR Management System - Bilingual Support Installation")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        print("\n✗ Installation failed at requirements installation")
        sys.exit(1)
    
    # Download spaCy models
    if not download_spacy_models():
        print("\n⚠ Warning: Some spaCy models failed to download")
        print("You may need to download them manually:")
        print("python -m spacy download en_core_web_sm")
        print("python -m spacy download de_core_news_sm")
    
    # Verify installation
    if not verify_installation():
        print("\n✗ Installation verification failed")
        sys.exit(1)
    
    # Create test files
    create_test_files()
    
    # Test bilingual functionality
    if test_bilingual_functionality():
        print("\n" + "=" * 60)
        print("✓ Installation completed successfully!")
        print("\nNext steps:")
        print("1. Run the application: python app.py")
        print("2. Open your browser to http://localhost:5000")
        print("3. Test the language switcher in the top navigation")
        print("4. Upload German and English CVs to test bilingual processing")
        print("\nFor testing, sample CV files have been created in 'test_files' directory")
    else:
        print("\n⚠ Installation completed with warnings")
        print("Some bilingual features may not work correctly")


if __name__ == "__main__":
    main()
