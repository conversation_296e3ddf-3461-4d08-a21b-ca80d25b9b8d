#!/usr/bin/env python3
"""
Test Enhanced LLM-based matching vs Traditional matching
Compare results on CNC job to see if enhanced approach fixes the ranking issues
"""

from hr_database_working import HRDatabase
from matcher import CVMatcher
import os

def test_both_approaches():
    """Test both traditional and enhanced matching approaches"""
    print("=== COMPARING TRADITIONAL VS ENHANCED MATCHING ===")
    print()
    
    try:
        db = HRDatabase()
        
        # Get CNC job and CVs
        cnc_job = db.get_job_by_title('CNC Fräser')
        if not cnc_job:
            print("CNC job not found!")
            return
            
        cvs = db.get_cvs_for_job('CNC Fräser')
        print(f"Found {len(cvs)} CVs for CNC job")
        print()
        
        # Test 1: Traditional Matching (current system)
        print("=== TRADITIONAL MATCHING RESULTS ===")
        traditional_matcher = CVMatcher(use_enhanced_matching=False)
        traditional_results = []
        
        for cv in cvs:
            try:
                # Calculate traditional scores
                tf_idf_score = traditional_matcher.calculate_tf_idf_similarity(cnc_job.description, cv.content)
                keyword_score = traditional_matcher.calculate_keyword_match(cnc_job.description, cv.content)
                skill_score = traditional_matcher.calculate_skill_match(cnc_job.description, cv.content)
                
                # Calculate overall with manufacturing weights
                overall_score = (skill_score * 0.6) + (keyword_score * 0.25) + (tf_idf_score * 0.15)
                
                traditional_results.append({
                    'name': cv.candidate_name or 'Unknown',
                    'overall': overall_score * 100,
                    'skill': skill_score * 100,
                    'keyword': keyword_score * 100,
                    'tfidf': tf_idf_score * 100
                })
                
            except Exception as e:
                print(f"Error processing CV {cv.candidate_name}: {e}")
        
        # Sort traditional results
        traditional_results.sort(key=lambda x: x['overall'], reverse=True)
        
        for i, result in enumerate(traditional_results, 1):
            print(f"{i}. {result['name']}")
            print(f"   Overall: {result['overall']:.1f}% | Skill: {result['skill']:.1f}% | Keyword: {result['keyword']:.1f}% | TF-IDF: {result['tfidf']:.1f}%")
            print()
        
        print("=" * 60)
        print()
        
        # Test 2: Enhanced Matching (without OpenAI API - fallback mode)
        print("=== ENHANCED MATCHING RESULTS (Fallback Mode) ===")
        enhanced_matcher = CVMatcher(use_enhanced_matching=True, openai_api_key=None)
        enhanced_results = []
        
        for cv in cvs:
            try:
                # This will use the enhanced fallback scoring
                if enhanced_matcher.enhanced_matcher:
                    enhanced_score = enhanced_matcher.enhanced_matcher.calculate_enhanced_match_score(
                        cv.content, cnc_job.description
                    )
                    enhanced_results.append({
                        'name': cv.candidate_name or 'Unknown',
                        'overall': enhanced_score,
                        'approach': 'Enhanced'
                    })
                else:
                    # Fallback to traditional
                    tf_idf_score = enhanced_matcher.calculate_tf_idf_similarity(cnc_job.description, cv.content)
                    keyword_score = enhanced_matcher.calculate_keyword_match(cnc_job.description, cv.content)
                    skill_score = enhanced_matcher.calculate_skill_match(cnc_job.description, cv.content)
                    overall_score = (skill_score * 0.6) + (keyword_score * 0.25) + (tf_idf_score * 0.15)
                    
                    enhanced_results.append({
                        'name': cv.candidate_name or 'Unknown',
                        'overall': overall_score * 100,
                        'approach': 'Traditional (fallback)'
                    })
                    
            except Exception as e:
                print(f"Error processing CV {cv.candidate_name}: {e}")
        
        # Sort enhanced results
        enhanced_results.sort(key=lambda x: x['overall'], reverse=True)
        
        for i, result in enumerate(enhanced_results, 1):
            print(f"{i}. {result['name']}")
            print(f"   Overall: {result['overall']:.1f}% | Approach: {result['approach']}")
            print()
        
        # Compare rankings
        print("=" * 60)
        print("=== RANKING COMPARISON ===")
        print()
        
        print("Traditional Ranking:")
        for i, result in enumerate(traditional_results, 1):
            print(f"{i}. {result['name']} ({result['overall']:.1f}%)")
        
        print()
        print("Enhanced Ranking:")
        for i, result in enumerate(enhanced_results, 1):
            print(f"{i}. {result['name']} ({result['overall']:.1f}%)")
        
        print()
        
        # Analyze specific candidates mentioned by user
        print("=== ANALYSIS OF SPECIFIC CANDIDATES ===")
        daniel_traditional = next((r for r in traditional_results if 'Daniel' in r['name']), None)
        reichelt_traditional = next((r for r in traditional_results if 'Reichelt' in r['name']), None)
        pascal_traditional = next((r for r in traditional_results if 'Pascal' in r['name']), None)
        
        daniel_enhanced = next((r for r in enhanced_results if 'Daniel' in r['name']), None)
        reichelt_enhanced = next((r for r in enhanced_results if 'Reichelt' in r['name']), None)
        pascal_enhanced = next((r for r in enhanced_results if 'Pascal' in r['name']), None)
        
        if daniel_traditional and reichelt_traditional:
            print(f"Daniel Meixner (CNC experience):")
            print(f"  Traditional: {daniel_traditional['overall']:.1f}%")
            if daniel_enhanced:
                print(f"  Enhanced: {daniel_enhanced['overall']:.1f}%")
            print()
            
            print(f"Reichelt Frank (less relevant experience):")
            print(f"  Traditional: {reichelt_traditional['overall']:.1f}%")
            if reichelt_enhanced:
                print(f"  Enhanced: {reichelt_enhanced['overall']:.1f}%")
            print()
            
            if pascal_traditional and pascal_enhanced:
                print(f"Pascal Baum (technical degree):")
                print(f"  Traditional: {pascal_traditional['overall']:.1f}%")
                print(f"  Enhanced: {pascal_enhanced['overall']:.1f}%")
                print()
        
        # Check if enhanced approach improved Daniel's ranking
        daniel_trad_rank = next((i for i, r in enumerate(traditional_results, 1) if 'Daniel' in r['name']), None)
        daniel_enh_rank = next((i for i, r in enumerate(enhanced_results, 1) if 'Daniel' in r['name']), None)
        
        if daniel_trad_rank and daniel_enh_rank:
            print(f"Daniel's ranking improvement:")
            print(f"  Traditional: #{daniel_trad_rank}")
            print(f"  Enhanced: #{daniel_enh_rank}")
            if daniel_enh_rank < daniel_trad_rank:
                print("  ✅ Enhanced approach improved Daniel's ranking!")
            elif daniel_enh_rank == daniel_trad_rank:
                print("  ➡️ No change in ranking")
            else:
                print("  ❌ Enhanced approach made Daniel's ranking worse")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_both_approaches()
