#!/usr/bin/env python3
"""
Test Improved CV Extractor
Compare original vs improved extraction performance
"""

import os
import glob
from cv_extractor import CVDataExtractor
from improved_cv_extractor import ImprovedCVExtractor

class ExtractorComparisonTest:
    def __init__(self):
        self.original_extractor = CVDataExtractor()
        self.improved_extractor = ImprovedCVExtractor()
        self.all_fields = ['name', 'email', 'phone', 'experience', 'skills', 'education']
        
        # Test cases with known issues
        self.test_cases = [
            {
                'name': 'German CV with umlauts',
                'content': """
                Dr. <PERSON><PERSON><PERSON><PERSON>entwickler
                
                Kontakt:
                E-Mail: <EMAIL>
                Telefon: +49 (0)89 123-456789
                
                Berufserfahrung:
                5 Jahre Berufserfahrung in der Softwareentwicklung
                
                Ausbildung:
                Promotion in Informatik, TU München, 2010
                
                Kenntnisse:
                Java, Spring Boot, PostgreSQL
                """,
                'expected': {
                    'name': '<PERSON><PERSON><PERSON><PERSON>',
                    'email': '<EMAIL>',
                    'phone': '+49 (0)89 123-456789',
                    'experience': '5 Jahre',
                    'skills': 'Java',
                    'education': 'Promotion'
                }
            },
            {
                'name': 'English CV with complex name',
                'content': """
                Sarah O'<PERSON>-Williams
                Software Developer
                
                Contact:
                Email: <EMAIL>
                Phone: +****************
                
                Experience: 7+ years in software development
                
                Education:
                PhD Computer Science, MIT, 2015
                
                Skills: Python, Django, React, AWS
                """,
                'expected': {
                    'name': "Sarah O'Connor-Williams",
                    'email': '<EMAIL>',
                    'phone': '+****************',
                    'experience': '7 years',
                    'skills': 'Python',
                    'education': 'PhD'
                }
            },
            {
                'name': 'German minimal CV',
                'content': """
                Max Weber
                <EMAIL>
                030 12345678
                3 Jahre Erfahrung als Java Entwickler
                Informatik Studium
                """,
                'expected': {
                    'name': 'Max Weber',
                    'email': '<EMAIL>',
                    'phone': '030 12345678',
                    'experience': '3 Jahre',
                    'skills': 'Java',
                    'education': 'Informatik'
                }
            }
        ]

    def test_text_extraction(self):
        """Test extraction on text content"""
        print("🔍 COMPARING ORIGINAL VS IMPROVED EXTRACTOR")
        print("=" * 60)
        
        overall_improvements = 0
        total_tests = 0
        
        for test_case in self.test_cases:
            print(f"\n📄 Testing: {test_case['name']}")
            print("-" * 40)
            
            text_content = test_case['content']
            filename = f"test_{test_case['name'].replace(' ', '_')}.txt"
            
            # Test original extractor
            original_results = {}
            original_results['name'] = self.original_extractor.extract_name(text_content, filename)
            original_results['email'] = self.original_extractor.extract_email(text_content)
            original_results['phone'] = self.original_extractor.extract_phone(text_content)
            original_results['experience'] = self.original_extractor.extract_experience(text_content)
            original_results['skills'] = self.original_extractor.extract_skills(text_content)
            original_results['education'] = self.original_extractor.extract_education(text_content)
            
            # Test improved extractor
            improved_results = self.improved_extractor.extract_cv_data_from_text(text_content, filename)
            
            # Compare results
            for field in self.all_fields:
                expected = test_case['expected'].get(field, '')
                original = original_results.get(field, '')
                improved = improved_results.get(field, '')
                
                # Check if extraction was successful
                original_success = self.validate_extraction(field, expected, original)
                improved_success = self.validate_extraction(field, expected, improved)
                
                total_tests += 1
                
                if improved_success and not original_success:
                    overall_improvements += 1
                    status = "🔥 IMPROVED"
                elif improved_success and original_success:
                    status = "✅ BOTH GOOD"
                elif not improved_success and original_success:
                    status = "⚠️  REGRESSION"
                else:
                    status = "❌ BOTH FAILED"
                
                print(f"   {field}:")
                print(f"     Original: '{original}' {'✅' if original_success else '❌'}")
                print(f"     Improved: '{improved}' {'✅' if improved_success else '❌'}")
                print(f"     Expected: '{expected}'")
                print(f"     Result: {status}")
                print()
        
        improvement_rate = (overall_improvements / total_tests) * 100 if total_tests > 0 else 0
        print(f"📊 OVERALL IMPROVEMENT: {overall_improvements}/{total_tests} ({improvement_rate:.1f}%)")

    def test_existing_cvs(self):
        """Test on existing CV files"""
        print(f"\n📁 TESTING ON EXISTING CV FILES")
        print("=" * 40)
        
        cv_files = glob.glob("uploads/*.pdf")[:3]  # Test first 3 files
        
        for cv_file in cv_files:
            filename = os.path.basename(cv_file)
            print(f"\n📄 Testing: {filename}")
            
            try:
                # Original extractor
                original_results = self.original_extractor.extract_cv_data(cv_file, self.all_fields)
                
                # Improved extractor
                improved_results = self.improved_extractor.extract_cv_data(cv_file, self.all_fields)
                
                # Compare results
                improvements = 0
                for field in self.all_fields:
                    original = original_results.get(field, '')
                    improved = improved_results.get(field, '')
                    
                    # Check if improved version is better
                    original_failed = (original.endswith('not found') or 
                                     original.endswith('not specified') or 
                                     original == '')
                    improved_failed = (improved.endswith('not found') or 
                                     improved.endswith('not specified') or 
                                     improved == '')
                    
                    if original_failed and not improved_failed:
                        improvements += 1
                        status = "🔥"
                    elif not original_failed and not improved_failed:
                        status = "✅"
                    elif not original_failed and improved_failed:
                        status = "⚠️"
                    else:
                        status = "❌"
                    
                    print(f"   {status} {field}: '{improved}' (was: '{original}')")
                
                print(f"   Improvements: {improvements}/{len(self.all_fields)}")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")

    def validate_extraction(self, field: str, expected: str, extracted: str) -> bool:
        """Validate if extraction result is acceptable"""
        if not expected:  # No expectation set
            return True
        
        if not extracted or extracted.endswith('not found') or extracted.endswith('not specified'):
            return False
        
        expected_lower = expected.lower()
        extracted_lower = extracted.lower()
        
        if field == 'name':
            # For names, check if main parts are present
            expected_parts = expected_lower.split()
            return any(part in extracted_lower for part in expected_parts if len(part) > 2)
        
        elif field == 'email':
            # Email should be exact match or contain the expected email
            return expected_lower in extracted_lower
        
        elif field == 'phone':
            # Phone should contain the main digits
            expected_digits = ''.join(filter(str.isdigit, expected))
            extracted_digits = ''.join(filter(str.isdigit, extracted))
            return expected_digits in extracted_digits if expected_digits else True
        
        elif field == 'experience':
            # Experience should contain the number of years or level
            expected_numbers = ''.join(filter(str.isdigit, expected))
            extracted_numbers = ''.join(filter(str.isdigit, extracted))
            return (expected_numbers in extracted_numbers if expected_numbers else 
                   any(term in extracted_lower for term in expected_lower.split()))
        
        elif field == 'skills':
            # Skills should contain at least some of the expected skills
            expected_skills = [s.strip().lower() for s in expected.split(',')]
            return any(skill in extracted_lower for skill in expected_skills)
        
        elif field == 'education':
            # Education should contain key terms
            expected_terms = expected_lower.split()
            return any(term in extracted_lower for term in expected_terms if len(term) > 3)
        
        return True

    def performance_comparison(self):
        """Compare performance of both extractors"""
        print(f"\n⚡ PERFORMANCE COMPARISON")
        print("-" * 30)
        
        import time
        
        test_text = """
        Dr. Anna Müller-Schmidt
        Softwareentwicklerin
        E-Mail: <EMAIL>
        Telefon: +49 30 12345678
        5 Jahre Berufserfahrung
        Master Informatik
        Java, Spring Boot, PostgreSQL
        """
        
        # Test original extractor
        start_time = time.time()
        for _ in range(100):
            self.original_extractor.extract_name(test_text)
            self.original_extractor.extract_email(test_text)
            self.original_extractor.extract_phone(test_text)
            self.original_extractor.extract_experience(test_text)
            self.original_extractor.extract_skills(test_text)
            self.original_extractor.extract_education(test_text)
        original_time = time.time() - start_time
        
        # Test improved extractor
        start_time = time.time()
        for _ in range(100):
            self.improved_extractor.extract_cv_data_from_text(test_text)
        improved_time = time.time() - start_time
        
        print(f"Original extractor: {original_time:.3f}s (100 runs)")
        print(f"Improved extractor: {improved_time:.3f}s (100 runs)")
        
        if improved_time < original_time:
            print(f"✅ Improved extractor is {((original_time - improved_time) / original_time) * 100:.1f}% faster")
        else:
            print(f"⚠️  Improved extractor is {((improved_time - original_time) / original_time) * 100:.1f}% slower")

def main():
    """Run extractor comparison tests"""
    test_suite = ExtractorComparisonTest()
    
    test_suite.test_text_extraction()
    test_suite.test_existing_cvs()
    test_suite.performance_comparison()
    
    print(f"\n✅ Extractor comparison completed!")

if __name__ == "__main__":
    main()
