#!/usr/bin/env python3
"""
🎯 Test HR App Email System
===========================
Test email sending through the HR app's email service
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import the email service from the app
from email_service import EmailService

def test_hr_app_email():
    """Test email sending through HR app email service"""
    print("🎯 Testing HR App Email System")
    print("=" * 40)
    
    # Initialize email service exactly like the app does
    email_service = EmailService(
        mail_server=os.environ.get('SMTP_HOST'),
        mail_port=int(os.environ.get('SMTP_PORT', 587)),
        mail_username=os.environ.get('SMTP_USERNAME'),
        mail_password=os.environ.get('SMTP_PASSWORD'),
        mail_use_tls=True,
        default_sender=os.environ.get('DEFAULT_SENDER_EMAIL')
    )
    
    print(f"📧 Email Service Configuration:")
    print(f"   Server: {email_service.mail_server}")
    print(f"   Port: {email_service.mail_port}")
    print(f"   Username: {email_service.mail_username}")
    print(f"   Sender: {email_service.default_sender}")
    print(f"   TLS: {email_service.mail_use_tls}")
    print()
    
    # Test email content
    recipient = "<EMAIL>"
    subject = "🎉 BAUCH HR System - Email Test Successful!"
    
    body_html = """
    <html>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #2c5aa0;">🎉 BAUCH HR Management System</h2>
            <h3 style="color: #28a745;">Email System Test - SUCCESS!</h3>
            
            <p>Congratulations! Your BAUCH HR email system is now working perfectly.</p>
            
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h4 style="margin-top: 0; color: #495057;">✅ Test Results:</h4>
                <ul style="margin-bottom: 0;">
                    <li>✅ Email service configuration: <strong>Successful</strong></li>
                    <li>✅ SMTP connection: <strong>Successful</strong></li>
                    <li>✅ Authentication: <strong>Successful</strong></li>
                    <li>✅ Email delivery: <strong>Successful</strong></li>
                </ul>
            </div>
            
            <p><strong>What this means:</strong></p>
            <ul>
                <li>You can now send emails to job applicants</li>
                <li>Status updates can be sent automatically</li>
                <li>Bulk email campaigns are ready to use</li>
                <li>Interview invitations can be sent</li>
            </ul>
            
            <div style="background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h4 style="margin-top: 0; color: #0066cc;">🚀 Next Steps:</h4>
                <p style="margin-bottom: 0;">Your BAUCH HR Management System is ready for production use!</p>
            </div>
            
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #dee2e6;">
            
            <p style="color: #6c757d; font-size: 14px;">
                Best regards,<br>
                <strong>BAUCH HR Team</strong><br>
                <em>Professional HR Management System</em>
            </p>
        </div>
    </body>
    </html>
    """
    
    print(f"📤 Sending test email to: {recipient}")
    print("   Subject:", subject)
    print()
    
    # Send the email
    try:
        success = email_service.send_email(
            recipient=recipient,
            subject=subject,
            body_html=body_html
        )
        
        if success:
            print("🎉 SUCCESS! Email sent through HR app email service!")
            print("✅ Your BAUCH HR system can now send emails to applicants")
            print("🎯 Check your <NAME_EMAIL>")
            return True
        else:
            print("❌ Email sending failed")
            return False
            
    except Exception as e:
        print(f"❌ Error sending email: {e}")
        return False

def main():
    """Main function"""
    success = test_hr_app_email()
    
    if success:
        print("\n🎯 HR Email System Status: ✅ WORKING")
        print("You can now use the email features in your HR application!")
    else:
        print("\n❌ HR Email System Status: FAILED")
        print("Please check the configuration and try again.")

if __name__ == "__main__":
    main()
