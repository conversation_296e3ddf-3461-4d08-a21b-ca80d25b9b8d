#!/usr/bin/env python3
"""
Advanced LLM-based CV Matching System
Based on OpenAI's multi-agent RAG approach for domain-agnostic ranking
"""

import json
import re
import math
from typing import List, Dict, Tuple, Any
from dataclasses import dataclass
from collections import Counter
import os

# Optional OpenAI import
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    openai = None

@dataclass
class CandidateProfile:
    """Structured candidate profile extracted from CV"""
    name: str
    skills: List[str]
    experience_years: int
    education_level: str
    work_history: List[Dict[str, str]]
    certifications: List[str]
    domain_experience: List[str]

@dataclass
class JobRequirements:
    """Structured job requirements"""
    title: str
    required_skills: List[str]
    min_experience: int
    education_requirements: str
    domain: str
    seniority_level: str  # junior, mid, senior, leadership

class AdvancedLLMMatcher:
    """
    Advanced LLM-based matcher using multi-agent architecture
    Inspired by OpenAI's RAG-enhanced resume screening approach
    """
    
    def __init__(self, openai_api_key: str = None):
        """Initialize the advanced matcher"""
        self.openai_api_key = openai_api_key or os.getenv('OPENAI_API_KEY')
        if self.openai_api_key and OPENAI_AVAILABLE:
            openai.api_key = self.openai_api_key
        
        # Domain-specific knowledge bases
        self.domain_knowledge = {
            'software': {
                'key_skills': ['programming', 'software development', 'coding', 'algorithms'],
                'technologies': ['java', 'python', 'javascript', 'react', 'spring', 'database'],
                'experience_weight': 0.3,
                'skills_weight': 0.4,
                'education_weight': 0.3
            },
            'manufacturing': {
                'key_skills': ['cnc', 'machining', 'quality control', 'production', 'mechanical'],
                'technologies': ['fanuc', 'heidenhain', 'siemens', 'cam', 'cad', 'measurement'],
                'experience_weight': 0.5,  # Experience more important in manufacturing
                'skills_weight': 0.4,
                'education_weight': 0.1   # Less emphasis on formal education
            },
            'finance': {
                'key_skills': ['financial analysis', 'accounting', 'risk management', 'compliance'],
                'technologies': ['excel', 'sap', 'bloomberg', 'sql', 'python'],
                'experience_weight': 0.4,
                'skills_weight': 0.3,
                'education_weight': 0.3
            }
        }
    
    def extract_candidate_profile(self, cv_content: str) -> CandidateProfile:
        """Extract structured profile from CV using LLM"""
        if not self.openai_api_key or not OPENAI_AVAILABLE:
            return self._fallback_extraction(cv_content)
        
        prompt = f"""
        Extract structured information from this CV and return as JSON:
        
        CV Content:
        {cv_content}
        
        Extract:
        1. name: Full name of candidate
        2. skills: List of technical and professional skills
        3. experience_years: Total years of professional experience
        4. education_level: Highest education (high_school, vocational, bachelor, master, phd)
        5. work_history: List of jobs with company, role, duration
        6. certifications: Professional certifications
        7. domain_experience: Specific domain/industry experience
        
        Return only valid JSON format.
        """
        
        try:
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1
            )
            
            result = json.loads(response.choices[0].message.content)
            return CandidateProfile(**result)
            
        except Exception as e:
            print(f"LLM extraction failed: {e}")
            return self._fallback_extraction(cv_content)
    
    def _fallback_extraction(self, cv_content: str) -> CandidateProfile:
        """Fallback extraction without LLM"""
        # Simple regex-based extraction
        name_match = re.search(r'^([A-Z][a-z]+ [A-Z][a-z]+)', cv_content, re.MULTILINE)
        name = name_match.group(1) if name_match else "Unknown"
        
        # Extract skills (simple keyword matching)
        skills = []
        skill_keywords = ['java', 'python', 'cnc', 'programming', 'machining', 'quality']
        for skill in skill_keywords:
            if skill.lower() in cv_content.lower():
                skills.append(skill)
        
        # Extract experience years
        exp_match = re.search(r'(\d+)\+?\s*(?:years?|jahre?)', cv_content.lower())
        experience_years = int(exp_match.group(1)) if exp_match else 0
        
        return CandidateProfile(
            name=name,
            skills=skills,
            experience_years=experience_years,
            education_level="unknown",
            work_history=[],
            certifications=[],
            domain_experience=[]
        )
    
    def detect_job_domain(self, job_description: str) -> str:
        """Detect the domain/industry of the job"""
        job_lower = job_description.lower()
        
        # Manufacturing indicators
        manufacturing_keywords = ['cnc', 'machining', 'manufacturing', 'production', 'quality control', 
                                'mechanical', 'industrial', 'factory', 'assembly']
        
        # Software indicators  
        software_keywords = ['software', 'programming', 'developer', 'coding', 'application',
                           'web', 'mobile', 'database', 'api']
        
        # Finance indicators
        finance_keywords = ['financial', 'accounting', 'banking', 'investment', 'risk',
                          'compliance', 'audit', 'treasury']
        
        manufacturing_score = sum(1 for kw in manufacturing_keywords if kw in job_lower)
        software_score = sum(1 for kw in software_keywords if kw in job_lower)
        finance_score = sum(1 for kw in finance_keywords if kw in job_lower)
        
        if manufacturing_score > software_score and manufacturing_score > finance_score:
            return 'manufacturing'
        elif software_score > finance_score:
            return 'software'
        elif finance_score > 0:
            return 'finance'
        else:
            return 'general'
    
    def calculate_domain_specific_score(self, candidate: CandidateProfile, 
                                      job_requirements: JobRequirements) -> Dict[str, float]:
        """Calculate domain-specific matching scores"""
        domain = job_requirements.domain
        domain_config = self.domain_knowledge.get(domain, self.domain_knowledge['software'])
        
        # 1. Skills Match Score
        required_skills = [skill.lower() for skill in job_requirements.required_skills]
        candidate_skills = [skill.lower() for skill in candidate.skills]
        
        skill_matches = len(set(required_skills) & set(candidate_skills))
        skills_score = skill_matches / max(len(required_skills), 1)
        
        # Bonus for domain-specific skills
        domain_skills = domain_config['key_skills']
        domain_matches = sum(1 for skill in candidate_skills if any(ds in skill for ds in domain_skills))
        skills_score += domain_matches * 0.1  # 10% bonus per domain skill
        
        # 2. Experience Score
        exp_ratio = candidate.experience_years / max(job_requirements.min_experience, 1)
        experience_score = min(1.0, exp_ratio)  # Cap at 1.0
        
        # 3. Education Score
        education_levels = {'high_school': 1, 'vocational': 2, 'bachelor': 3, 'master': 4, 'phd': 5}
        candidate_edu_score = education_levels.get(candidate.education_level, 1)
        
        # Different domains value education differently
        if domain == 'manufacturing':
            # Vocational training often more valuable than degrees
            if candidate.education_level == 'vocational':
                education_score = 1.0
            else:
                education_score = min(1.0, candidate_edu_score / 3)
        else:
            education_score = min(1.0, candidate_edu_score / 4)
        
        return {
            'skills_score': min(1.0, skills_score),
            'experience_score': experience_score,
            'education_score': education_score,
            'domain_config': domain_config
        }
    
    def calculate_advanced_match_score(self, cv_content: str, job_description: str) -> Dict[str, Any]:
        """Calculate advanced matching score using multi-agent approach"""
        
        # Agent 1: Extract candidate profile
        candidate = self.extract_candidate_profile(cv_content)
        
        # Agent 2: Analyze job requirements
        domain = self.detect_job_domain(job_description)
        
        # Create job requirements (simplified for demo)
        job_requirements = JobRequirements(
            title="Job Title",
            required_skills=self._extract_required_skills(job_description),
            min_experience=self._extract_min_experience(job_description),
            education_requirements="bachelor",
            domain=domain,
            seniority_level="mid"
        )
        
        # Agent 3: Calculate domain-specific scores
        scores = self.calculate_domain_specific_score(candidate, job_requirements)
        
        # Agent 4: Weighted final score based on domain
        domain_config = scores['domain_config']
        final_score = (
            scores['skills_score'] * domain_config['skills_weight'] +
            scores['experience_score'] * domain_config['experience_weight'] +
            scores['education_score'] * domain_config['education_weight']
        )
        
        return {
            'final_score': final_score * 100,  # Convert to percentage
            'breakdown': {
                'skills': scores['skills_score'] * 100,
                'experience': scores['experience_score'] * 100,
                'education': scores['education_score'] * 100
            },
            'domain': domain,
            'candidate_profile': candidate,
            'explanation': self._generate_explanation(candidate, job_requirements, scores)
        }
    
    def _extract_required_skills(self, job_description: str) -> List[str]:
        """Extract required skills from job description"""
        # Simple keyword extraction (could be enhanced with LLM)
        skills = []
        skill_patterns = [
            r'(?:experience with|knowledge of|skills in)\s+([^.]+)',
            r'(?:proficiency in|expertise in)\s+([^.]+)',
            r'(?:required|must have):\s*([^.]+)'
        ]
        
        for pattern in skill_patterns:
            matches = re.findall(pattern, job_description, re.IGNORECASE)
            for match in matches:
                skills.extend([s.strip() for s in match.split(',')])
        
        return skills[:10]  # Limit to top 10
    
    def _extract_min_experience(self, job_description: str) -> int:
        """Extract minimum experience requirement"""
        exp_match = re.search(r'(\d+)\+?\s*(?:years?|jahre?)', job_description.lower())
        return int(exp_match.group(1)) if exp_match else 2
    
    def _generate_explanation(self, candidate: CandidateProfile, 
                            job_requirements: JobRequirements, scores: Dict) -> str:
        """Generate explanation for the matching score"""
        explanation = f"Candidate {candidate.name} evaluated for {job_requirements.domain} role:\n"
        explanation += f"- Skills match: {scores['skills_score']*100:.1f}%\n"
        explanation += f"- Experience: {candidate.experience_years} years vs {job_requirements.min_experience} required\n"
        explanation += f"- Education: {candidate.education_level}\n"
        explanation += f"- Domain: {job_requirements.domain} (optimized weighting applied)"
        
        return explanation

# Integration with existing system
class EnhancedCVMatcher:
    """Enhanced matcher that combines traditional and LLM approaches"""
    
    def __init__(self, openai_api_key: str = None):
        self.advanced_matcher = AdvancedLLMMatcher(openai_api_key)
        
    def calculate_enhanced_match_score(self, job_description: str, cv_content: str) -> float:
        """Calculate enhanced match score"""
        try:
            result = self.advanced_matcher.calculate_advanced_match_score(cv_content, job_description)
            return result['final_score']
        except Exception as e:
            print(f"Enhanced matching failed: {e}")
            # Fallback to basic matching
            return self._basic_fallback_score(job_description, cv_content)
    
    def _basic_fallback_score(self, job_description: str, cv_content: str) -> float:
        """Basic fallback scoring"""
        job_words = set(job_description.lower().split())
        cv_words = set(cv_content.lower().split())
        intersection = job_words.intersection(cv_words)
        return (len(intersection) / len(job_words)) * 100 if job_words else 0
