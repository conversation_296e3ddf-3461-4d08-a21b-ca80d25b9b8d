{% extends 'base.html' %}

{% block title %}Search Documents - HR Management System{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="display-5 mb-3">
                <i class="fas fa-search text-primary me-2"></i>Document Search
            </h1>
            <p class="lead">Search through all uploaded documents in the system.</p>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <form method="POST" action="{{ url_for('search_documents') }}">
                        <div class="input-group">
                            <input type="text" class="form-control form-control-lg" 
                                   name="query" placeholder="Enter search terms..." 
                                   value="{{ query }}" required>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Search
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    {% if query %}
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>Search Results for "{{ query }}"
                    </h5>
                </div>
                <div class="card-body">
                    {% if results %}
                        <div class="list-group">
                            {% for doc in results %}
                            <div class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h5 class="mb-1">{{ doc.filename }}</h5>
                                    <small class="text-muted">{{ doc.filetype|upper }}</small>
                                </div>
                                <p class="mb-1">{{ doc.content_preview }}</p>
                                <small class="text-muted">
                                    <i class="fas fa-file me-1"></i> {{ doc.filepath }}
                                </small>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>No documents found matching your search.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}