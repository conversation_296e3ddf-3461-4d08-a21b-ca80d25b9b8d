#!/usr/bin/env python3
"""
🔍 BAUCH HR - Email System Debug Tool
====================================
Debug and test email system configuration
"""

import os
import sys
import requests
from dotenv import load_dotenv

def test_listmonk_connection():
    """Test if Listmonk is running and accessible"""
    print("🔍 Testing Listmonk Connection...")
    print("-" * 40)
    
    listmonk_url = os.environ.get('LISTMONK_URL', 'http://localhost:9000')
    
    try:
        # Test basic connection
        response = requests.get(f"{listmonk_url}/api/health", timeout=5)
        print(f"✅ Listmonk is running!")
        print(f"   URL: {listmonk_url}")
        print(f"   Status: {response.status_code}")
        return True
    except requests.exceptions.ConnectionError:
        print(f"❌ Listmonk is not running on {listmonk_url}")
        print("   Please start Listmonk first:")
        print("   1. cd email_system")
        print("   2. ./listmonk --install (first time only)")
        print("   3. ./listmonk")
        return False
    except Exception as e:
        print(f"❌ Error connecting to Listmonk: {e}")
        return False

def test_basic_email_config():
    """Test basic email configuration"""
    print("\n📧 Testing Basic Email Configuration...")
    print("-" * 40)
    
    smtp_host = os.environ.get('SMTP_HOST')
    smtp_port = os.environ.get('SMTP_PORT')
    smtp_user = os.environ.get('SMTP_USERNAME')
    smtp_pass = os.environ.get('SMTP_PASSWORD')
    
    print(f"SMTP Host: {smtp_host}")
    print(f"SMTP Port: {smtp_port}")
    print(f"SMTP User: {smtp_user}")
    print(f"SMTP Pass: {'*' * len(smtp_pass) if smtp_pass else 'Not set'}")
    
    if all([smtp_host, smtp_port, smtp_user, smtp_pass]):
        print("✅ Basic email configuration looks complete")
        return True
    else:
        print("❌ Basic email configuration is incomplete")
        return False

def test_email_service_import():
    """Test if email services can be imported"""
    print("\n🔧 Testing Email Service Imports...")
    print("-" * 40)
    
    try:
        from listmonk_integration import ListmonkEmailService
        print("✅ Listmonk integration can be imported")
        listmonk_available = True
    except ImportError as e:
        print(f"❌ Listmonk integration import failed: {e}")
        listmonk_available = False
    
    try:
        from email_service import EmailService
        print("✅ Basic email service can be imported")
        basic_available = True
    except ImportError as e:
        print(f"❌ Basic email service import failed: {e}")
        basic_available = False
    
    return listmonk_available, basic_available

def test_app_email_detection():
    """Test how the app detects email service"""
    print("\n🎯 Testing App Email Service Detection...")
    print("-" * 40)
    
    try:
        # Simulate app's email service detection logic
        try:
            from listmonk_integration import ListmonkEmailService
            email_service_type = "listmonk"
            print("✅ App would use Listmonk email service")
        except ImportError:
            email_service_type = "basic"
            print("✅ App would use basic email service")
        
        print(f"   Detected service type: {email_service_type}")
        return email_service_type
    except Exception as e:
        print(f"❌ Error in email service detection: {e}")
        return None

def main():
    """Main debug function"""
    print("🚀 BAUCH HR Email System Debug Tool")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Run tests
    listmonk_running = test_listmonk_connection()
    basic_config_ok = test_basic_email_config()
    listmonk_import, basic_import = test_email_service_import()
    detected_service = test_app_email_detection()
    
    # Summary
    print("\n📋 Summary")
    print("=" * 20)
    print(f"Listmonk Running: {'✅' if listmonk_running else '❌'}")
    print(f"Basic Config OK: {'✅' if basic_config_ok else '❌'}")
    print(f"Listmonk Import: {'✅' if listmonk_import else '❌'}")
    print(f"Basic Import: {'✅' if basic_import else '❌'}")
    print(f"Detected Service: {detected_service}")
    
    # Recommendations
    print("\n💡 Recommendations")
    print("-" * 20)
    
    if not listmonk_running and listmonk_import:
        print("🔧 To use Listmonk:")
        print("   1. Install PostgreSQL")
        print("   2. cd email_system")
        print("   3. ./listmonk --install")
        print("   4. ./listmonk")
    
    if basic_config_ok and basic_import:
        print("📧 Basic email service is ready to use")
        print("   Your app will work with basic SMTP email sending")
    
    if not basic_config_ok:
        print("⚠️  Please check your .env file email configuration")

if __name__ == "__main__":
    main()
