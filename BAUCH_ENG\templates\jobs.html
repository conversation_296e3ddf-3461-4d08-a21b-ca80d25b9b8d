{% extends 'base.html' %}

{% block title %}Jobs - HR Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-briefcase me-2"></i>Job Board
    </h1>
    <a href="{{ url_for('add_job') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i> Add New Job
    </a>
</div>

{% if jobs %}
    <div class="card">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Job Title</th>
                            <th>Platform</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Days Remaining</th>
                            <th>Status</th>
                            <th>Responsible Person</th>
                            <th>CVs</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for job in jobs %}
                        <tr>
                            <td>
                                <a href="{{ url_for('job_detail', job_title=job.title) }}" class="text-decoration-none">
                                    <strong class="text-primary">{{ job.title }}</strong>
                                </a>
                                <br>
                                <small class="text-muted">{{ job.description|truncate(50) }}</small>
                            </td>
                            <td>
                                {% if job.platform %}
                                    {% if job.job_url %}
                                        <a href="{{ job.job_url }}" target="_blank" class="text-decoration-none">
                                            <span class="badge bg-secondary">
                                                {{ job.platform }} <i class="fas fa-external-link-alt ms-1"></i>
                                            </span>
                                        </a>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ job.platform }}</span>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">—</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if job.start_date %}
                                    {{ job.start_date }}
                                {% else %}
                                    <span class="text-muted">—</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if job.end_date %}
                                    {{ job.end_date }}
                                {% else %}
                                    <span class="text-muted">—</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if job.days_remaining is not none %}
                                    {% if job.days_remaining > 0 %}
                                        <span class="text-success">{{ job.days_remaining }}</span>
                                    {% elif job.days_remaining == 0 %}
                                        <span class="text-warning">Today</span>
                                    {% else %}
                                        <span class="text-danger">{{ job.days_remaining }}</span>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">∞</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if job.status == 'Active' %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-circle me-1"></i>{{ job.status }}
                                    </span>
                                {% elif job.status == 'Expired' %}
                                    <span class="badge bg-danger">
                                        <i class="fas fa-circle me-1"></i>{{ job.status }}
                                    </span>
                                {% elif job.status == 'Ending Soon' %}
                                    <span class="badge bg-warning">
                                        <i class="fas fa-circle me-1"></i>{{ job.status }}
                                    </span>
                                {% elif job.status == 'Open Pool' %}
                                    <span class="badge bg-info">
                                        <i class="fas fa-circle me-1"></i>{{ job.status }}
                                    </span>
                                {% else %}
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-circle me-1"></i>{{ job.status }}
                                    </span>
                                {% endif %}
                            </td>
                            <td>{{ job.main_responsible_person }}</td>
                            <td>
                                <span class="badge bg-primary">{{ job.cv_count }}</span>
                                {% if job.cv_count > 0 %}
                                    <div class="btn-group ms-2" role="group">
                                        <a href="{{ url_for('match') }}?job={{ job.title }}" class="btn btn-sm btn-success" title="Match CVs">
                                            <i class="fas fa-check-circle"></i>
                                        </a>
                                        <button class="btn btn-sm btn-info" onclick="showExtractModal('{{ job.title }}', {{ job.cv_count }})" title="Extract to Excel">
                                            <i class="fas fa-file-excel"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ loop.index }}" title="Delete Job ({{ job.cv_count }} CVs)">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                {% else %}
                                    <button class="btn btn-sm btn-danger ms-2" data-bs-toggle="modal" data-bs-target="#deleteModal{{ loop.index }}" title="Delete Job">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                {% endif %}
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li>
                                            <a class="dropdown-item" href="{{ url_for('cvs', job=job.title) }}">
                                                <i class="fas fa-file-alt me-1"></i> View CVs
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="{{ url_for('upload_cv', job=job.title) }}">
                                                <i class="fas fa-upload me-1"></i> Upload CV
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="{{ url_for('match') }}?job={{ job.title }}">
                                                <i class="fas fa-check-circle me-1"></i> Match CVs
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item text-danger" href="#" data-bs-toggle="modal" data-bs-target="#deleteModal{{ loop.index }}">
                                                <i class="fas fa-trash-alt me-1"></i> Delete Job
                                                {% if job.cv_count > 0 %}
                                                    <i class="fas fa-exclamation-triangle ms-1 text-warning" title="This job has {{ job.cv_count }} CVs"></i>
                                                {% endif %}
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modals -->
    {% for job in jobs %}
    <div class="modal fade" id="deleteModal{{ loop.index }}" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Deletion</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the job <strong>{{ job.title }}</strong>?</p>
                    {% if job.cv_count > 0 %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Warning:</strong> This job has <strong>{{ job.cv_count }} CV{{ 's' if job.cv_count != 1 }}</strong> associated with it.
                        </div>
                        <p class="text-danger">
                            <i class="fas fa-trash-alt me-2"></i>
                            All CVs and candidate data for this job will be permanently deleted.
                        </p>
                    {% else %}
                        <p class="text-muted">This job has no CVs associated with it.</p>
                    {% endif %}
                    <p class="text-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <strong>This action cannot be undone.</strong>
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <a href="{{ url_for('delete_job', job_title=job.title) }}" class="btn btn-danger">
                        <i class="fas fa-trash-alt me-1"></i>
                        {% if job.cv_count > 0 %}
                            Delete Job & {{ job.cv_count }} CV{{ 's' if job.cv_count != 1 }}
                        {% else %}
                            Delete Job
                        {% endif %}
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}

    <!-- Extract to Excel Modal -->
    <div class="modal fade" id="extractModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-file-excel me-2"></i>Extract CVs to Excel
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Extract candidate data from CVs for job: <strong id="extractJobTitle"></strong></p>
                    <p class="text-muted">Total CVs available: <span id="extractTotalCVs"></span></p>

                    <div class="mb-3">
                        <label for="extractCount" class="form-label">Number of candidates to extract:</label>
                        <select class="form-select" id="extractCount">
                            <option value="5">Top 5 candidates</option>
                            <option value="10">Top 10 candidates</option>
                            <option value="15">Top 15 candidates</option>
                            <option value="all">All candidates</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Data to extract:</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="extractName" checked>
                            <label class="form-check-label" for="extractName">Name</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="extractEmail" checked>
                            <label class="form-check-label" for="extractEmail">Email</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="extractPhone" checked>
                            <label class="form-check-label" for="extractPhone">Phone</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="extractExperience" checked>
                            <label class="form-check-label" for="extractExperience">Experience</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="extractSkills" checked>
                            <label class="form-check-label" for="extractSkills">Skills</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="extractEducation">
                            <label class="form-check-label" for="extractEducation">Education</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-success" onclick="extractToExcel()">
                        <i class="fas fa-download me-1"></i>Download Excel
                    </button>
                </div>
            </div>
        </div>
    </div>

{% else %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i> No jobs found. Click the "Add New Job" button to create your first job posting.
    </div>
{% endif %}

<script>
let currentJobTitle = '';

function showExtractModal(jobTitle, cvCount) {
    currentJobTitle = jobTitle;
    document.getElementById('extractJobTitle').textContent = jobTitle;
    document.getElementById('extractTotalCVs').textContent = cvCount;

    // Update the select options based on available CVs
    const selectElement = document.getElementById('extractCount');
    selectElement.innerHTML = '';

    if (cvCount >= 5) {
        selectElement.innerHTML += '<option value="5">Top 5 candidates</option>';
    }
    if (cvCount >= 10) {
        selectElement.innerHTML += '<option value="10">Top 10 candidates</option>';
    }
    if (cvCount >= 15) {
        selectElement.innerHTML += '<option value="15">Top 15 candidates</option>';
    }
    selectElement.innerHTML += '<option value="all">All ' + cvCount + ' candidates</option>';

    new bootstrap.Modal(document.getElementById('extractModal')).show();
}

function extractToExcel() {
    const count = document.getElementById('extractCount').value;
    const fields = [];

    if (document.getElementById('extractName').checked) fields.push('name');
    if (document.getElementById('extractEmail').checked) fields.push('email');
    if (document.getElementById('extractPhone').checked) fields.push('phone');
    if (document.getElementById('extractExperience').checked) fields.push('experience');
    if (document.getElementById('extractSkills').checked) fields.push('skills');
    if (document.getElementById('extractEducation').checked) fields.push('education');

    if (fields.length === 0) {
        alert('Please select at least one field to extract.');
        return;
    }

    // Create download URL
    const params = new URLSearchParams({
        job: currentJobTitle,
        count: count,
        fields: fields.join(',')
    });

    // Trigger download
    window.location.href = '/extract-excel?' + params.toString();

    // Close modal
    bootstrap.Modal.getInstance(document.getElementById('extractModal')).hide();
}
</script>

{% endblock %}
