#!/usr/bin/env python3
"""
Test CV Field Extraction on Existing CV Files
Tests extraction accuracy on real PDF and DOCX files
"""

import os
import glob
import json
from typing import Dict, List
from cv_extractor import CVDataExtractor
try:
    from bilingual_cv_extractor import BilingualCVExtractor
    BILINGUAL_AVAILABLE = True
except ImportError:
    BILINGUAL_AVAILABLE = False

class ExistingCVExtractionTest:
    def __init__(self):
        self.basic_extractor = CVDataExtractor()
        if BILINGUAL_AVAILABLE:
            self.bilingual_extractor = BilingualCVExtractor()
        
        self.all_fields = ['name', 'email', 'phone', 'experience', 'skills', 'education']
        
        # Expected results for known CV files (based on filenames and content)
        self.expected_results = {
            'CV_Maria_Schmidt_80.pdf': {
                'name': '<PERSON>',
                'email_contains': '@',
                'phone_contains': '+49',
                'experience_contains': 'year',
                'skills_contains': 'java',
                'education_contains': 'informatik'
            },
            'CV_<PERSON>_Mueller_20.pdf': {
                'name': '<PERSON>',
                'email_contains': '@',
                'phone_contains': '+49',
                'experience_contains': 'year',
                'skills_contains': 'windows',
                'education_contains': 'fachinformatiker'
            },
            'Emma_Brooks_CV.pdf': {
                'name': 'Emma Brooks',
                'email_contains': '@',
                'phone_contains': '+',
                'experience_contains': 'year',
                'skills_contains': 'project',
                'education_contains': 'degree'
            },
            'Taha_Mughal_CV_Tailored.pdf': {
                'name': 'Taha Mughal',
                'email_contains': '@',
                'phone_contains': '+',
                'experience_contains': 'year',
                'skills_contains': 'python',
                'education_contains': 'computer'
            }
        }

    def test_existing_cvs_basic_extractor(self):
        """Test basic extractor on existing CV files"""
        print("🔍 TESTING BASIC EXTRACTOR ON EXISTING CVS")
        print("=" * 50)
        
        cv_files = glob.glob("uploads/*.pdf") + glob.glob("uploads/*.docx")
        
        if not cv_files:
            print("❌ No CV files found in uploads directory")
            return
        
        results = []
        
        for cv_file in cv_files:
            filename = os.path.basename(cv_file)
            print(f"\n📄 Testing: {filename}")
            
            try:
                # Extract all fields
                extracted_data = self.basic_extractor.extract_cv_data(cv_file, self.all_fields)
                
                # Show extracted content preview
                text_content = self.basic_extractor.extract_text_from_file(cv_file)
                content_preview = text_content[:200] + "..." if len(text_content) > 200 else text_content
                
                print(f"   Content preview: {content_preview}")
                print(f"   Extracted fields:")
                
                field_results = {}
                for field in self.all_fields:
                    value = extracted_data.get(field, 'Not extracted')
                    print(f"     {field}: {value}")
                    field_results[field] = value
                
                # Validate against expected results if available
                if filename in self.expected_results:
                    validation_results = self.validate_against_expected(field_results, self.expected_results[filename])
                    print(f"   Validation:")
                    for field, result in validation_results.items():
                        status = "✅" if result['valid'] else "❌"
                        print(f"     {status} {field}: {result['reason']}")
                
                results.append({
                    'filename': filename,
                    'extracted_data': field_results,
                    'content_preview': content_preview
                })
                
            except Exception as e:
                print(f"   ❌ Error extracting from {filename}: {e}")
                results.append({
                    'filename': filename,
                    'error': str(e)
                })
        
        return results

    def test_existing_cvs_bilingual_extractor(self):
        """Test bilingual extractor on existing CV files"""
        if not BILINGUAL_AVAILABLE:
            print("⚠️  Bilingual extractor not available")
            return []
        
        print(f"\n🌍 TESTING BILINGUAL EXTRACTOR ON EXISTING CVS")
        print("=" * 50)
        
        cv_files = glob.glob("uploads/*.pdf") + glob.glob("uploads/*.docx")
        results = []
        
        for cv_file in cv_files:
            filename = os.path.basename(cv_file)
            print(f"\n📄 Testing: {filename}")
            
            try:
                # Extract all fields using bilingual extractor
                extracted_data = self.bilingual_extractor.extract_cv_data_bilingual(cv_file, self.all_fields)
                
                print(f"   Language info:")
                if 'language_detected' in extracted_data:
                    print(f"     Detected language: {extracted_data.get('language_detected', 'Unknown')}")
                    print(f"     Confidence: {extracted_data.get('language_confidence', 'Unknown')}")
                    print(f"     Is bilingual: {extracted_data.get('is_bilingual', 'Unknown')}")
                
                print(f"   Extracted fields:")
                field_results = {}
                for field in self.all_fields:
                    value = extracted_data.get(field, 'Not extracted')
                    print(f"     {field}: {value}")
                    field_results[field] = value
                
                # Validate against expected results if available
                if filename in self.expected_results:
                    validation_results = self.validate_against_expected(field_results, self.expected_results[filename])
                    print(f"   Validation:")
                    for field, result in validation_results.items():
                        status = "✅" if result['valid'] else "❌"
                        print(f"     {status} {field}: {result['reason']}")
                
                results.append({
                    'filename': filename,
                    'extracted_data': field_results,
                    'language_info': {
                        'detected': extracted_data.get('language_detected'),
                        'confidence': extracted_data.get('language_confidence'),
                        'bilingual': extracted_data.get('is_bilingual')
                    }
                })
                
            except Exception as e:
                print(f"   ❌ Error extracting from {filename}: {e}")
                results.append({
                    'filename': filename,
                    'error': str(e)
                })
        
        return results

    def validate_against_expected(self, extracted: Dict, expected: Dict) -> Dict:
        """Validate extracted data against expected patterns"""
        results = {}
        
        for field, expected_value in expected.items():
            if field.endswith('_contains'):
                # Check if extracted field contains expected substring
                base_field = field.replace('_contains', '')
                extracted_value = extracted.get(base_field, '').lower()
                expected_substring = expected_value.lower()
                
                if expected_substring in extracted_value:
                    results[base_field] = {'valid': True, 'reason': f"Contains '{expected_value}'"}
                else:
                    results[base_field] = {'valid': False, 'reason': f"Missing '{expected_value}'"}
            else:
                # Direct comparison
                extracted_value = extracted.get(field, '').lower()
                expected_lower = expected_value.lower()
                
                if expected_lower in extracted_value:
                    results[field] = {'valid': True, 'reason': f"Matches '{expected_value}'"}
                else:
                    results[field] = {'valid': False, 'reason': f"Expected '{expected_value}', got '{extracted.get(field, 'None')}'"}
        
        return results

    def test_extraction_consistency(self):
        """Test extraction consistency across multiple runs"""
        print(f"\n🔄 TESTING EXTRACTION CONSISTENCY")
        print("-" * 35)
        
        cv_files = glob.glob("uploads/*.pdf")[:2]  # Test first 2 files
        
        for cv_file in cv_files:
            filename = os.path.basename(cv_file)
            print(f"\n📄 Testing consistency for: {filename}")
            
            # Run extraction 3 times
            results = []
            for i in range(3):
                try:
                    extracted_data = self.basic_extractor.extract_cv_data(cv_file, self.all_fields)
                    results.append(extracted_data)
                except Exception as e:
                    print(f"   ❌ Error in run {i+1}: {e}")
            
            # Check consistency
            if len(results) >= 2:
                consistent = True
                for field in self.all_fields:
                    values = [result.get(field, '') for result in results]
                    if len(set(values)) > 1:  # Different values found
                        consistent = False
                        print(f"   ❌ {field}: Inconsistent results {values}")
                    else:
                        print(f"   ✅ {field}: Consistent")
                
                if consistent:
                    print(f"   ✅ All fields consistent across runs")
            else:
                print(f"   ⚠️  Not enough successful runs to test consistency")

    def test_field_specific_issues(self):
        """Test for specific field extraction issues"""
        print(f"\n🔍 TESTING FIELD-SPECIFIC ISSUES")
        print("-" * 35)
        
        # Test German name extraction
        german_names_test = """
        Dr. Klaus-Dieter Müller-Schmidt
        Straße: Müllerstraße 123
        E-Mail: <EMAIL>
        """
        
        print(f"German name extraction test:")
        temp_file = "temp_german_name_test.txt"
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write(german_names_test)
        
        try:
            result = self.basic_extractor.extract_cv_data(temp_file, ['name'])
            name = result.get('name', '')
            if 'Klaus' in name and 'Müller' in name:
                print(f"   ✅ German name: {name}")
            else:
                print(f"   ❌ German name: {name} (expected Klaus-Dieter Müller-Schmidt)")
        finally:
            if os.path.exists(temp_file):
                os.remove(temp_file)
        
        # Test German phone extraction
        german_phone_test = """
        Telefon: +49 (0)89 123-456789
        Mobil: 0171 9876543
        """
        
        print(f"German phone extraction test:")
        temp_file = "temp_german_phone_test.txt"
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write(german_phone_test)
        
        try:
            result = self.basic_extractor.extract_cv_data(temp_file, ['phone'])
            phone = result.get('phone', '')
            if '+49' in phone or '089' in phone or '0171' in phone:
                print(f"   ✅ German phone: {phone}")
            else:
                print(f"   ❌ German phone: {phone} (expected German format)")
        finally:
            if os.path.exists(temp_file):
                os.remove(temp_file)
        
        # Test German experience extraction
        german_experience_test = """
        Berufserfahrung: 5 Jahre Softwareentwicklung
        2018-heute: Senior Java Entwickler
        """
        
        print(f"German experience extraction test:")
        temp_file = "temp_german_exp_test.txt"
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write(german_experience_test)
        
        try:
            result = self.basic_extractor.extract_cv_data(temp_file, ['experience'])
            experience = result.get('experience', '')
            if '5' in experience or 'Senior' in experience:
                print(f"   ✅ German experience: {experience}")
            else:
                print(f"   ❌ German experience: {experience} (expected 5 years or Senior)")
        finally:
            if os.path.exists(temp_file):
                os.remove(temp_file)

def main():
    """Run existing CV extraction tests"""
    print("🚀 EXISTING CV FIELD EXTRACTION TEST SUITE")
    print("=" * 60)
    
    test_suite = ExistingCVExtractionTest()
    
    # Test basic extractor
    basic_results = test_suite.test_existing_cvs_basic_extractor()
    
    # Test bilingual extractor
    bilingual_results = test_suite.test_existing_cvs_bilingual_extractor()
    
    # Test consistency
    test_suite.test_extraction_consistency()
    
    # Test field-specific issues
    test_suite.test_field_specific_issues()
    
    print(f"\n✅ Existing CV extraction testing completed!")
    print(f"📊 Tested {len(basic_results)} CV files with basic extractor")
    if bilingual_results:
        print(f"📊 Tested {len(bilingual_results)} CV files with bilingual extractor")

if __name__ == "__main__":
    main()
