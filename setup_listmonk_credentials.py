#!/usr/bin/env python3
"""
🔐 BAUCH HR - Listmonk Credentials Setup
========================================
Simple script to set up your Listmonk email credentials
"""

import os
import getpass
from pathlib import Path

def main():
    print("🚀 BAUCH HR Management System - Listmonk Setup")
    print("=" * 60)
    print("This script will help you configure your Listmonk email credentials.")
    print()
    
    # Check if .env file exists
    env_file = Path('.env')
    if not env_file.exists():
        print("❌ .env file not found. Please run setup_secure_email.py first.")
        return
    
    print("📧 Enter your Listmonk email credentials:")
    print("-" * 40)
    
    # Get email credentials
    email = input("📧 Your email address: ").strip()
    if not email:
        print("❌ Email address is required!")
        return
    
    print("\n🔑 Email password/app password:")
    print("   For Gmail: Use App Password (not your regular password)")
    print("   For Outlook: Use your regular password or app password")
    password = getpass.getpass("🔐 Password: ").strip()
    if not password:
        print("❌ Password is required!")
        return
    
    # Optional: Listmonk admin credentials
    print("\n🔧 Listmonk Admin Credentials (optional - press Enter to use defaults):")
    listmonk_user = input("👤 Admin username (default: admin): ").strip() or "admin"
    listmonk_pass = getpass.getpass("🔐 Admin password (default: auto-generated): ").strip()
    
    # Update .env file
    try:
        # Read existing .env file
        with open('.env', 'r') as f:
            lines = f.readlines()
        
        # Update email credentials
        updated_lines = []
        for line in lines:
            if line.startswith('SMTP_USERNAME='):
                updated_lines.append(f'SMTP_USERNAME={email}\n')
            elif line.startswith('SMTP_PASSWORD='):
                updated_lines.append(f'SMTP_PASSWORD={password}\n')
            elif line.startswith('DEFAULT_SENDER_EMAIL='):
                updated_lines.append(f'DEFAULT_SENDER_EMAIL={email}\n')
            elif line.startswith('LISTMONK_USERNAME='):
                updated_lines.append(f'LISTMONK_USERNAME={listmonk_user}\n')
            elif line.startswith('LISTMONK_PASSWORD=') and listmonk_pass:
                updated_lines.append(f'LISTMONK_PASSWORD={listmonk_pass}\n')
            else:
                updated_lines.append(line)
        
        # Write updated .env file
        with open('.env', 'w') as f:
            f.writelines(updated_lines)
        
        print("\n✅ Credentials updated successfully!")
        print()
        print("🎯 Next steps:")
        print("1. Start Listmonk: cd email_system && ./listmonk")
        print("2. Access web interface: http://localhost:9000")
        print(f"3. Login with username: {listmonk_user}")
        print("4. Configure SMTP settings in Listmonk admin panel")
        print()
        print("🔒 Security reminder:")
        print("   Your credentials are stored securely in .env file")
        print("   Never share or commit this file to version control!")
        
    except Exception as e:
        print(f"❌ Error updating credentials: {e}")
        return

if __name__ == "__main__":
    main()
