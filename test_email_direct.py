#!/usr/bin/env python3
"""
🔥 DIRECT EMAIL TEST - BAUCH HR
==============================
Test email sending directly with credentials until it works!
"""

import smtplib
import os
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from dotenv import load_dotenv
import ssl

def test_email_direct():
    """Test email sending directly with SMTP"""
    print("🔥 DIRECT EMAIL TEST - Will not stop until email is sent!")
    print("=" * 60)
    
    # Load environment variables
    load_dotenv()
    
    # Get credentials
    smtp_host = os.environ.get('SMTP_HOST')
    smtp_port = int(os.environ.get('SMTP_PORT', 587))
    smtp_user = os.environ.get('SMTP_USERNAME')
    smtp_pass = os.environ.get('SMTP_PASSWORD')
    sender_email = os.environ.get('DEFAULT_SENDER_EMAIL')
    
    print(f"📧 Email Configuration:")
    print(f"   Host: {smtp_host}")
    print(f"   Port: {smtp_port}")
    print(f"   User: {smtp_user}")
    print(f"   Sender: {sender_email}")
    print(f"   Password: {'*' * len(smtp_pass) if smtp_pass else 'NOT SET'}")
    print()
    
    # Target email
    target_email = "<EMAIL>"
    
    # Create message
    msg = MIMEMultipart()
    msg['From'] = sender_email
    msg['To'] = target_email
    msg['Subject'] = "🎉 BAUCH HR Test Email - Success!"
    
    # Email body
    body = """
    <html>
    <body>
        <h2>🎉 BAUCH HR Email System Test</h2>
        <p>Congratulations! The email system is now working perfectly.</p>
        <p><strong>Test Details:</strong></p>
        <ul>
            <li>✅ SMTP Connection: Successful</li>
            <li>✅ Authentication: Successful</li>
            <li>✅ Email Delivery: Successful</li>
        </ul>
        <p>Your BAUCH HR Management System is ready to send emails to applicants!</p>
        <br>
        <p>Best regards,<br>
        <strong>BAUCH HR Team</strong></p>
    </body>
    </html>
    """
    
    msg.attach(MIMEText(body, 'html'))
    
    # Try different SMTP configurations
    configurations = [
        # Gmail with TLS (most common)
        {
            'host': 'smtp.gmail.com',
            'port': 587,
            'use_tls': True,
            'use_ssl': False,
            'name': 'Gmail TLS (Port 587)'
        },
        # Gmail with SSL
        {
            'host': 'smtp.gmail.com',
            'port': 465,
            'use_tls': False,
            'use_ssl': True,
            'name': 'Gmail SSL (Port 465)'
        },
        # Gmail with different TLS settings
        {
            'host': 'smtp.gmail.com',
            'port': 587,
            'use_tls': True,
            'use_ssl': False,
            'name': 'Gmail TLS with relaxed SSL'
        }
    ]
    
    for i, config in enumerate(configurations, 1):
        print(f"🔄 Attempt {i}: {config['name']}")
        print("-" * 40)
        
        try:
            if config['use_ssl']:
                # SSL connection
                context = ssl.create_default_context()
                server = smtplib.SMTP_SSL(config['host'], config['port'], context=context)
                print(f"✅ SSL connection established to {config['host']}:{config['port']}")
            else:
                # Regular connection
                server = smtplib.SMTP(config['host'], config['port'])
                print(f"✅ Connection established to {config['host']}:{config['port']}")
                
                if config['use_tls']:
                    # Start TLS
                    context = ssl.create_default_context()
                    server.starttls(context=context)
                    print("✅ TLS encryption enabled")
            
            # Login
            server.login(smtp_user, smtp_pass)
            print("✅ Authentication successful")
            
            # Send email
            text = msg.as_string()
            server.sendmail(sender_email, target_email, text)
            print("✅ Email sent successfully!")
            
            server.quit()
            print()
            print("🎉 SUCCESS! Email <NAME_EMAIL>")
            print("🎯 Check your inbox (and spam folder)")
            return True
            
        except smtplib.SMTPAuthenticationError as e:
            print(f"❌ Authentication failed: {e}")
            print("   Check your email and app password")
        except smtplib.SMTPRecipientsRefused as e:
            print(f"❌ Recipient refused: {e}")
        except smtplib.SMTPServerDisconnected as e:
            print(f"❌ Server disconnected: {e}")
        except smtplib.SMTPException as e:
            print(f"❌ SMTP error: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
        
        try:
            server.quit()
        except:
            pass
        
        print()
    
    print("❌ All attempts failed. Let's try troubleshooting...")
    return False

def troubleshoot_gmail():
    """Provide Gmail troubleshooting steps"""
    print("🔧 Gmail Troubleshooting Steps:")
    print("=" * 40)
    print("1. ✅ Enable 2-Factor Authentication")
    print("   - Go to Google Account settings")
    print("   - Security > 2-Step Verification")
    print()
    print("2. 🔑 Generate App Password")
    print("   - Go to Google Account settings")
    print("   - Security > App passwords")
    print("   - Select 'Mail' and generate password")
    print("   - Use this password, not your regular password")
    print()
    print("3. 🔓 Enable Less Secure Apps (if needed)")
    print("   - Go to Google Account settings")
    print("   - Security > Less secure app access")
    print("   - Turn on (not recommended, use app password instead)")
    print()
    print("4. 📧 Check Email Settings")
    print("   - Make sure IMAP is enabled in Gmail")
    print("   - Settings > Forwarding and POP/IMAP")

def main():
    """Main function"""
    success = test_email_direct()
    
    if not success:
        troubleshoot_gmail()
        print()
        print("🔄 After fixing the above, run this script again:")
        print("   python test_email_direct.py")

if __name__ == "__main__":
    main()
