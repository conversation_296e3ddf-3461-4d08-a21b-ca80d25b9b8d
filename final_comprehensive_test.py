#!/usr/bin/env python3
"""
Final Comprehensive CV Matching Test
Complete validation of the German/English CV matching system
"""

import os
import glob
import time
from typing import Dict, List
from matcher import CVMatcher
from cv_extractor import CVDataExtractor

class FinalTestSuite:
    def __init__(self):
        self.matcher = CVMatcher()
        self.extractor = CVDataExtractor()
        self.test_results = []

    def run_all_tests(self):
        """Run all comprehensive tests"""
        print("🚀 FINAL COMPREHENSIVE CV MATCHING TEST SUITE")
        print("=" * 60)
        
        # Test 1: Basic functionality
        self.test_basic_functionality()
        
        # Test 2: Language detection and preference
        self.test_language_capabilities()
        
        # Test 3: Accuracy validation
        self.test_accuracy_validation()
        
        # Test 4: Performance validation
        self.test_performance()
        
        # Test 5: Edge cases
        self.test_edge_cases()
        
        # Final summary
        self.print_final_summary()

    def test_basic_functionality(self):
        """Test basic CV matching functionality"""
        print(f"\n1️⃣ BASIC FUNCTIONALITY TEST")
        print("-" * 35)
        
        # Load test CVs
        test_cases = [
            {
                'name': 'German Excellent Java CV',
                'cv_file': 'test_cvs/german_cv_excellent_match.txt',
                'job': 'Java Developer (German)',
                'expected_score_range': (80, 100)
            },
            {
                'name': 'English Excellent Java CV',
                'cv_file': 'test_cvs/english_cv_excellent_match.txt',
                'job': 'Java Developer (English)',
                'expected_score_range': (85, 100)
            },
            {
                'name': 'German Poor Match CV',
                'cv_file': 'test_cvs/german_cv_poor_match.txt',
                'job': 'Java Developer (German)',
                'expected_score_range': (0, 50)
            },
            {
                'name': 'English Poor Match CV',
                'cv_file': 'test_cvs/english_cv_poor_match.txt',
                'job': 'Java Developer (English)',
                'expected_score_range': (0, 60)
            }
        ]
        
        java_job_german = """
        Java Entwickler (m/w/d)
        Mindestens 3 Jahre Berufserfahrung
        Java, Spring Boot, PostgreSQL
        REST-APIs, Git, Jenkins, Scrum
        Informatik-Studium
        """
        
        java_job_english = """
        Java Developer Position
        3+ years experience required
        Java, Spring Boot, PostgreSQL
        REST APIs, Git, Jenkins, Scrum
        Computer Science degree
        """
        
        jobs = {
            'Java Developer (German)': java_job_german,
            'Java Developer (English)': java_job_english
        }
        
        passed_tests = 0
        total_tests = len(test_cases)
        
        for test_case in test_cases:
            if not os.path.exists(test_case['cv_file']):
                print(f"   ❌ {test_case['name']}: CV file not found")
                continue
            
            with open(test_case['cv_file'], 'r', encoding='utf-8') as f:
                cv_content = f.read()
            
            job_desc = jobs[test_case['job']]
            score = self.calculate_overall_score(job_desc, cv_content)
            
            min_score, max_score = test_case['expected_score_range']
            if min_score <= score <= max_score:
                print(f"   ✅ {test_case['name']}: {score:.1f}% (Expected: {min_score}-{max_score}%)")
                passed_tests += 1
            else:
                print(f"   ❌ {test_case['name']}: {score:.1f}% (Expected: {min_score}-{max_score}%)")
        
        self.test_results.append({
            'test': 'Basic Functionality',
            'passed': passed_tests,
            'total': total_tests,
            'success_rate': (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        })

    def test_language_capabilities(self):
        """Test multilingual capabilities"""
        print(f"\n2️⃣ LANGUAGE CAPABILITIES TEST")
        print("-" * 35)
        
        # Test language preference
        german_cv = """
        Anna Müller - Softwareentwicklerin
        5 Jahre Berufserfahrung in Java-Entwicklung
        Spring Boot, PostgreSQL, REST-APIs
        Scrum, Git, Jenkins
        Master Informatik
        """
        
        english_cv = """
        John Smith - Software Developer
        5 years Java development experience
        Spring Boot, PostgreSQL, REST APIs
        Scrum, Git, Jenkins
        Master Computer Science
        """
        
        german_job = "Java Entwickler, Spring Boot, PostgreSQL, Scrum, Informatik"
        english_job = "Java Developer, Spring Boot, PostgreSQL, Scrum, Computer Science"
        
        # Test German CV with both jobs
        german_cv_german_job = self.calculate_overall_score(german_job, german_cv)
        german_cv_english_job = self.calculate_overall_score(english_job, german_cv)
        
        # Test English CV with both jobs
        english_cv_german_job = self.calculate_overall_score(german_job, english_cv)
        english_cv_english_job = self.calculate_overall_score(english_job, english_cv)
        
        print(f"   German CV + German Job: {german_cv_german_job:.1f}%")
        print(f"   German CV + English Job: {german_cv_english_job:.1f}%")
        print(f"   English CV + German Job: {english_cv_german_job:.1f}%")
        print(f"   English CV + English Job: {english_cv_english_job:.1f}%")
        
        # Validate language preferences
        language_tests_passed = 0
        total_language_tests = 2
        
        if german_cv_german_job >= german_cv_english_job:
            print(f"   ✅ German CV prefers German job")
            language_tests_passed += 1
        else:
            print(f"   ❌ German CV doesn't prefer German job")
        
        if english_cv_english_job >= english_cv_german_job:
            print(f"   ✅ English CV prefers English job")
            language_tests_passed += 1
        else:
            print(f"   ❌ English CV doesn't prefer English job")
        
        self.test_results.append({
            'test': 'Language Capabilities',
            'passed': language_tests_passed,
            'total': total_language_tests,
            'success_rate': (language_tests_passed / total_language_tests) * 100
        })

    def test_accuracy_validation(self):
        """Test accuracy with existing CV files"""
        print(f"\n3️⃣ ACCURACY VALIDATION TEST")
        print("-" * 35)
        
        # Test with existing PDF files
        cv_files = glob.glob("uploads/*.pdf")
        if not cv_files:
            print("   ⚠️  No PDF files found for accuracy testing")
            return
        
        java_job = """
        Senior Java Developer
        3+ years Java experience
        Spring Boot, PostgreSQL
        REST APIs, Git, Jenkins
        Computer Science degree
        """
        
        results = []
        for cv_file in cv_files:
            try:
                cv_content = self.extractor.extract_text_from_file(cv_file)
                if cv_content:
                    score = self.calculate_overall_score(java_job, cv_content)
                    results.append({
                        'file': os.path.basename(cv_file),
                        'score': score
                    })
            except Exception as e:
                print(f"   ⚠️  Error processing {cv_file}: {e}")
        
        # Sort by score
        results.sort(key=lambda x: x['score'], reverse=True)
        
        print(f"   CV Rankings for Java Developer position:")
        for i, result in enumerate(results, 1):
            print(f"   {i}. {result['file']}: {result['score']:.1f}%")
        
        # Validate that Maria scores higher than Max (if both exist)
        maria_score = next((r['score'] for r in results if 'maria' in r['file'].lower()), None)
        max_score = next((r['score'] for r in results if 'max' in r['file'].lower()), None)
        
        accuracy_tests_passed = 0
        total_accuracy_tests = 1
        
        if maria_score is not None and max_score is not None:
            if maria_score > max_score:
                print(f"   ✅ Maria (Java dev) scores higher than Max (sysadmin): {maria_score:.1f}% vs {max_score:.1f}%")
                accuracy_tests_passed += 1
            else:
                print(f"   ❌ Max scores higher than Maria: {max_score:.1f}% vs {maria_score:.1f}%")
        else:
            print(f"   ⚠️  Maria or Max CV not found for comparison")
        
        self.test_results.append({
            'test': 'Accuracy Validation',
            'passed': accuracy_tests_passed,
            'total': total_accuracy_tests,
            'success_rate': (accuracy_tests_passed / total_accuracy_tests) * 100 if total_accuracy_tests > 0 else 0
        })

    def test_performance(self):
        """Test performance and consistency"""
        print(f"\n4️⃣ PERFORMANCE TEST")
        print("-" * 25)
        
        # Test performance with multiple runs
        test_cv = "Java Developer with Spring Boot and PostgreSQL experience"
        test_job = "Java Developer position requiring Spring Boot and PostgreSQL"
        
        times = []
        scores = []
        
        for i in range(10):
            start_time = time.time()
            score = self.calculate_overall_score(test_job, test_cv)
            end_time = time.time()
            
            times.append(end_time - start_time)
            scores.append(score)
        
        avg_time = sum(times) / len(times)
        min_score = min(scores)
        max_score = max(scores)
        
        print(f"   Average processing time: {avg_time:.3f}s")
        print(f"   Score consistency: {min_score:.1f}% - {max_score:.1f}%")
        
        # Performance criteria
        performance_tests_passed = 0
        total_performance_tests = 2
        
        if avg_time < 0.1:  # Should be fast
            print(f"   ✅ Fast processing (< 0.1s)")
            performance_tests_passed += 1
        else:
            print(f"   ❌ Slow processing (>= 0.1s)")
        
        if max_score - min_score < 0.1:  # Should be consistent
            print(f"   ✅ Consistent scoring")
            performance_tests_passed += 1
        else:
            print(f"   ❌ Inconsistent scoring")
        
        self.test_results.append({
            'test': 'Performance',
            'passed': performance_tests_passed,
            'total': total_performance_tests,
            'success_rate': (performance_tests_passed / total_performance_tests) * 100
        })

    def test_edge_cases(self):
        """Test edge cases and error handling"""
        print(f"\n5️⃣ EDGE CASES TEST")
        print("-" * 20)
        
        job = "Java Developer with Spring Boot"
        
        edge_cases = [
            ("Empty CV", ""),
            ("Only name", "John Doe"),
            ("Special chars", "Müller, Jürgen - Größe: 1,80m"),
            ("Very long CV", "Java " * 1000),
            ("Numbers only", "123 456 789"),
            ("Mixed languages", "Java Developer Entwickler Spring Boot")
        ]
        
        edge_tests_passed = 0
        total_edge_tests = len(edge_cases)
        
        for case_name, cv_content in edge_cases:
            try:
                score = self.calculate_overall_score(job, cv_content)
                if 0 <= score <= 100:  # Valid score range
                    print(f"   ✅ {case_name}: {score:.1f}%")
                    edge_tests_passed += 1
                else:
                    print(f"   ❌ {case_name}: Invalid score {score:.1f}%")
            except Exception as e:
                print(f"   ❌ {case_name}: Error - {e}")
        
        self.test_results.append({
            'test': 'Edge Cases',
            'passed': edge_tests_passed,
            'total': total_edge_tests,
            'success_rate': (edge_tests_passed / total_edge_tests) * 100
        })

    def calculate_overall_score(self, job_desc: str, cv_content: str) -> float:
        """Calculate overall score using the same weights as the matcher"""
        tf_idf_score = self.matcher.calculate_tf_idf_similarity(job_desc, cv_content)
        keyword_score = self.matcher.calculate_keyword_match(job_desc, cv_content)
        skill_score = self.matcher.calculate_skill_match(job_desc, cv_content)
        
        overall_score = (skill_score * 0.5) + (keyword_score * 0.3) + (tf_idf_score * 0.2)
        return overall_score * 100

    def print_final_summary(self):
        """Print final test summary"""
        print(f"\n📊 FINAL TEST SUMMARY")
        print("=" * 30)
        
        total_passed = sum(result['passed'] for result in self.test_results)
        total_tests = sum(result['total'] for result in self.test_results)
        overall_success_rate = (total_passed / total_tests) * 100 if total_tests > 0 else 0
        
        for result in self.test_results:
            status = "✅" if result['success_rate'] >= 80 else "⚠️" if result['success_rate'] >= 60 else "❌"
            print(f"{status} {result['test']}: {result['passed']}/{result['total']} ({result['success_rate']:.1f}%)")
        
        print(f"\n🎯 OVERALL RESULTS:")
        print(f"   Total Tests Passed: {total_passed}/{total_tests}")
        print(f"   Overall Success Rate: {overall_success_rate:.1f}%")
        
        if overall_success_rate >= 80:
            print(f"\n🎉 EXCELLENT! The CV matching system is working very well!")
            print(f"   ✅ German and English CV parsing is accurate")
            print(f"   ✅ Language detection and preference works correctly")
            print(f"   ✅ Scoring algorithm produces logical results")
            print(f"   ✅ Performance is fast and consistent")
            print(f"   ✅ Edge cases are handled properly")
        elif overall_success_rate >= 60:
            print(f"\n👍 GOOD! The CV matching system works well with minor issues.")
        else:
            print(f"\n⚠️  NEEDS IMPROVEMENT! Several issues detected in the CV matching system.")

def main():
    """Run the final comprehensive test suite"""
    test_suite = FinalTestSuite()
    test_suite.run_all_tests()

if __name__ == "__main__":
    main()
