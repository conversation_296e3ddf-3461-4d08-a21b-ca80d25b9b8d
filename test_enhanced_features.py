#!/usr/bin/env python3
"""
Test script for enhanced BAUCH HR Management System features
Tests security enhancements, bilingual processing, and deployment readiness
"""

import os
import sys
import json
import tempfile
from datetime import datetime

def test_enhanced_security():
    """Test enhanced security features"""
    print("🔒 Testing Enhanced Security Features")
    print("-" * 40)
    
    try:
        from hr_database import HRDatabase
        
        # Create temporary database for testing
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
            db_path = tmp_db.name
        
        hr_db = HRDatabase(f'sqlite:///{db_path}')
        
        # Test user creation with strong password
        print("✓ Testing user creation...")
        user = hr_db.create_user("testuser", "<EMAIL>", "StrongPassword123!")
        assert user is not None
        print(f"  Created user: {user.username}")
        
        # Test authentication
        print("✓ Testing authentication...")
        auth_user = hr_db.authenticate_user("testuser", "StrongPassword123!")
        assert auth_user is not None
        print(f"  Authentication successful for: {auth_user.username}")
        
        # Test failed login attempts
        print("✓ Testing failed login protection...")
        for i in range(3):
            failed_auth = hr_db.authenticate_user("testuser", "wrongpassword")
            assert failed_auth is None
        print("  Failed login attempts recorded")
        
        # Test password change
        print("✓ Testing password change...")
        success = hr_db.change_user_password(user.id, "StrongPassword123!", "NewStrongPassword456!")
        assert success is True
        print("  Password changed successfully")
        
        # Test authentication with new password
        new_auth = hr_db.authenticate_user("testuser", "NewStrongPassword456!")
        assert new_auth is not None
        print("  Authentication with new password successful")
        
        # Cleanup
        os.unlink(db_path)
        print("✅ Security features test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Security test failed: {e}")
        return False


def test_bilingual_processing():
    """Test bilingual CV processing capabilities"""
    print("\n🌐 Testing Bilingual Processing")
    print("-" * 40)
    
    try:
        from language_detector import LanguageDetector
        from bilingual_cv_extractor import BilingualCVExtractor
        from bilingual_matcher import BilingualCVMatcher
        from german_skills import extract_german_skills_from_text
        
        # Test language detection
        print("✓ Testing language detection...")
        detector = LanguageDetector()
        
        german_text = "Ich bin ein erfahrener Softwareentwickler mit 5 Jahren Berufserfahrung."
        english_text = "I am an experienced software developer with 5 years of experience."
        
        de_lang, de_conf = detector.detect_language(german_text)
        en_lang, en_conf = detector.detect_language(english_text)
        
        print(f"  German text detected as: {de_lang} (confidence: {de_conf:.2f})")
        print(f"  English text detected as: {en_lang} (confidence: {en_conf:.2f})")
        
        # Test German skills extraction
        print("✓ Testing German skills extraction...")
        skills_text = "Python Java JavaScript Webentwicklung Projektmanagement Teamarbeit"
        skills = extract_german_skills_from_text(skills_text)
        print(f"  Extracted {len(skills)} German skills: {skills[:5]}")
        
        # Test bilingual CV extraction
        print("✓ Testing bilingual CV extraction...")
        extractor = BilingualCVExtractor()
        
        german_cv = """
        Max Müller
        E-Mail: <EMAIL>
        Telefon: +49 89 123456789
        Berufserfahrung: 5 Jahre Softwareentwicklung
        Fähigkeiten: Python, Java, Webentwicklung, Projektmanagement
        """
        
        name = extractor.extract_name_bilingual(german_cv)
        email = extractor.extract_email(german_cv)
        skills = extractor.extract_skills_bilingual(german_cv)
        
        print(f"  Extracted name: {name}")
        print(f"  Extracted email: {email}")
        print(f"  Extracted skills: {skills[:50]}...")
        
        # Test bilingual matching
        print("✓ Testing bilingual matching...")
        matcher = BilingualCVMatcher()
        
        german_job = "Wir suchen einen Softwareentwickler mit Python und Java Kenntnissen."
        english_cv = "Software developer with Python and Java experience."
        
        results = matcher.match_bilingual(german_job, [english_cv])
        score = results[0][0] * 100
        print(f"  Cross-language match score: {score:.1f}%")
        
        print("✅ Bilingual processing test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Bilingual processing test failed: {e}")
        return False


def test_translation_service():
    """Test translation service"""
    print("\n🌍 Testing Translation Service")
    print("-" * 40)
    
    try:
        from translation_service import get_text, TranslationService
        
        service = TranslationService()
        
        # Test UI translations
        print("✓ Testing UI translations...")
        home_en = get_text('home', 'en')
        home_de = get_text('home', 'de')
        jobs_en = get_text('jobs', 'en')
        jobs_de = get_text('jobs', 'de')
        
        print(f"  Home: {home_en} / {home_de}")
        print(f"  Jobs: {jobs_en} / {jobs_de}")
        
        # Test password-related translations
        print("✓ Testing security translations...")
        change_pwd_en = get_text('change_password', 'en')
        change_pwd_de = get_text('change_password', 'de')
        
        print(f"  Change Password: {change_pwd_en} / {change_pwd_de}")
        
        print("✅ Translation service test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Translation service test failed: {e}")
        return False


def test_database_enhancements():
    """Test database enhancements with match scoring"""
    print("\n💾 Testing Database Enhancements")
    print("-" * 40)
    
    try:
        from hr_database import HRDatabase
        import json
        
        # Create temporary database
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
            db_path = tmp_db.name
        
        hr_db = HRDatabase(f'sqlite:///{db_path}')
        
        # Create test job
        print("✓ Creating test job...")
        job = hr_db.add_job("Test Developer", "Python developer position")
        print(f"  Created job: {job.title}")
        
        # Create test CV with enhanced data
        print("✓ Creating test CV with enhanced data...")
        extracted_data = json.dumps({
            "name": "John Doe",
            "email": "<EMAIL>",
            "skills": ["Python", "Java"],
            "language_detected": "english"
        })
        
        cv = hr_db.add_cv(
            "john_doe_cv.pdf", 
            "John Doe CV content", 
            "Test Developer",
            candidate_name="John Doe",
            language="english",
            extracted_data=extracted_data
        )
        print(f"  Created CV: {cv.filename}")
        
        # Update CV with match score
        print("✓ Updating CV with match score...")
        match_details = json.dumps({
            "content_similarity": 0.75,
            "keyword_match": 0.80,
            "skill_match": 0.85,
            "language_bonus": 0.05
        })
        
        updated_cv = hr_db.update_cv_match_score(cv.id, "82.5%", match_details)
        print(f"  Updated CV match score: {updated_cv.match_score}")
        
        # Cleanup
        os.unlink(db_path)
        print("✅ Database enhancements test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Database enhancements test failed: {e}")
        return False


def test_deployment_readiness():
    """Test deployment readiness"""
    print("\n🚀 Testing Deployment Readiness")
    print("-" * 40)
    
    try:
        # Check if deployment files exist
        deployment_files = [
            'wsgi.py',
            'production_config.py',
            'deploy.sh',
            'Dockerfile',
            'docker-compose.yml',
            'nginx.conf',
            'DEPLOYMENT_GUIDE.md'
        ]
        
        print("✓ Checking deployment files...")
        for file in deployment_files:
            if os.path.exists(file):
                print(f"  ✅ {file}")
            else:
                print(f"  ❌ {file} - Missing")
                return False
        
        # Check if requirements.txt has all dependencies
        print("✓ Checking requirements.txt...")
        with open('requirements.txt', 'r') as f:
            requirements = f.read()
        
        required_packages = [
            'Flask', 'spacy', 'langdetect', 'googletrans', 'scikit-learn'
        ]
        
        for package in required_packages:
            if package.lower() in requirements.lower():
                print(f"  ✅ {package}")
            else:
                print(f"  ❌ {package} - Missing from requirements")
                return False
        
        # Test WSGI application
        print("✓ Testing WSGI application...")
        try:
            from wsgi import application
            print("  ✅ WSGI application imports successfully")
        except Exception as e:
            print(f"  ❌ WSGI application error: {e}")
            return False
        
        print("✅ Deployment readiness test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Deployment readiness test failed: {e}")
        return False


def main():
    """Run all tests"""
    print("🧪 BAUCH HR Management System - Enhanced Features Test Suite")
    print("=" * 65)
    
    tests = [
        ("Enhanced Security", test_enhanced_security),
        ("Bilingual Processing", test_bilingual_processing),
        ("Translation Service", test_translation_service),
        ("Database Enhancements", test_database_enhancements),
        ("Deployment Readiness", test_deployment_readiness)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All enhanced features are working correctly!")
        print("✅ Security enhancements implemented")
        print("✅ Bilingual processing operational")
        print("✅ Match scoring integrated")
        print("✅ Deployment configuration ready")
        print("\n🚀 The system is ready for production deployment!")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed")
        print("Some features may need attention before deployment")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
