# 📧 Email System Setup Guide

## ✅ **Issues Fixed:**

### 1. **URL Not Found Error** - FIXED ✅
- **Problem**: Job detail URLs with special characters were not working
- **Solution**: Updated routes to use `<path:job_title>` and added URL decoding with `urllib.parse.unquote()`
- **Result**: Job detail pages now work correctly with any job title

### 2. **Email System Added** - NEW FEATURE ✅
- **Added**: Complete email system for mass emailing applicants
- **Features**: 
  - Send emails to all applicants or selected ones
  - German email templates (Bewerbung eingegangen, Einladung zum Vorstellungsgespräch, Status-Update)
  - Custom message support
  - Email preview functionality
  - Automatic email extraction from CVs

## 🚀 **New Email Features:**

### **Email Button Locations:**
1. **Job Detail Page**: "Email Applicants" button in the action bar
2. **Quick Actions Section**: Dedicated email card for easy access

### **Email Templates Available:**
1. **Application Received** (Bewerbung eingegangen)
2. **Interview Invitation** (Einladung zum Vorstellungsgespräch) 
3. **Status Update** (Status-Update)
4. **Custom Message** (Benutzerdefinierte Nachricht)

### **Email Functionality:**
- ✅ **Bulk Email Sending**: Send to all or selected applicants
- ✅ **Email Extraction**: Automatically finds email addresses in CVs
- ✅ **Template Variables**: {name}, {job_title}, {status}, {additional_info}
- ✅ **German Language**: All templates in German for local HR processes
- ✅ **Email Preview**: See how emails will look before sending
- ✅ **Selection Tools**: Select all/none buttons for easy management

## ⚙️ **Email Configuration Setup:**

### **Method 1: Environment Variables (Recommended)**
Create a `.env` file in your project directory:

```bash
# Email Configuration
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-specific-password
MAIL_DEFAULT_SENDER=<EMAIL>
```

### **Method 2: Gmail Setup (Most Common)**

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate App Password**:
   - Go to Google Account settings
   - Security → App passwords
   - Generate password for "Mail"
   - Use this password (not your regular Gmail password)

3. **Configuration**:
   ```bash
   MAIL_SERVER=smtp.gmail.com
   MAIL_PORT=587
   MAIL_USERNAME=<EMAIL>
   MAIL_PASSWORD=your-16-character-app-password
   MAIL_DEFAULT_SENDER=<EMAIL>
   ```

### **Method 3: Other Email Providers**

**Outlook/Hotmail:**
```bash
MAIL_SERVER=smtp-mail.outlook.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-password
```

**Corporate Exchange:**
```bash
MAIL_SERVER=your-company-smtp-server.com
MAIL_PORT=587
MAIL_USERNAME=your-username
MAIL_PASSWORD=your-password
```

## 🧪 **Testing Email System:**

### **1. Test Email Configuration:**
```python
# Run this in Python console to test
from email_service import EmailService

# Test configuration
email_service = EmailService()
print("Email service initialized successfully!")

# Test templates
templates = email_service.get_default_templates('de')
print(f"Available templates: {list(templates.keys())}")
```

### **2. Test Email Sending (Safe Mode):**
```python
# Test without actually sending emails
recipients = [
    {
        'email': '<EMAIL>',
        'name': 'Test User',
        'job_title': 'Test Position',
        'status': 'under review',
        'additional_info': 'This is a test email.'
    }
]

# This will prepare emails but not send them if SMTP is not configured
result = email_service.send_bulk_emails(
    recipients=recipients,
    subject='Test Email',
    template=templates['application_received'],
    async_send=False
)
print(f"Test result: {result}")
```

## 📋 **How to Use Email System:**

### **Step 1: Access Email System**
1. Go to any job with applicants
2. Click "Email Applicants" button
3. You'll see the email interface

### **Step 2: Configure Email**
1. **Select Template**: Choose from German templates or custom message
2. **Enter Subject**: Email subject line
3. **Select Recipients**: Choose which applicants to email
4. **Preview**: Review email content before sending

### **Step 3: Send Emails**
1. Click "Send Emails" button
2. System will show success/failure count
3. Only applicants with valid email addresses will receive emails

## 🔧 **Troubleshooting:**

### **Common Issues:**

**1. "Email configuration incomplete"**
- Solution: Set up environment variables for email server

**2. "No valid email addresses found"**
- Solution: Make sure CVs contain email addresses
- Check CV content for proper email format

**3. "SMTP Authentication failed"**
- Solution: Check username/password, use app passwords for Gmail

**4. "Connection refused"**
- Solution: Check MAIL_SERVER and MAIL_PORT settings

### **Email Address Extraction:**
The system automatically extracts email addresses from CV content using regex pattern:
- Looks for standard email format: `<EMAIL>`
- Displays found emails in applicant selection
- Shows warning if no email found in CV

## 🎯 **Production Recommendations:**

1. **Use Professional Email Service**: Consider SendGrid, Mailgun, or AWS SES for high volume
2. **Monitor Delivery**: Track email delivery rates and bounces
3. **Rate Limiting**: Don't send too many emails at once
4. **Backup Email Addresses**: Maintain separate contact database
5. **Legal Compliance**: Follow GDPR and email marketing regulations

## 📞 **Support:**

If you need help with email configuration:
1. Check the error messages in the application
2. Verify your email provider settings
3. Test with a simple email client first
4. Contact your IT department for corporate email settings

---

**✅ Your German HR Application now has full email functionality!**
