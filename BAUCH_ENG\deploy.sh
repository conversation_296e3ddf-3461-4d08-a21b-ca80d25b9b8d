#!/bin/bash

# BAUCH HR Management System Deployment Script
# This script sets up the application for production deployment

echo "🚀 BAUCH HR Management System Deployment"
echo "========================================"

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed."
    exit 1
fi

echo "✅ Python 3 found"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install dependencies
echo "📥 Installing dependencies..."
pip install -r requirements.txt

# Create uploads directory if it doesn't exist
if [ ! -d "uploads" ]; then
    echo "📁 Creating uploads directory..."
    mkdir uploads
fi

# Create admin user if database doesn't exist
if [ ! -f "hr_database.db" ]; then
    echo "👤 Creating admin user..."
    python create_admin_user.py
else
    echo "ℹ️  Database already exists. Skipping user creation."
fi

# Set permissions
echo "🔒 Setting permissions..."
chmod 755 app.py
chmod 755 create_admin_user.py
chmod -R 755 static/
chmod -R 755 templates/
chmod 777 uploads/

echo ""
echo "✅ Deployment completed successfully!"
echo ""
echo "🌐 To start the application:"
echo "   For development: python app.py"
echo "   For production:  gunicorn -w 4 -b 0.0.0.0:8000 app:app"
echo ""
echo "🔑 Default login credentials:"
echo "   Username: admin"
echo "   Password: admin123"
echo ""
echo "⚠️  IMPORTANT: Change the default password after first login!"
