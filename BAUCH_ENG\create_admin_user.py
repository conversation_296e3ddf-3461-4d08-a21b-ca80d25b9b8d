#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create the default admin user for the BAUCH HR Management System
"""

from hr_database import HRDatabase

def create_admin_user():
    """Create the default admin user"""
    print("Creating default admin user...")
    
    # Initialize database
    db = HRDatabase()
    
    try:
        # Create admin user
        user = db.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123'
        )
        print(f"✅ Admin user created successfully!")
        print(f"   Username: {user.username}")
        print(f"   Email: {user.email}")
        print(f"   Password: admin123")
        print()
        print("⚠️  IMPORTANT: Please change the default password after first login!")
        
    except ValueError as e:
        if "already exists" in str(e):
            print("ℹ️  Admin user already exists.")
            print("   Username: admin")
            print("   Password: admin123")
        else:
            print(f"❌ Error creating admin user: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    create_admin_user()
