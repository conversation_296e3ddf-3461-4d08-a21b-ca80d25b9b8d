#!/usr/bin/env python3
"""
Comprehensive Test Suite for Enhanced German CV Extractor
Tests the improved experience and skills extraction functionality
"""

import os
import tempfile
from typing import Dict, List
from bilingual_cv_extractor_patched import BilingualCVExtractorPatched, extract_german_cv_data

class EnhancedGermanExtractorTest:
    def __init__(self):
        self.extractor = BilingualCVExtractorPatched()
        
    def test_experience_extraction(self):
        """Test German experience extraction patterns"""
        print("🔧 TESTING EXPERIENCE EXTRACTION")
        print("=" * 50)
        
        test_cases = [
            # Years of experience patterns
            ("5 Jahre Berufserfahrung in der Softwareentwicklung", "5 Jahre Berufserfahrung"),
            ("Über 3 Jahre Erfahrung mit Java", "3 Jahre Berufserfahrung"),
            ("2-4 Jahre Berufstätigkeit", "2-4 Jahre Berufserfahrung"),
            ("Seit 6 Jahren tätig als Entwickler", "6 Jahre Berufserfahrung"),
            
            # Job positions with companies
            ("2018-2023: Senior Java Entwickler bei SAP AG", "2018-2023: Senior Java Entwickler bei SAP AG"),
            ("Softwareentwickler bei Microsoft (2020-heute)", "2020-heute: Softwareentwickler bei Microsoft"),
            ("2019-2022: Projektmanager\n2022-heute: Team Lead", "2019-2022: Projektmanager"),
            
            # Experience levels
            ("Junior Entwickler mit ersten Erfahrungen", "Junior Level (0-2 Jahre)"),
            ("Senior Manager mit Führungsverantwortung", "Senior Level (5+ Jahre)"),
            ("Erfahrener Spezialist im Bereich", "Mittleres Level (2-5 Jahre)"),
        ]
        
        for i, (cv_text, expected_pattern) in enumerate(test_cases, 1):
            print(f"\n📝 Test Case {i}:")
            print(f"   Input: '{cv_text}'")
            
            experience = self.extractor._extract_experience(cv_text)
            print(f"   Output: '{experience}'")
            
            # Check if the output contains expected elements
            success = any(word in experience for word in expected_pattern.split())
            print(f"   Result: {'✅ PASS' if success else '❌ FAIL'}")
    
    def test_skills_extraction(self):
        """Test German skills extraction"""
        print(f"\n🛠️ TESTING SKILLS EXTRACTION")
        print("=" * 50)
        
        test_cases = [
            # Technical skills in German context
            ("Programmierung mit Python, Java und JavaScript", ["Python", "Java", "Javascript"]),
            ("Webentwicklung mit React und Node.js", ["React", "Node.js", "Webentwicklung"]),
            ("Datenbank: MySQL, PostgreSQL", ["Mysql", "Postgresql"]),
            
            # Skills sections
            ("""
            Fähigkeiten:
            • Python, Java
            • Projektmanagement
            • Teamarbeit
            """, ["Python", "Java", "Projektmanagement", "Teamarbeit"]),
            
            # Mixed German/English skills
            ("Docker, Kubernetes, Cloud Computing, Agile Entwicklung", ["Docker", "Kubernetes", "Cloud Computing"]),
            
            # Soft skills in German
            ("Kommunikation, Führung, Problemlösung", ["Kommunikation", "Führung", "Problemlösung"]),
        ]
        
        for i, (cv_text, expected_skills) in enumerate(test_cases, 1):
            print(f"\n📝 Test Case {i}:")
            print(f"   Input: '{cv_text}'")
            
            skills = self.extractor._extract_skills(cv_text)
            print(f"   Output: '{skills}'")
            
            # Check if expected skills are found
            found_count = sum(1 for skill in expected_skills if skill.lower() in skills.lower())
            success = found_count >= len(expected_skills) // 2  # At least half should be found
            print(f"   Found {found_count}/{len(expected_skills)} expected skills")
            print(f"   Result: {'✅ PASS' if success else '❌ FAIL'}")
    
    def test_education_extraction(self):
        """Test German education extraction"""
        print(f"\n🎓 TESTING EDUCATION EXTRACTION")
        print("=" * 50)
        
        test_cases = [
            ("Bachelor of Science Informatik", "Bachelor"),
            ("Master Informatik an der TU München", "Master"),
            ("Diplom-Informatiker", "Diplom"),
            ("Studium der Informatik", "Informatik"),
            ("Ausbildung zum Fachinformatiker", "Fachinformatiker"),
            ("B.Sc. Maschinenbau", "Maschinenbau"),
        ]
        
        for i, (cv_text, expected_keyword) in enumerate(test_cases, 1):
            print(f"\n📝 Test Case {i}:")
            print(f"   Input: '{cv_text}'")
            
            education = self.extractor._extract_education(cv_text)
            print(f"   Output: '{education}'")
            
            success = expected_keyword.lower() in education.lower()
            print(f"   Result: {'✅ PASS' if success else '❌ FAIL'}")
    
    def test_seniority_classification(self):
        """Test seniority level classification"""
        print(f"\n📊 TESTING SENIORITY CLASSIFICATION")
        print("=" * 50)
        
        test_cases = [
            ("5 Jahre Berufserfahrung", "Mid"),
            ("10 Jahre Berufserfahrung", "Senior"),
            ("1 Jahr Berufserfahrung", "Junior"),
            ("Senior Manager mit Führungsverantwortung", "Senior"),
            ("Junior Entwickler", "Junior"),
            ("Erfahrener Spezialist", "Mid"),
        ]
        
        for i, (experience_text, expected_level) in enumerate(test_cases, 1):
            print(f"\n📝 Test Case {i}:")
            print(f"   Input: '{experience_text}'")
            
            seniority = self.extractor._classify_seniority(experience_text, experience_text)
            print(f"   Output: '{seniority}'")
            
            success = expected_level.lower() in seniority.lower()
            print(f"   Result: {'✅ PASS' if success else '❌ FAIL'}")
    
    def test_complete_cv_extraction(self):
        """Test complete CV extraction with a realistic German CV"""
        print(f"\n📄 TESTING COMPLETE CV EXTRACTION")
        print("=" * 50)
        
        german_cv_text = """
        Max Müller
        E-Mail: <EMAIL>
        Telefon: +49 89 *********
        
        Berufserfahrung:
        2018-2023: Senior Java Entwickler bei SAP AG
        • Entwicklung von Enterprise-Anwendungen
        • Führung eines 5-köpfigen Entwicklerteams
        • 5 Jahre Berufserfahrung in der Softwareentwicklung
        
        Fähigkeiten:
        • Programmierung: Java, Python, JavaScript
        • Frameworks: Spring Boot, React, Angular
        • Datenbanken: MySQL, PostgreSQL
        • Soft Skills: Teamarbeit, Projektmanagement, Führung
        
        Ausbildung:
        Master of Science Informatik
        Technische Universität München (2016-2018)
        
        Bachelor Informatik
        Universität Stuttgart (2013-2016)
        """
        
        print("📝 Extracting from complete German CV...")
        
        # Test all fields
        fields = ['name', 'email', 'phone', 'experience', 'skills', 'education', 'seniority']
        
        # Create a temporary file for testing
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(german_cv_text)
            temp_file = f.name
        
        try:
            # Since we can't directly test with PDF, we'll test the text extraction methods
            print(f"\n🔍 EXTRACTION RESULTS:")
            print("-" * 30)
            
            name = self.extractor._extract_name(german_cv_text, "test.pdf")
            print(f"Name: '{name}'")
            
            email = self.extractor._extract_email(german_cv_text)
            print(f"Email: '{email}'")
            
            phone = self.extractor._extract_phone(german_cv_text)
            print(f"Phone: '{phone}'")
            
            experience = self.extractor._extract_experience(german_cv_text)
            print(f"Experience: '{experience}'")
            
            skills = self.extractor._extract_skills(german_cv_text)
            print(f"Skills: '{skills}'")
            
            education = self.extractor._extract_education(german_cv_text)
            print(f"Education: '{education}'")
            
            seniority = self.extractor._classify_seniority(german_cv_text, experience)
            print(f"Seniority: '{seniority}'")
            
            # Validate results
            validations = [
                ("Name", name and "max" in name.lower() and "müller" in name.lower()),
                ("Email", email and "@" in email),
                ("Phone", phone and "+49" in phone),
                ("Experience", experience and ("jahre" in experience.lower() or "sap" in experience.lower())),
                ("Skills", skills and len(skills.split(",")) >= 3),
                ("Education", education and ("master" in education.lower() or "informatik" in education.lower())),
                ("Seniority", seniority and seniority.lower() in ['junior', 'mid', 'senior'])
            ]
            
            print(f"\n📊 VALIDATION RESULTS:")
            print("-" * 30)
            passed = 0
            for field, is_valid in validations:
                status = "✅ PASS" if is_valid else "❌ FAIL"
                print(f"{field}: {status}")
                if is_valid:
                    passed += 1
            
            print(f"\nOverall: {passed}/{len(validations)} tests passed ({(passed/len(validations))*100:.1f}%)")
            
        finally:
            # Clean up temp file
            os.unlink(temp_file)
    
    def test_edge_cases(self):
        """Test edge cases and error handling"""
        print(f"\n🔍 TESTING EDGE CASES")
        print("=" * 50)
        
        edge_cases = [
            ("", "Empty text"),
            ("Just some random text without CV content", "No CV content"),
            ("Name: Max\nSkills: R", "Minimal content with 'R' skill"),
            ("Berufserfahrung: nicht vorhanden", "No experience specified"),
        ]
        
        for i, (text, description) in enumerate(edge_cases, 1):
            print(f"\n📝 Edge Case {i}: {description}")
            print(f"   Input: '{text}'")
            
            try:
                experience = self.extractor._extract_experience(text)
                skills = self.extractor._extract_skills(text)
                
                print(f"   Experience: '{experience}'")
                print(f"   Skills: '{skills}'")
                
                # Check that we don't get placeholder values like "R"
                skills_valid = skills != "R" and "nicht spezifiziert" in skills if not text.strip() else True
                
                print(f"   Result: {'✅ PASS' if skills_valid else '❌ FAIL'}")
                
            except Exception as e:
                print(f"   Error: {e}")
                print(f"   Result: ❌ FAIL")
    
    def run_all_tests(self):
        """Run all test suites"""
        print("🚀 ENHANCED GERMAN CV EXTRACTOR TEST SUITE")
        print("=" * 80)
        
        self.test_experience_extraction()
        self.test_skills_extraction()
        self.test_education_extraction()
        self.test_seniority_classification()
        self.test_complete_cv_extraction()
        self.test_edge_cases()
        
        print(f"\n✅ ALL TESTS COMPLETED!")
        print("=" * 80)

def main():
    """Run the enhanced German extractor tests"""
    test_suite = EnhancedGermanExtractorTest()
    test_suite.run_all_tests()

if __name__ == "__main__":
    main()
