"""Enhanced Bilingual CV extractor (improved version, July 2025).

Focus: robust extraction for German CVs including experience, skills, and education.
Handles both PDF and DOCX files with comprehensive German language support.
"""

from __future__ import annotations
import re, os
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime

try:
    import fitz  # PyMuPDF
except ImportError:  # pragma: no cover
    raise ImportError("PyMuPDF (fitz) is required: pip install pymupdf")

try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

# ---------- Helper ---------------------------------------------------------

_BAD_TOKENS = {
    # Company / legal forms
    "gmbh", "kg", "ag", "ug", "mbh", "sarl", "s.a.", "e.v.", "ev",
    # Sections / words that are *not* personal names
    "lebenslauf", "curriculum", "vitae", "bewerbung",
    "anschreiben", "anschrift", "straße", "str", "str.", "telefon",
    "tel", "email", "e-mail", "name", "vorname", "nachname",
    # CV sections
    "berufserfahrung", "education", "ausbildung", "qualifikationen",
    "fähigkeiten", "skills", "projekte", "projects", "kontakt", "contact",
    "persönliche", "daten", "personal", "data", "ort", "stadt", "city",
    "deutschland", "germany", "berlin", "münchen", "hamburg"
}

_GERMAN_NAME_PATTERNS = [
    # generic two‑plus word pattern (strict title‑case)
    r"\b([A-ZÄÖÜ][a-zäöüß]+(?:\s+[A-ZÄÖÜ][a-zäöüß]+){1,2})\b",
    # Labelled variants
    r"Name\s*[:–-]\s*([A-ZÄÖÜ][a-zäöüß]+(?:\s+[A-ZÄÖÜ][a-zäöüß]+)+)",
    r"Vor-?\s*und\s*Nachname\s*[:–-]\s*([A-ZÄÖÜ][a-zäöüß]+(?:\s+[A-ZÄÖÜ][a-zäöüß]+)+)",
    r"Bewerbung\s+von\s+([A-ZÄÖÜ][a-zäöüß]+(?:\s+[A-ZÄÖÜ][a-zäöüß]+)+)",
    r"Lebenslauf\s+von\s+([A-ZÄÖÜ][a-zäöüß]+(?:\s+[A-ZÄÖÜ][a-zäöüß]+)+)"
]

_EMAIL_RE = re.compile(r"[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}")
_PHONE_RES = [
    re.compile(r"\+49[\s\-/]?\d{2,4}[\s\-/]?\d{6,8}"),   # intl German (min 6 digits after area)
    re.compile(r"0\d{2,4}[\s\-/]?\d{6,8}"),                # national (min 6 digits after area)
    re.compile(r"\(\d{2,4}\)\s?\d{6,8}"),                 # (0xx) xxx (min 6 digits)
    re.compile(r"(?<!\d)\d{4,5}[\s\-/]\d{6,8}(?!\d)")     # split w/ space or hyphen, avoid dates
]

# German Experience Patterns
_GERMAN_EXPERIENCE_PATTERNS = [
    # Years of experience patterns
    r"(\d+)\+?\s*Jahre?\s*(?:Berufs)?(?:erfahrung|tätigkeit|praxis)",
    r"(\d+)-(\d+)\s*Jahre?\s*(?:Berufs)?(?:erfahrung|tätigkeit)",
    r"(?:Berufs)?(?:erfahrung|tätigkeit)\s*:?\s*(\d+)\+?\s*Jahre?",
    r"(\d+)\+?\s*Jahre?\s*(?:in|im|als|mit)\s*[\w\s]+",
    r"seit\s*(\d+)\s*Jahre?n?\s*(?:tätig|beschäftigt|aktiv)",
    r"über\s*(\d+)\s*Jahre?\s*(?:Erfahrung|tätig|aktiv)",
    r"mehr\s*als\s*(\d+)\s*Jahre?\s*(?:Erfahrung|tätig)",

    # Date range patterns (YYYY-YYYY, MM/YYYY-MM/YYYY)
    r"(\d{4})\s*[-–]\s*(\d{4}|\w+)",  # 2018-2023 or 2018-heute
    r"(\d{1,2})/(\d{4})\s*[-–]\s*(\d{1,2})/(\d{4})",  # 01/2018-12/2023
    r"(\d{1,2})\.(\d{4})\s*[-–]\s*(\d{1,2})\.(\d{4})",  # 01.2018-12.2023

    # Job title with company patterns
    r"(\d{4})\s*[-–]\s*(?:\d{4}|heute|present):\s*([^,\n]+?)(?:\s+bei\s+([^,\n]+))?",
    r"([^,\n]+?)\s+bei\s+([^,\n]+?)\s*\((\d{4})\s*[-–]\s*(?:\d{4}|heute)\)",
]

# German Skills Patterns and Keywords
_GERMAN_TECHNICAL_SKILLS = [
    # Programming languages
    'python', 'java', 'javascript', 'typescript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust',
    'kotlin', 'swift', 'scala', 'r', 'matlab', 'sql', 'html', 'css',

    # German IT terms
    'programmierung', 'softwareentwicklung', 'webentwicklung', 'anwendungsentwicklung',
    'datenbank', 'datenbankentwicklung', 'algorithmus', 'datenstrukturen',
    'objektorientiert', 'funktional', 'agile entwicklung', 'scrum', 'kanban',
    'devops', 'continuous integration', 'versionskontrolle', 'git', 'svn',
    'docker', 'kubernetes', 'cloud computing', 'aws', 'azure', 'google cloud',

    # Frameworks and technologies
    'spring', 'spring boot', 'hibernate', 'react', 'angular', 'vue', 'node.js',
    'express', 'django', 'flask', 'laravel', 'symfony', 'bootstrap', 'jquery',
    'rest', 'api', 'microservices', 'soap', 'json', 'xml',

    # Databases
    'mysql', 'postgresql', 'oracle', 'mongodb', 'redis', 'elasticsearch',
    'sqlite', 'mariadb', 'cassandra', 'neo4j',

    # Tools and platforms
    'jenkins', 'gitlab', 'github', 'jira', 'confluence', 'slack', 'teams',
    'visual studio', 'intellij', 'eclipse', 'vscode', 'linux', 'windows', 'macos'
]

_GERMAN_SOFT_SKILLS = [
    'teamarbeit', 'kommunikation', 'führung', 'projektmanagement', 'problemlösung',
    'analytisches denken', 'kreativität', 'flexibilität', 'belastbarkeit',
    'selbstständigkeit', 'zuverlässigkeit', 'verantwortungsbewusstsein',
    'kundenorientierung', 'serviceorientierung', 'qualitätsbewusstsein',
    'zeitmanagement', 'organisationstalent', 'präsentationsfähigkeiten',
    'verhandlungsgeschick', 'konfliktlösung', 'mentoring', 'coaching'
]

# German Education Keywords
_GERMAN_EDUCATION_KEYWORDS = [
    'bachelor', 'master', 'diplom', 'magister', 'promotion', 'doktor', 'phd',
    'studium', 'studiengang', 'fachrichtung', 'schwerpunkt', 'abschluss',
    'universität', 'hochschule', 'fachhochschule', 'technische universität',
    'ausbildung', 'berufsausbildung', 'lehre', 'praktikum', 'weiterbildung',
    'fortbildung', 'schulung', 'kurs', 'seminar', 'zertifikat', 'zertifizierung',
    'abitur', 'fachabitur', 'realschulabschluss', 'hauptschulabschluss'
]

# Experience level indicators
_EXPERIENCE_LEVELS = {
    'junior': ['junior', 'einsteiger', 'berufsanfänger', 'absolvent', 'trainee', 'praktikant'],
    'mid': ['erfahren', 'fortgeschritten', 'spezialist', 'fachkraft', 'experte'],
    'senior': ['senior', 'leitend', 'führung', 'manager', 'direktor', 'chef', 'head', 'lead']
}


# ---------- Core class -----------------------------------------------------

class BilingualCVExtractorPatched:
    """Enhanced extractor for German CVs with comprehensive field extraction."""

    # -- public API ---------------------------------------------------------
    def extract_cv_data(self, file_path: str, fields: List[str] = None) -> dict[str, str]:
        """Extract specified fields from CV file"""
        if fields is None:
            fields = ['name', 'email', 'phone', 'experience', 'skills', 'education']

        text = self._extract_text_from_file(file_path)
        if not text:
            return {"error": "Could not read CV text"}

        result = {}
        filename = os.path.basename(file_path)

        # Extract requested fields
        if 'name' in fields:
            result['name'] = self._extract_name(text, filename) or ""
        if 'email' in fields:
            result['email'] = self._extract_email(text) or ""
        if 'phone' in fields:
            result['phone'] = self._extract_phone(text) or ""
        if 'experience' in fields:
            result['experience'] = self._extract_experience(text) or ""
        if 'skills' in fields:
            result['skills'] = self._extract_skills(text) or ""
        if 'education' in fields:
            result['education'] = self._extract_education(text) or ""

        # Optional: Add seniority level classification
        if 'seniority' in fields:
            result['seniority'] = self._classify_seniority(text, result.get('experience', ''))

        return result

    # -- implementation -----------------------------------------------------
    def _extract_text_from_file(self, file_path: str) -> str:
        """Extract text from PDF or DOCX file"""
        file_ext = Path(file_path).suffix.lower()

        if file_ext == '.pdf':
            try:
                with fitz.open(file_path) as doc:
                    return "\f".join(page.get_text("text") for page in doc)
            except Exception as e:
                print(f"Error reading PDF: {e}")
                return ""

        elif file_ext == '.docx' and DOCX_AVAILABLE:
            try:
                doc = Document(file_path)
                text_parts = []
                for paragraph in doc.paragraphs:
                    if paragraph.text.strip():
                        text_parts.append(paragraph.text.strip())
                return "\n".join(text_parts)
            except Exception as e:
                print(f"Error reading DOCX: {e}")
                return ""

        else:
            print(f"Unsupported file format: {file_ext}")
            return ""

    # 1) NAME ---------------------------------------------------------------
    def _extract_name(self, text: str, filename: str = "") -> str | None:
        # A) heading detector on first page
        first_page = text.split("\f", 1)[0] if "\f" in text else text
        heading = self._heading_name(first_page)
        if heading:
            return heading

        # B) two-line split detector (first\nlast)
        lines = first_page.splitlines()
        for i in range(len(lines)-1):
            l1, l2 = lines[i].strip(), lines[i+1].strip()
            clean_l1 = self._clean_name(l1)
            clean_l2 = self._clean_name(l2)
            if (clean_l1 and clean_l2 and
                self._looks_like_name(clean_l1) and self._looks_like_name(clean_l2)):
                return f"{clean_l1.title()} {clean_l2.title()}"

        # C) regex cascade (without IGNORECASE for generic)
        for idx, pat in enumerate(_GERMAN_NAME_PATTERNS):
            flags = 0 if idx == 0 else re.I
            m = re.search(pat, text, flags)
            if m:
                cand = m.group(1).strip()
                # Clean up the candidate name
                cand = self._clean_name(cand)
                if cand and not self._bad_token(cand):
                    return cand.title()

        # D) fallback: filename stem
        stem = Path(filename).stem.replace("_", " ").replace("-", " ")
        if self._looks_like_name(stem):
            return stem.title()

        return None

    # 1a) helpers
    def _heading_name(self, first_page: str) -> str | None:
        lines = [l.strip() for l in first_page.splitlines() if l.strip()][:20]
        for line in lines:
            # Clean the line first
            cleaned_line = self._clean_name(line)
            if cleaned_line and self._looks_like_name(cleaned_line):
                return cleaned_line.title()
        return None

    def _looks_like_name(self, txt: str) -> bool:
        tokens = txt.split()
        if not 1 <= len(tokens) <= 3:
            return False
        if any(self._bad_token(t) for t in tokens):
            return False
        return all(
            (t.isupper() and len(t) > 1) or
            (t[0].isupper() and t[1:].islower())
            for t in tokens
        )

    def _bad_token(self, token: str) -> bool:
        return token.lower().rstrip(".,") in _BAD_TOKENS

    def _clean_name(self, name: str) -> str:
        """Clean up extracted name by removing common prefixes and suffixes"""
        # Remove common prefixes
        prefixes_to_remove = ["lebenslauf:", "name:", "bewerbung von", "cv:", "curriculum vitae"]
        name_lower = name.lower()

        for prefix in prefixes_to_remove:
            if name_lower.startswith(prefix):
                name = name[len(prefix):].strip()
                break

        # Remove common suffixes and clean up
        name = re.sub(r'\s*[:–-]\s*$', '', name)  # Remove trailing colons/dashes
        name = re.sub(r'\s+', ' ', name)  # Normalize whitespace

        # Split and check each part
        parts = name.split()
        clean_parts = []

        for part in parts:
            # Skip parts that are clearly not names
            if (len(part) < 2 or
                part.lower() in _BAD_TOKENS or
                part.isdigit() or
                '@' in part or
                part.lower() in ['von', 'der', 'die', 'das', 'und', 'the', 'of', 'and']):
                continue
            clean_parts.append(part)

        # Return cleaned name if we have 1-3 valid parts
        if 1 <= len(clean_parts) <= 3:
            return ' '.join(clean_parts)

        return ""

    # 2) EMAIL --------------------------------------------------------------
    def _extract_email(self, text: str) -> str | None:
        m = _EMAIL_RE.search(text)
        return m.group(0) if m else None

    # 3) PHONE --------------------------------------------------------------
    def _extract_phone(self, text: str) -> str | None:
        for reg in _PHONE_RES:
            m = reg.search(text)
            if m:
                phone = m.group(0)
                # Filter out obvious non-phone numbers
                if self._is_valid_phone(phone):
                    return phone
        return None

    # 4) EXPERIENCE ---------------------------------------------------------
    def _extract_experience(self, text: str) -> str:
        """Extract work experience from German CV text"""
        text_lower = text.lower()

        # Try to extract years of experience first
        years_exp = self._extract_years_of_experience(text)
        if years_exp:
            return years_exp

        # Try to extract job positions and companies
        positions = self._extract_job_positions(text)
        if positions:
            return positions

        # Try to extract date ranges and calculate experience
        date_ranges = self._extract_date_ranges(text)
        if date_ranges:
            return date_ranges

        # Look for experience level indicators
        level_indicators = self._extract_experience_level(text_lower)
        if level_indicators:
            return level_indicators

        return "Berufserfahrung nicht spezifiziert"

    def _extract_years_of_experience(self, text: str) -> str:
        """Extract explicit years of experience mentions"""
        for pattern in _GERMAN_EXPERIENCE_PATTERNS[:7]:  # First 7 are years patterns
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                if isinstance(matches[0], tuple):
                    # Range pattern (e.g., "2-4 Jahre")
                    return f"{matches[0][0]}-{matches[0][1]} Jahre Berufserfahrung"
                else:
                    # Single number pattern
                    years = matches[0]
                    return f"{years} Jahre Berufserfahrung"
        return ""

    def _extract_job_positions(self, text: str) -> str:
        """Extract job positions and companies"""
        lines = text.split('\n')
        positions = []

        # Look for lines that contain job titles and companies
        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Pattern: "YYYY-YYYY: Position bei Company"
            job_match = re.search(r'(\d{4})\s*[-–]\s*(?:(\d{4})|heute|present):\s*([^,\n]+?)(?:\s+bei\s+([^,\n]+))?', line, re.IGNORECASE)
            if job_match:
                start_year, end_year, position, company = job_match.groups()
                end_year = end_year or "heute"
                if company:
                    positions.append(f"{start_year}-{end_year}: {position.strip()} bei {company.strip()}")
                else:
                    positions.append(f"{start_year}-{end_year}: {position.strip()}")
                continue

            # Pattern: "Position bei Company (YYYY-YYYY)"
            job_match2 = re.search(r'([^,\n]+?)\s+bei\s+([^,\n]+?)\s*\((\d{4})\s*[-–]\s*(?:(\d{4})|heute)\)', line, re.IGNORECASE)
            if job_match2:
                position, company, start_year, end_year = job_match2.groups()
                end_year = end_year or "heute"
                positions.append(f"{start_year}-{end_year}: {position.strip()} bei {company.strip()}")

        if positions:
            # Return the most recent position or summarize
            if len(positions) == 1:
                return positions[0]
            else:
                return f"Aktuelle Position: {positions[0]} (Insgesamt {len(positions)} Positionen)"

        return ""

    def _extract_date_ranges(self, text: str) -> str:
        """Extract and calculate experience from date ranges"""
        current_year = datetime.now().year
        total_months = 0
        positions = []

        # Look for date range patterns
        date_patterns = _GERMAN_EXPERIENCE_PATTERNS[7:10]  # Date range patterns

        for pattern in date_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if len(match) >= 2:
                    try:
                        start_year = int(match[0])
                        end_year = current_year if match[1].lower() in ['heute', 'present'] else int(match[1])
                        months = (end_year - start_year) * 12
                        total_months += months
                        positions.append(f"{start_year}-{end_year if end_year != current_year else 'heute'}")
                    except (ValueError, IndexError):
                        continue

        if total_months > 0:
            years = total_months // 12
            if years > 0:
                return f"{years} Jahre Berufserfahrung ({', '.join(positions)})"

        return ""

    def _extract_experience_level(self, text_lower: str) -> str:
        """Extract experience level indicators"""
        for level, keywords in _EXPERIENCE_LEVELS.items():
            for keyword in keywords:
                if keyword in text_lower:
                    if level == 'junior':
                        return "Junior Level (0-2 Jahre)"
                    elif level == 'mid':
                        return "Mittleres Level (2-5 Jahre)"
                    elif level == 'senior':
                        return "Senior Level (5+ Jahre)"
        return ""

    # 5) SKILLS -------------------------------------------------------------
    def _extract_skills(self, text: str) -> str:
        """Extract skills from German CV text"""
        found_skills = set()
        text_lower = text.lower()

        # Extract technical skills (with word boundary checks for short skills)
        for skill in _GERMAN_TECHNICAL_SKILLS:
            skill_lower = skill.lower()
            # For single letter skills like 'r', require word boundaries
            if len(skill) == 1:
                if re.search(r'\b' + re.escape(skill_lower) + r'\b', text_lower):
                    # Additional check: make sure it's in a skills context
                    if self._is_in_skills_context(text, skill):
                        found_skills.add(skill.upper())  # Single letters in uppercase
            else:
                if skill_lower in text_lower:
                    found_skills.add(skill.title())

        # Extract soft skills
        for skill in _GERMAN_SOFT_SKILLS:
            if skill.lower() in text_lower:
                found_skills.add(skill.title())

        # Look for skills sections specifically
        skills_from_sections = self._extract_skills_from_sections(text)
        found_skills.update(skills_from_sections)

        if found_skills:
            # Convert to sorted list and limit to reasonable number
            skills_list = sorted(list(found_skills))[:15]
            return ", ".join(skills_list)

        return "Fähigkeiten nicht spezifiziert"

    def _is_in_skills_context(self, text: str, skill: str) -> bool:
        """Check if a skill appears in a skills-related context"""
        lines = text.split('\n')
        skill_context_keywords = ['fähigkeiten', 'kenntnisse', 'skills', 'kompetenzen', 'programmierung']

        for line in lines:
            if skill.lower() in line.lower():
                # Check if this line or nearby lines contain skills context
                line_lower = line.lower()
                if any(keyword in line_lower for keyword in skill_context_keywords):
                    return True
                # Check if line contains other programming languages (indicating skills context)
                prog_langs = ['python', 'java', 'javascript', 'c++', 'c#']
                if any(lang in line_lower for lang in prog_langs):
                    return True

        return False

    def _extract_skills_from_sections(self, text: str) -> set:
        """Extract skills from dedicated skills sections"""
        skills = set()
        lines = text.split('\n')

        in_skills_section = False
        skills_section_keywords = ['fähigkeiten', 'kenntnisse', 'skills', 'kompetenzen', 'qualifikationen']

        for line in lines:
            line_lower = line.lower().strip()

            # Check if we're entering a skills section
            if any(keyword in line_lower for keyword in skills_section_keywords):
                in_skills_section = True
                continue

            # Check if we're leaving skills section (new section starts)
            if in_skills_section and line_lower and line_lower.endswith(':') and len(line_lower.split()) <= 3:
                if not any(keyword in line_lower for keyword in skills_section_keywords):
                    in_skills_section = False
                    continue

            # Extract skills from current line if in skills section
            if in_skills_section and line.strip():
                # Split by common separators
                skill_candidates = re.split(r'[,;•·\-\n]', line)
                for candidate in skill_candidates:
                    candidate = candidate.strip()
                    if candidate and len(candidate) > 1 and not candidate.isdigit():
                        # Clean up the candidate
                        candidate = re.sub(r'^[•·\-\s]+', '', candidate)
                        candidate = re.sub(r'[•·\-\s]+$', '', candidate)
                        if candidate and len(candidate) > 1:
                            skills.add(candidate.title())

        return skills

    # 6) EDUCATION ----------------------------------------------------------
    def _extract_education(self, text: str) -> str:
        """Extract education information from German CV text"""
        text_lower = text.lower()
        found_education = []

        # Look for German education keywords
        for edu_keyword in _GERMAN_EDUCATION_KEYWORDS:
            if edu_keyword in text_lower:
                # Find the context around the keyword
                lines = text.split('\n')
                for line in lines:
                    if edu_keyword in line.lower():
                        clean_line = line.strip()
                        if clean_line and len(clean_line) > len(edu_keyword):
                            found_education.append(clean_line)
                        break

        # Look for specific degree patterns
        degree_patterns = [
            r'(Bachelor|Master|Diplom|Magister|Doktor|Dr\.)\s+(?:of\s+)?([A-Za-zäöüß\s]+)',
            r'(B\.Sc\.|M\.Sc\.|B\.A\.|M\.A\.|Dipl\.-Ing\.)\s+([A-Za-zäöüß\s]+)',
            r'Studium\s+(?:der\s+)?([A-Za-zäöüß\s]+)',
            r'Ausbildung\s+(?:zum\s+|zur\s+)?([A-Za-zäöüß\s]+)'
        ]

        for pattern in degree_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    degree_info = ' '.join(match).strip()
                else:
                    degree_info = match.strip()
                if degree_info and degree_info not in found_education:
                    found_education.append(degree_info)

        if found_education:
            # Return the most relevant education entries
            return "; ".join(found_education[:3])

        return "Ausbildung nicht spezifiziert"

    # 7) SENIORITY CLASSIFICATION ------------------------------------------
    def _classify_seniority(self, text: str, experience: str) -> str:
        """Classify seniority level based on experience and text content"""
        text_lower = text.lower()

        # Extract years from experience string
        years_match = re.search(r'(\d+)\s*Jahre?', experience)
        years = int(years_match.group(1)) if years_match else 0

        # Check for explicit level indicators in text
        for level, keywords in _EXPERIENCE_LEVELS.items():
            for keyword in keywords:
                if keyword in text_lower:
                    return level.title()

        # Classify based on years of experience
        if years >= 7:
            return "Senior"
        elif years >= 3:
            return "Mid"
        elif years >= 1:
            return "Junior"
        else:
            # Look for other indicators
            if any(word in text_lower for word in ['manager', 'leiter', 'direktor', 'chef']):
                return "Senior"
            elif any(word in text_lower for word in ['spezialist', 'experte', 'fachkraft']):
                return "Mid"
            else:
                return "Junior"

    # 8) EDUCATION-JOB MISMATCH DETECTION ----------------------------------
    def detect_education_job_mismatch(self, cv_text: str, job_description: str) -> Dict[str, str]:
        """Detect potential mismatches between education and job requirements"""
        cv_education = self._extract_education(cv_text).lower()
        job_lower = job_description.lower()

        # Common field mappings
        education_fields = {
            'informatik': ['software', 'programming', 'development', 'it', 'computer'],
            'maschinenbau': ['mechanical', 'engineering', 'manufacturing', 'production'],
            'elektrotechnik': ['electrical', 'electronics', 'automation', 'control'],
            'bwl': ['business', 'management', 'finance', 'marketing', 'sales'],
            'wirtschaft': ['business', 'economics', 'finance', 'management']
        }

        mismatches = []
        cv_field = None

        # Identify CV education field
        for field, keywords in education_fields.items():
            if field in cv_education:
                cv_field = field
                break

        if cv_field:
            # Check if job requires different field
            job_keywords = education_fields[cv_field]
            if not any(keyword in job_lower for keyword in job_keywords):
                # Look for other fields in job description
                for other_field, other_keywords in education_fields.items():
                    if other_field != cv_field and any(keyword in job_lower for keyword in other_keywords):
                        mismatches.append(f"Ausbildung: {cv_field.title()}, Job erfordert: {other_field.title()}")

        return {
            'cv_education_field': cv_field or "Unbekannt",
            'mismatches': "; ".join(mismatches) if mismatches else "Keine Unstimmigkeiten erkannt"
        }

    def _is_valid_phone(self, phone: str) -> bool:
        """Check if extracted phone number is likely valid"""
        # Remove all non-digits to check
        digits_only = re.sub(r'\D', '', phone)

        # Must have at least 6 digits for a valid phone
        if len(digits_only) < 6:
            return False

        # Avoid common date patterns
        if re.match(r'^\d{2}[-/]\d{4}$', phone):  # MM/YYYY or MM-YYYY
            return False
        if re.match(r'^\d{3}[-/]\d{4}$', phone):  # MMM/YYYY or MMM-YYYY
            return False

        # Must not be all the same digit
        if len(set(digits_only)) == 1:
            return False

        return True


# Backward compatibility alias
BilingualCVExtractor = BilingualCVExtractorPatched

# Convenience function for quick extraction
def extract_german_cv_data(file_path: str, fields: List[str] = None) -> Dict[str, str]:
    """Quick function to extract German CV data"""
    extractor = BilingualCVExtractorPatched()
    return extractor.extract_cv_data(file_path, fields)
