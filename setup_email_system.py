"""
Complete Email System Setup for BAUCH HR Management
Sets up Listmonk professional email system
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path


def install_dependencies():
    """Install required Python packages"""
    print("📦 Installing required packages...")
    packages = ["requests"]
    
    for package in packages:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ Installed {package}")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package}")
            return False
    
    return True


def setup_listmonk_config():
    """Setup Listmonk configuration"""
    email_dir = Path("email_system")
    config_file = email_dir / "config.toml"
    
    if config_file.exists():
        print("✅ Listmonk config already exists")
        return True
    
    print("🔧 Setting up Listmonk configuration...")
    
    # Basic configuration for HR use
    config_content = """
[app]
address = "0.0.0.0:9000"
admin_username = "admin"
admin_password = "listmonk"

[db]
host = "localhost"
port = 5432
user = "listmonk"
password = "listmonk"
database = "listmonk"
ssl_mode = "disable"
max_open = 25
max_idle = 25
max_lifetime = "300s"

# For SQLite (simpler setup)
# Uncomment these lines and comment out PostgreSQL settings above
# [db]
# host = ""
# port = 0
# user = ""
# password = ""
# database = "listmonk.db"
# ssl_mode = "disable"

[smtp]
host = "localhost"
port = 587
auth_protocol = "plain"
username = ""
password = ""
hello_hostname = ""
max_conns = 10
max_msg_retries = 2
idle_timeout = "15s"
wait_timeout = "5s"
tls_enabled = true
tls_skip_verify = false

[upload]
provider = "filesystem"
filesystem_upload_path = "uploads"
filesystem_upload_uri = "/uploads"

[privacy]
individual_tracking = false
unsubscribe_header = true
allow_blocklist = true
allow_export = true
allow_wipe = true
exportable = ["profile", "subscriptions", "campaign_views", "link_clicks"]

[security]
enable_captcha = false
captcha_key = ""
captcha_secret = ""

[admin]
username = "admin"
password = "listmonk"
"""
    
    try:
        with open(config_file, 'w') as f:
            f.write(config_content)
        print("✅ Created Listmonk configuration")
        return True
    except Exception as e:
        print(f"❌ Failed to create config: {e}")
        return False


def check_listmonk_health():
    """Check if Listmonk is running and healthy"""
    try:
        response = requests.get("http://localhost:9000/api/health", timeout=5)
        return response.status_code == 200
    except:
        return False


def start_listmonk_server():
    """Start Listmonk server"""
    email_dir = Path("email_system")
    listmonk_exe = email_dir / "listmonk.exe"
    
    if not listmonk_exe.exists():
        print("❌ Listmonk executable not found!")
        print("Please run the download script first.")
        return False
    
    if check_listmonk_health():
        print("✅ Listmonk is already running")
        return True
    
    print("🚀 Starting Listmonk server...")
    
    try:
        # Change to email directory
        original_dir = os.getcwd()
        os.chdir(email_dir)
        
        # Start Listmonk
        process = subprocess.Popen(
            [str(listmonk_exe)],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
        )
        
        # Return to original directory
        os.chdir(original_dir)
        
        # Wait for server to start
        print("⏳ Waiting for Listmonk to start...")
        for i in range(30):
            if check_listmonk_health():
                print("✅ Listmonk started successfully!")
                return True
            time.sleep(1)
            print(f"   Checking... ({i+1}/30)")
        
        print("❌ Listmonk failed to start")
        return False
        
    except Exception as e:
        print(f"❌ Error starting Listmonk: {e}")
        return False


def test_email_integration():
    """Test the email integration"""
    print("🧪 Testing email integration...")
    
    try:
        from listmonk_integration import ListmonkEmailService
        
        # Test connection
        email_service = ListmonkEmailService()
        
        # Test getting templates
        templates = email_service.get_default_templates('de')
        if templates:
            print("✅ Email templates loaded successfully")
            print(f"   Available templates: {list(templates.keys())}")
        
        print("✅ Email integration test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Email integration test failed: {e}")
        return False


def main():
    """Main setup function"""
    print("📧 BAUCH HR - Professional Email System Setup")
    print("=" * 60)
    print("Setting up Listmonk professional email marketing system...")
    print()
    
    # Step 1: Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies")
        return False
    
    # Step 2: Setup configuration
    if not setup_listmonk_config():
        print("❌ Failed to setup configuration")
        return False
    
    # Step 3: Start Listmonk server
    if not start_listmonk_server():
        print("❌ Failed to start Listmonk server")
        print("\n💡 Troubleshooting:")
        print("1. Make sure email_system/listmonk.exe exists")
        print("2. Check if port 9000 is available")
        print("3. Try running: cd email_system && ./listmonk.exe --install")
        return False
    
    # Step 4: Test integration
    if not test_email_integration():
        print("⚠️ Email integration test failed, but server is running")
    
    print("\n🎉 Email System Setup Complete!")
    print("=" * 60)
    print("✅ Listmonk professional email server is running")
    print("📧 Email system integrated with HR application")
    print()
    print("🌐 Listmonk Admin Panel:")
    print("   URL: http://localhost:9000")
    print("   Username: admin")
    print("   Password: listmonk")
    print()
    print("📋 Next Steps:")
    print("1. Configure SMTP settings in Listmonk admin panel")
    print("2. Test email sending from HR application")
    print("3. Monitor email campaigns and delivery")
    print()
    print("🚀 Your HR application now has professional email capabilities!")
    
    return True


if __name__ == "__main__":
    main()
