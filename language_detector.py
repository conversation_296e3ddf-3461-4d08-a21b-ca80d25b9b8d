"""
Language Detection Module for Bilingual HR Management System
Detects language of CV and job description texts
"""

import re
from typing import Dict, Optional, Tuple
from langdetect import detect, DetectorFactory
from langdetect.lang_detect_exception import LangDetectException


class LanguageDetector:
    def __init__(self):
        # Set seed for consistent results
        DetectorFactory.seed = 0
        
        # German language indicators
        self.german_indicators = {
            'words': [
                'und', 'der', 'die', 'das', 'ich', 'bin', 'ist', 'sind', 'haben', 'hat',
                'mit', 'für', 'von', 'zu', 'auf', 'bei', 'nach', 'über', 'unter',
                'bewerbung', 'lebenslauf', 'berufserfahrung', 'ausbildung', 'studium',
                'fähigkeiten', 'kenntnisse', 'sprachen', 'qualifikationen',
                'arbeitgeber', 'unternehmen', 'position', 'stelle', 'beruf',
                'sehr geehrte', 'sehr geehrter', 'mit freundlichen grüßen',
                'freundlichen grüßen', 'hochachtungsvoll'
            ],
            'patterns': [
                r'\b(?:sehr\s+geehrte[rs]?)\b',
                r'\b(?:mit\s+freundlichen\s+grüßen)\b',
                r'\b(?:freundlichen\s+grüßen)\b',
                r'\b(?:bewerbung\s+(?:als|für))\b',
                r'\b(?:geboren\s+am)\b',
                r'\b(?:wohnhaft\s+in)\b'
            ]
        }
        
        # English language indicators
        self.english_indicators = {
            'words': [
                'and', 'the', 'of', 'to', 'in', 'for', 'with', 'on', 'at', 'by',
                'resume', 'curriculum', 'vitae', 'experience', 'education', 'skills',
                'qualifications', 'employment', 'position', 'job', 'work',
                'dear sir', 'dear madam', 'sincerely', 'regards', 'yours faithfully'
            ],
            'patterns': [
                r'\b(?:dear\s+(?:sir|madam|mr|ms|mrs))\b',
                r'\b(?:yours\s+(?:sincerely|faithfully))\b',
                r'\b(?:best\s+regards)\b',
                r'\b(?:application\s+for)\b',
                r'\b(?:born\s+on)\b',
                r'\b(?:residing\s+in)\b'
            ]
        }
    
    def detect_language(self, text: str) -> Tuple[str, float]:
        """
        Detect the primary language of the text
        Returns: (language_code, confidence_score)
        """
        if not text or len(text.strip()) < 10:
            return 'unknown', 0.0
        
        try:
            # Use langdetect library for primary detection
            detected_lang = detect(text)
            
            # Calculate confidence based on language indicators
            confidence = self._calculate_confidence(text, detected_lang)
            
            # Map language codes
            if detected_lang == 'de':
                return 'german', confidence
            elif detected_lang == 'en':
                return 'english', confidence
            else:
                # Fallback to manual detection for unsupported languages
                manual_lang, manual_conf = self._manual_language_detection(text)
                return manual_lang, manual_conf
                
        except LangDetectException:
            # Fallback to manual detection
            return self._manual_language_detection(text)
    
    def _calculate_confidence(self, text: str, detected_lang: str) -> float:
        """Calculate confidence score based on language indicators"""
        text_lower = text.lower()
        
        if detected_lang == 'de':
            indicators = self.german_indicators
        elif detected_lang == 'en':
            indicators = self.english_indicators
        else:
            return 0.5  # Default confidence for other languages
        
        # Count word indicators
        word_matches = sum(1 for word in indicators['words'] if word in text_lower)
        word_score = min(word_matches / 10, 1.0)  # Normalize to 0-1
        
        # Count pattern indicators
        pattern_matches = sum(1 for pattern in indicators['patterns'] 
                            if re.search(pattern, text_lower, re.IGNORECASE))
        pattern_score = min(pattern_matches / 3, 1.0)  # Normalize to 0-1
        
        # Combined confidence score
        confidence = (word_score * 0.7) + (pattern_score * 0.3)
        return min(max(confidence, 0.1), 0.95)  # Clamp between 0.1 and 0.95
    
    def _manual_language_detection(self, text: str) -> Tuple[str, float]:
        """Manual language detection based on indicators"""
        text_lower = text.lower()
        
        # Count German indicators
        german_score = 0
        for word in self.german_indicators['words']:
            german_score += text_lower.count(word)
        for pattern in self.german_indicators['patterns']:
            german_score += len(re.findall(pattern, text_lower, re.IGNORECASE)) * 2
        
        # Count English indicators
        english_score = 0
        for word in self.english_indicators['words']:
            english_score += text_lower.count(word)
        for pattern in self.english_indicators['patterns']:
            english_score += len(re.findall(pattern, text_lower, re.IGNORECASE)) * 2
        
        # Determine language based on scores
        total_score = german_score + english_score
        if total_score == 0:
            return 'unknown', 0.0
        
        if german_score > english_score:
            confidence = german_score / total_score
            return 'german', min(confidence, 0.9)
        elif english_score > german_score:
            confidence = english_score / total_score
            return 'english', min(confidence, 0.9)
        else:
            return 'mixed', 0.5
    
    def is_bilingual_text(self, text: str) -> bool:
        """Check if text contains both German and English content"""
        text_lower = text.lower()
        
        # Check for German indicators
        german_found = any(word in text_lower for word in self.german_indicators['words'][:5])
        german_patterns = any(re.search(pattern, text_lower, re.IGNORECASE) 
                            for pattern in self.german_indicators['patterns'])
        
        # Check for English indicators
        english_found = any(word in text_lower for word in self.english_indicators['words'][:5])
        english_patterns = any(re.search(pattern, text_lower, re.IGNORECASE) 
                             for pattern in self.english_indicators['patterns'])
        
        return (german_found or german_patterns) and (english_found or english_patterns)
    
    def get_language_info(self, text: str) -> Dict[str, any]:
        """Get comprehensive language information about the text"""
        language, confidence = self.detect_language(text)
        is_bilingual = self.is_bilingual_text(text)
        
        return {
            'primary_language': language,
            'confidence': confidence,
            'is_bilingual': is_bilingual,
            'text_length': len(text),
            'word_count': len(text.split()),
            'recommendation': self._get_processing_recommendation(language, confidence, is_bilingual)
        }
    
    def _get_processing_recommendation(self, language: str, confidence: float, is_bilingual: bool) -> str:
        """Get recommendation for how to process the text"""
        if is_bilingual:
            return "Use bilingual processing with both German and English NLP models"
        elif language == 'german' and confidence > 0.7:
            return "Use German NLP model for processing"
        elif language == 'english' and confidence > 0.7:
            return "Use English NLP model for processing"
        elif confidence < 0.5:
            return "Use both German and English models due to low confidence"
        else:
            return f"Use {language} NLP model with fallback to bilingual processing"


# Utility functions for easy integration
def detect_cv_language(cv_text: str) -> str:
    """Quick function to detect CV language"""
    detector = LanguageDetector()
    language, confidence = detector.detect_language(cv_text)
    return language if confidence > 0.6 else 'mixed'


def should_use_german_processing(text: str) -> bool:
    """Determine if German processing should be used"""
    detector = LanguageDetector()
    language, confidence = detector.detect_language(text)
    return language == 'german' or (language == 'mixed' and confidence > 0.4)


def should_use_english_processing(text: str) -> bool:
    """Determine if English processing should be used"""
    detector = LanguageDetector()
    language, confidence = detector.detect_language(text)
    return language == 'english' or (language == 'mixed' and confidence > 0.4)
