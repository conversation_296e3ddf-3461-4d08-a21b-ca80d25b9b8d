"""
Integration test for Email Service with HR Database
Shows how email service integrates with the HR management system
"""

import os
import tempfile
import json
from unittest.mock import patch, MagicMock
from email_service import EmailService


def test_email_integration_with_hr_system():
    """Test email service integration with HR database"""
    print("🔗 Email Service Integration Test")
    print("-" * 50)
    
    try:
        # Try to import HR database
        from hr_database import HRDatabase
        
        # Create temporary database
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
            db_path = tmp_db.name
        
        print("1. Setting up test database...")
        hr_db = HRDatabase(f'sqlite:///{db_path}')
        
        # Create test job
        job = hr_db.add_job(
            title="Python Developer",
            description="We are looking for a skilled Python developer"
        )
        print(f"   ✓ Created job: {job.title}")
        
        # Create test CVs
        cv_data = [
            {
                "filename": "john_doe.pdf",
                "content": "<PERSON> - Python Developer with 3 years experience",
                "job_title": "Python Developer",
                "candidate_name": "<PERSON>",
                "language": "english",
                "extracted_data": json.dumps({
                    "name": "<PERSON>e",
                    "email": "<EMAIL>",
                    "skills": ["Python", "Django", "PostgreSQL"]
                })
            },
            {
                "filename": "jane_smith.pdf", 
                "content": "Jane Smith - Senior Python Developer with 5 years experience",
                "job_title": "Python Developer",
                "candidate_name": "Jane Smith",
                "language": "english",
                "extracted_data": json.dumps({
                    "name": "Jane Smith",
                    "email": "<EMAIL>",
                    "skills": ["Python", "Flask", "MongoDB", "AWS"]
                })
            }
        ]
        
        cvs = []
        for cv_info in cv_data:
            cv = hr_db.add_cv(**cv_info)
            cvs.append(cv)
            print(f"   ✓ Created CV: {cv.candidate_name}")
        
        print("\n2. Setting up email service...")
        email_service = EmailService(
            mail_server='smtp.test.com',
            mail_port=587,
            mail_username='<EMAIL>',
            mail_password='test_password',
            default_sender='<EMAIL>'
        )
        
        # Mock the actual email sending
        with patch.object(email_service, 'send_email', return_value=True) as mock_send:
            print("\n3. Sending application received emails...")
            
            # Get email template
            templates = email_service.get_default_templates('en')
            template = templates['application_received']
            
            # Prepare recipients from CV data
            recipients = []
            for cv in cvs:
                extracted_data = json.loads(cv.extracted_data)
                recipient = {
                    'email': extracted_data['email'],
                    'name': extracted_data['name'],
                    'job_title': cv.job_title
                }
                recipients.append(recipient)
            
            # Send bulk emails
            result = email_service.send_bulk_emails(
                recipients=recipients,
                subject="Application Received - Python Developer Position",
                template=template,
                async_send=False
            )
            
            print(f"   ✓ Emails sent: {result['success']}")
            print(f"   ✓ Failed: {result['failed']}")
            print(f"   ✓ Total recipients: {len(recipients)}")
            
            # Verify email calls
            assert mock_send.call_count == len(recipients)
            print(f"   ✓ Email service called {mock_send.call_count} times")
        
        print("\n4. Testing interview invitation workflow...")
        
        # Simulate updating CV status and sending interview invitations
        with patch.object(email_service, 'send_email', return_value=True) as mock_send:
            interview_template = templates['interview_invitation']
            
            # Select first candidate for interview
            selected_cv = cvs[0]
            extracted_data = json.loads(selected_cv.extracted_data)
            
            interview_data = {
                'email': extracted_data['email'],
                'name': extracted_data['name'],
                'job_title': selected_cv.job_title,
                'interview_date': 'March 20, 2024',
                'interview_time': '2:00 PM',
                'interview_location': 'Conference Room A, 5th Floor'
            }
            
            # Send interview invitation
            success = email_service.send_email(
                recipient=interview_data['email'],
                subject=f"Interview Invitation - {interview_data['job_title']}",
                body_html=interview_template.format(**interview_data)
            )
            
            print(f"   ✓ Interview invitation sent to {interview_data['name']}: {success}")
            assert mock_send.called
        
        print("\n5. Testing status update emails...")
        
        with patch.object(email_service, 'send_email', return_value=True) as mock_send:
            status_template = templates['status_update']
            
            # Send different status updates
            status_updates = [
                {
                    'cv': cvs[0],
                    'status': 'moved to next round',
                    'additional_info': 'Congratulations! Please prepare for the technical interview.'
                },
                {
                    'cv': cvs[1], 
                    'status': 'not selected',
                    'additional_info': 'Thank you for your interest. We will keep your profile for future opportunities.'
                }
            ]
            
            for update in status_updates:
                cv = update['cv']
                extracted_data = json.loads(cv.extracted_data)
                
                email_data = {
                    'email': extracted_data['email'],
                    'name': extracted_data['name'],
                    'job_title': cv.job_title,
                    'status': update['status'],
                    'additional_info': update['additional_info']
                }
                
                success = email_service.send_email(
                    recipient=email_data['email'],
                    subject=f"Application Status Update - {email_data['job_title']}",
                    body_html=status_template.format(**email_data)
                )
                
                print(f"   ✓ Status update sent to {email_data['name']}: {update['status']}")
        
        # Cleanup
        os.unlink(db_path)
        
        print("\n✅ Email integration test completed successfully!")
        return True
        
    except ImportError:
        print("❌ HR Database not available - skipping integration test")
        print("   This test requires hr_database.py to be available")
        return False
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False


def test_email_service_standalone():
    """Test email service functionality without HR database"""
    print("\n📧 Standalone Email Service Test")
    print("-" * 50)
    
    try:
        # Test basic functionality
        email_service = EmailService(
            mail_server='smtp.test.com',
            mail_port=587,
            mail_username='<EMAIL>',
            mail_password='test_password',
            default_sender='<EMAIL>'
        )
        
        print("1. Testing template retrieval...")
        templates = email_service.get_default_templates('en')
        assert len(templates) == 3
        print(f"   ✓ Retrieved {len(templates)} templates")
        
        print("\n2. Testing template formatting...")
        test_data = {
            'name': 'Test User',
            'job_title': 'Test Position',
            'status': 'under review',
            'additional_info': 'We will contact you soon.'
        }
        
        formatted = templates['status_update'].format(**test_data)
        assert 'Test User' in formatted
        assert 'Test Position' in formatted
        print("   ✓ Template formatting works correctly")
        
        print("\n3. Testing bulk email preparation...")
        recipients = [
            {'email': '<EMAIL>', 'name': 'User One', 'job_title': 'Developer'},
            {'email': '<EMAIL>', 'name': 'User Two', 'job_title': 'Designer'}
        ]
        
        # Mock email sending for testing
        with patch.object(email_service, 'send_email', return_value=True):
            result = email_service.send_bulk_emails(
                recipients=recipients,
                subject='Test Subject',
                template='<p>Hello {name}, thank you for applying to {job_title}</p>',
                async_send=False
            )
        
        assert result['success'] == 2
        assert result['failed'] == 0
        print(f"   ✓ Bulk email preparation successful: {result['success']} emails")
        
        print("\n4. Testing error handling...")
        # Test with missing template variable
        with patch.object(email_service, 'send_email', return_value=True):
            result = email_service.send_bulk_emails(
                recipients=[{'email': '<EMAIL>', 'name': 'Test'}],
                subject='Test',
                template='Hello {name}, your {missing_var} is ready',
                async_send=False
            )
        
        assert result['failed'] == 1
        print("   ✓ Error handling works correctly")
        
        print("\n✅ Standalone email service test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Standalone test failed: {e}")
        return False


def main():
    """Run integration tests"""
    print("🧪 BAUCH HR Management System - Email Integration Tests")
    print("=" * 65)
    
    tests_passed = 0
    total_tests = 2
    
    # Run integration test
    if test_email_integration_with_hr_system():
        tests_passed += 1
    
    # Run standalone test
    if test_email_service_standalone():
        tests_passed += 1
    
    print("\n" + "=" * 65)
    print(f"📊 Integration Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✅ All integration tests passed!")
        print("\n🎯 Email Service Status: WORKING")
        print("\nThe email service is ready for use with the following features:")
        print("• ✓ Single email sending")
        print("• ✓ Bulk email sending (sync/async)")
        print("• ✓ HTML and text email support")
        print("• ✓ Template system with variables")
        print("• ✓ Multi-language templates (EN/DE)")
        print("• ✓ Error handling and logging")
        print("• ✓ Integration with HR database")
        
        print("\n📋 Next Steps:")
        print("1. Configure email credentials in environment variables")
        print("2. Test with real email provider (Gmail, Outlook, etc.)")
        print("3. Integrate with your Flask application")
        print("4. Set up monitoring for email delivery")
        
    else:
        print("❌ Some integration tests failed")
        print("Please check the error messages above")
    
    return tests_passed == total_tests


if __name__ == '__main__':
    main()
