# 🚀 BAUCH HR Management System - Complete Deployment Guide

This guide provides step-by-step instructions for deploying the BAUCH HR Management System on Uberspace server.

## 📋 Table of Contents

1. [Prerequisites](#prerequisites)
2. [Phase 1: GitHub Setup](#phase-1-github-setup)
3. [Phase 2: Server Connection](#phase-2-server-connection)
4. [Phase 3: Environment Setup](#phase-3-environment-setup)
5. [Phase 4: Application Configuration](#phase-4-application-configuration)
6. [Phase 5: Production Deployment](#phase-5-production-deployment)
7. [Phase 6: Web Server Configuration](#phase-6-web-server-configuration)
8. [Phase 7: Security & Maintenance](#phase-7-security--maintenance)
9. [Phase 8: Testing & Access](#phase-8-testing--access)
10. [Troubleshooting](#troubleshooting)
11. [Maintenance](#maintenance)

---

## Prerequisites

- Uberspace account and server access
- GitHub account
- SSH client (Terminal, PuTTY, etc.)
- Basic command line knowledge

---

## Phase 1: GitHub Setup

### 1.1 Initialize Git Repository

```bash
# Navigate to your BAUCH_ENG folder
cd C:\Users\<USER>\Desktop\BAUCH_ENG

# Initialize git repository
git init

# Add all files
git add .

# Make initial commit
git commit -m "Initial commit - BAUCH HR Management System"
```

### 1.2 Create GitHub Repository

1. Go to [GitHub.com](https://github.com)
2. Click "New Repository"
3. Name it: `bauch-hr-system`
4. Make it **Private** (recommended for business use)
5. Don't initialize with README (we already have one)
6. Click "Create Repository"

### 1.3 Push to GitHub

```bash
# Add your GitHub repository (replace with your username)
git remote add origin https://github.com/YOURUSERNAME/bauch-hr-system.git

# Push to GitHub
git branch -M main
git push -u origin main
```

---

## Phase 2: Server Connection

### 2.1 Connect to Uberspace

```bash
# SSH into your Uberspace server (replace with your details)
ssh <EMAIL>

# Example:
# ssh <EMAIL>
```

### 2.2 Clone Repository

```bash
# Navigate to home directory
cd ~

# Clone your repository (replace with your GitHub URL)
git clone https://github.com/YOURUSERNAME/bauch-hr-system.git

# Navigate to project
cd bauch-hr-system

# Verify files are present
ls -la
```

---

## Phase 3: Environment Setup

### 3.1 Create Virtual Environment

```bash
# Create Python virtual environment
python3 -m venv venv

# Activate virtual environment
source venv/bin/activate

# Verify activation (should show venv in prompt)
which python

# Upgrade pip
pip install --upgrade pip
```

### 3.2 Install Dependencies

```bash
# Install all required packages
pip install -r requirements.txt

# Verify installation
pip list | grep -E "(Flask|SQLAlchemy|PyMuPDF|openpyxl)"
```

**Expected output should include:**
- Flask==2.3.3
- SQLAlchemy==2.0.21
- PyMuPDF==1.23.8
- openpyxl==3.1.2

---

## Phase 4: Application Configuration

### 4.1 Create Admin User

```bash
# Create the default admin user
python create_admin_user.py
```

**Expected output:**
```
Creating default admin user...
✅ Admin user created successfully!
   Username: admin
   Email: <EMAIL>
   Password: admin123
⚠️  IMPORTANT: Please change the default password after first login!
```

### 4.2 Production Configuration

Create environment configuration:

```bash
# Create .env file for production settings
nano .env
```

Add this content (replace SECRET_KEY with a random string):
```bash
FLASK_ENV=production
SECRET_KEY=your-super-secret-random-key-here-change-this-to-something-unique
DATABASE_URL=sqlite:///hr_database.db
```

**To generate a secure secret key:**
```bash
python -c "import secrets; print(secrets.token_hex(32))"
```

### 4.3 Test Installation

```bash
# Test run (development mode)
python app.py
```

**Expected output:**
```
* Serving Flask app 'app'
* Debug mode: on
* Running on all addresses (0.0.0.0)
* Running on http://127.0.0.1:5000
```

Press `Ctrl+C` to stop the test.

---

## Phase 5: Production Deployment

### 5.1 Install Production Server

```bash
# Gunicorn should already be in requirements.txt, but verify:
pip install gunicorn

# Test gunicorn
gunicorn -w 2 -b 127.0.0.1:8000 app:app
```

### 5.2 Create Systemd Service

Create service file for auto-start:

```bash
# Create systemd user directory if it doesn't exist
mkdir -p ~/.config/systemd/user

# Create service file
nano ~/.config/systemd/user/bauch-hr.service
```

Add this content (replace YOURUSERNAME with your actual username):
```ini
[Unit]
Description=BAUCH HR Management System
After=network.target

[Service]
Type=simple
User=%i
WorkingDirectory=/home/<USER>/bauch-hr-system
Environment=PATH=/home/<USER>/bauch-hr-system/venv/bin
ExecStart=/home/<USER>/bauch-hr-system/venv/bin/gunicorn -w 4 -b 127.0.0.1:8000 app:app
Restart=always
RestartSec=10

[Install]
WantedBy=default.target
```

### 5.3 Enable and Start Service

```bash
# Reload systemd
systemctl --user daemon-reload

# Enable service (auto-start on boot)
systemctl --user enable bauch-hr.service

# Start the service
systemctl --user start bauch-hr.service

# Check status
systemctl --user status bauch-hr.service
```

**Expected status:** `Active: active (running)`

---

## Phase 6: Web Server Configuration

### 6.1 Configure Apache Reverse Proxy

```bash
# Navigate to your web directory
cd ~/html

# Create .htaccess file
nano .htaccess
```

Add this content:
```apache
RewriteEngine On

# Redirect all requests to your Flask app
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ http://localhost:8000/$1 [P,L]

# Force HTTPS (recommended)
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
```

### 6.2 Set Up Custom Domain (Optional)

If you have a custom domain:

```bash
# Add your domain
uberspace web domain add yourdomain.com

# Check domain status
uberspace web domain list
```

---

## Phase 7: Security & Maintenance

### 7.1 Set Proper Permissions

```bash
# Set directory permissions
chmod 755 ~/bauch-hr-system
chmod 600 ~/bauch-hr-system/.env
chmod 755 ~/bauch-hr-system/uploads
chmod 644 ~/bauch-hr-system/*.py
```

### 7.2 Create Backup System

```bash
# Create backup directory
mkdir -p ~/backups

# Create backup script
nano ~/backup-bauch.sh
```

Add this content:
```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="$HOME/backups"
PROJECT_DIR="$HOME/bauch-hr-system"

echo "Starting backup at $(date)"

# Create backup
cd "$PROJECT_DIR"
tar -czf "$BACKUP_DIR/bauch-hr-backup-$DATE.tar.gz" \
    --exclude=venv \
    --exclude=__pycache__ \
    --exclude=.git \
    .

echo "Backup created: bauch-hr-backup-$DATE.tar.gz"

# Keep only last 7 backups
cd "$BACKUP_DIR"
ls -t bauch-hr-backup-*.tar.gz | tail -n +8 | xargs -r rm

echo "Backup completed at $(date)"
```

Make it executable:
```bash
chmod +x ~/backup-bauch.sh

# Test backup
~/backup-bauch.sh
```

### 7.3 Set Up Automated Backups

```bash
# Edit crontab
crontab -e

# Add daily backup at 2 AM
0 2 * * * /home/<USER>/backup-bauch.sh >> /home/<USER>/backup.log 2>&1
```

---

## Phase 8: Testing & Access

### 8.1 Access Your Application

1. **Open your web browser**
2. **Navigate to your URL:**
   - Uberspace subdomain: `https://YOURUSERNAME.YOURSERVER.uberspace.de`
   - Custom domain: `https://yourdomain.com`

### 8.2 Initial Login

1. **Login with default credentials:**
   - Username: `admin`
   - Password: `admin123`

2. **⚠️ IMMEDIATELY change the default password!**

### 8.3 Test Core Features

- [ ] Login/logout functionality
- [ ] Create a new job posting
- [ ] Upload a CV file
- [ ] Test CV matching
- [ ] Export data to Excel
- [ ] Check file uploads work

---

## Troubleshooting

### Common Issues and Solutions

#### Service Not Starting
```bash
# Check service status
systemctl --user status bauch-hr.service

# View detailed logs
journalctl --user -u bauch-hr.service -f

# Restart service
systemctl --user restart bauch-hr.service
```

#### Application Not Accessible
```bash
# Check if port is listening
ss -tlnp | grep 8000

# Test application directly
curl http://localhost:8000

# Check Apache configuration
cat ~/html/.htaccess
```

#### Database Issues
```bash
# Check database file exists
ls -la ~/bauch-hr-system/hr_database.db

# Recreate admin user if needed
cd ~/bauch-hr-system
source venv/bin/activate
python create_admin_user.py
```

#### File Upload Issues
```bash
# Check uploads directory permissions
ls -la ~/bauch-hr-system/uploads/

# Fix permissions if needed
chmod 755 ~/bauch-hr-system/uploads/
```

### Log Files Locations

- **Application logs:** `journalctl --user -u bauch-hr.service`
- **Apache logs:** `/home/<USER>/logs/`
- **Backup logs:** `~/backup.log`

---

## Maintenance

### Regular Tasks

#### Weekly
- [ ] Check application logs for errors
- [ ] Verify backups are working
- [ ] Test core functionality

#### Monthly
- [ ] Update dependencies: `pip install -r requirements.txt --upgrade`
- [ ] Review and clean old backup files
- [ ] Check disk space usage

#### As Needed
- [ ] Update application code: `git pull origin main`
- [ ] Restart service after updates: `systemctl --user restart bauch-hr.service`

### Update Procedure

```bash
# Navigate to project
cd ~/bauch-hr-system

# Backup before update
~/backup-bauch.sh

# Pull latest changes
git pull origin main

# Update dependencies if needed
source venv/bin/activate
pip install -r requirements.txt --upgrade

# Restart service
systemctl --user restart bauch-hr.service

# Verify everything works
systemctl --user status bauch-hr.service
```

---

## Support & Contact

For technical support or questions about this deployment:

1. **Check logs first:** `journalctl --user -u bauch-hr.service -f`
2. **Verify service status:** `systemctl --user status bauch-hr.service`
3. **Test manually:** `cd ~/bauch-hr-system && source venv/bin/activate && python app.py`

---

## Security Notes

⚠️ **Important Security Reminders:**

1. **Change default password immediately**
2. **Use strong, unique SECRET_KEY**
3. **Keep system updated**
4. **Monitor logs regularly**
5. **Use HTTPS only**
6. **Regular backups**

---

**🎉 Congratulations! Your BAUCH HR Management System is now deployed and ready for use!**
