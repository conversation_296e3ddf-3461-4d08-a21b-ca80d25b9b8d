#!/usr/bin/env python3
"""
Test Real CV File Extraction
Tests extraction on actual PDF and DOCX files in the uploads directory
"""

import os
import glob
import tempfile
from typing import Dict, List
from cv_extractor import CVDataExtractor
from matcher import CVMatcher
# import pandas as pd  # Not needed for this test

class RealCVExtractionTest:
    def __init__(self):
        self.extractor = CVDataExtractor()
        self.matcher = CVMatcher()
        self.all_fields = ['name', 'email', 'phone', 'experience', 'skills', 'education']
        
        # Expected patterns for known CV files
        self.expected_patterns = {
            'CV_Maria_Schmidt_80.pdf': {
                'name_contains': ['maria', 'schmidt'],
                'email_contains': '@',
                'skills_contains': ['java', 'spring'],
                'education_contains': ['informatik', 'computer'],
                'experience_contains': ['year', 'jahr'],
                'language': 'german'
            },
            'CV_Max_Mueller_20.pdf': {
                'name_contains': ['max', 'müller'],
                'email_contains': '@',
                'skills_contains': ['windows', 'server'],
                'education_contains': ['fachinformatiker', 'system'],
                'experience_contains': ['year', 'jahr'],
                'language': 'german'
            },
            'Emma_Brooks_CV.pdf': {
                'name_contains': ['emma', 'brooks'],
                'email_contains': '@',
                'skills_contains': ['project', 'management'],
                'education_contains': ['degree', 'university'],
                'experience_contains': ['year', 'experience'],
                'language': 'english'
            },
            'Taha_Mughal_CV_Tailored.pdf': {
                'name_contains': ['taha', 'mughal'],
                'email_contains': '@',
                'skills_contains': ['python', 'data'],
                'education_contains': ['computer', 'science'],
                'experience_contains': ['year', 'experience'],
                'language': 'english'
            }
        }

    def test_real_cv_extraction(self):
        """Test extraction on real CV files"""
        print("🔍 REAL CV FILE EXTRACTION TEST")
        print("=" * 50)
        
        cv_files = glob.glob("uploads/*.pdf") + glob.glob("uploads/*.docx")
        
        if not cv_files:
            print("❌ No CV files found in uploads directory")
            return {}
        
        print(f"📁 Found {len(cv_files)} CV files to test")
        
        results = {}
        
        for cv_file in cv_files:
            filename = os.path.basename(cv_file)
            print(f"\n📄 Testing: {filename}")
            print("-" * 40)
            
            try:
                # Extract all fields
                extracted_data = self.extractor.extract_cv_data(cv_file, self.all_fields)
                
                # Show extracted content preview
                text_content = self.extractor.extract_text_from_file(cv_file)
                content_preview = text_content[:300] + "..." if len(text_content) > 300 else text_content
                
                print(f"   Content preview: {content_preview}")
                print(f"\n   Extracted fields:")
                
                field_results = {}
                successful_fields = 0
                
                for field in self.all_fields:
                    value = extracted_data.get(field, 'Not extracted')
                    is_successful = self.validate_real_extraction(field, value, filename)
                    
                    field_results[field] = {
                        'value': value,
                        'success': is_successful
                    }
                    
                    if is_successful:
                        successful_fields += 1
                    
                    # Display result
                    status = "✅" if is_successful else "❌"
                    print(f"     {status} {field}: '{value}'")
                
                # Validate against expected patterns if available
                if filename in self.expected_patterns:
                    print(f"\n   📋 Validation against expected patterns:")
                    validation_results = self.validate_against_expected(field_results, self.expected_patterns[filename])
                    for field, result in validation_results.items():
                        status = "✅" if result['valid'] else "❌"
                        print(f"     {status} {field}: {result['reason']}")
                
                success_rate = (successful_fields / len(self.all_fields)) * 100
                print(f"\n   📊 Success Rate: {success_rate:.1f}% ({successful_fields}/{len(self.all_fields)})")
                
                results[filename] = {
                    'field_results': field_results,
                    'success_rate': success_rate,
                    'content_preview': content_preview
                }
                
            except Exception as e:
                print(f"   ❌ Error extracting from {filename}: {e}")
                results[filename] = {'error': str(e)}
        
        return results

    def validate_real_extraction(self, field: str, extracted_value: str, filename: str) -> bool:
        """Validate extraction for real CV files"""
        if not extracted_value or extracted_value in ['Not extracted', 'not found', 'not specified']:
            return False
        
        extracted_lower = extracted_value.lower()
        
        # Basic validation
        if field == 'email':
            return '@' in extracted_value and '.' in extracted_value
        elif field == 'phone':
            return any(char.isdigit() for char in extracted_value) and len(''.join(filter(str.isdigit, extracted_value))) >= 6
        elif field == 'name':
            return len(extracted_value.split()) >= 2 and not any(word in extracted_lower for word in ['email', 'phone', 'address'])
        elif field == 'skills':
            skill_keywords = ['java', 'python', 'javascript', 'sql', 'git', 'spring', 'react', 'docker', 'windows', 'linux', 'project', 'management']
            return any(skill in extracted_lower for skill in skill_keywords)
        elif field == 'education':
            education_keywords = ['degree', 'university', 'college', 'bachelor', 'master', 'phd', 'informatik', 'computer', 'fachinformatiker', 'studium']
            return any(keyword in extracted_lower for keyword in education_keywords)
        elif field == 'experience':
            experience_keywords = ['year', 'jahre', 'experience', 'erfahrung', 'senior', 'junior', 'developer', 'engineer']
            return any(keyword in extracted_lower for keyword in experience_keywords)
        
        return True

    def validate_against_expected(self, extracted: Dict, expected: Dict) -> Dict:
        """Validate extracted data against expected patterns"""
        results = {}
        
        for field_pattern, expected_value in expected.items():
            if field_pattern.endswith('_contains'):
                base_field = field_pattern.replace('_contains', '')
                extracted_value = extracted.get(base_field, {}).get('value', '').lower()
                
                if isinstance(expected_value, list):
                    # Check if any of the expected values are present
                    found = any(val.lower() in extracted_value for val in expected_value)
                    results[base_field] = {
                        'valid': found,
                        'reason': f"Contains {expected_value}" if found else f"Missing {expected_value}"
                    }
                else:
                    # Single value check
                    found = expected_value.lower() in extracted_value
                    results[base_field] = {
                        'valid': found,
                        'reason': f"Contains '{expected_value}'" if found else f"Missing '{expected_value}'"
                    }
        
        return results

    def test_cv_matching_with_real_files(self):
        """Test CV matching using real CV files"""
        print(f"\n🎯 REAL CV MATCHING TEST")
        print("=" * 40)
        
        cv_files = glob.glob("uploads/*.pdf") + glob.glob("uploads/*.docx")
        
        if not cv_files:
            print("❌ No CV files found for matching test")
            return
        
        # Test job descriptions
        test_jobs = {
            'Java Developer (German)': """
            Java Entwickler (m/w/d)
            
            Anforderungen:
            • Mindestens 3 Jahre Berufserfahrung in der Java-Entwicklung
            • Erfahrung mit Spring Boot Framework
            • Kenntnisse in PostgreSQL-Datenbanken
            • Git und Jenkins für CI/CD
            • Agile Scrum-Methoden
            • Abgeschlossenes Studium der Informatik
            """,
            
            'Java Developer (English)': """
            Java Developer Position
            
            Requirements:
            • 3+ years Java development experience
            • Spring Boot framework expertise
            • PostgreSQL database experience
            • Git, Jenkins, CI/CD experience
            • Scrum methodology
            • Computer Science degree
            """,
            
            'System Administrator': """
            System Administrator Position
            
            Requirements:
            • Windows Server administration
            • Active Directory management
            • Network configuration
            • IT support experience
            • ITIL knowledge
            • Technical certification
            """,
            
            'Project Manager': """
            Project Manager Position
            
            Requirements:
            • Project management experience
            • Team leadership skills
            • Communication skills
            • Planning and organization
            • Risk management
            • PMP certification preferred
            """
        }
        
        for job_title, job_desc in test_jobs.items():
            print(f"\n💼 Testing Job: {job_title}")
            print("-" * 30)
            
            results = []
            
            for cv_file in cv_files:
                filename = os.path.basename(cv_file)
                
                try:
                    # Extract CV content
                    cv_content = self.extractor.extract_text_from_file(cv_file)
                    if not cv_content:
                        print(f"   ⚠️  Could not extract content from {filename}")
                        continue
                    
                    # Calculate match scores
                    tf_idf_score = self.matcher.calculate_tf_idf_similarity(job_desc, cv_content)
                    keyword_score = self.matcher.calculate_keyword_match(job_desc, cv_content)
                    skill_score = self.matcher.calculate_skill_match(job_desc, cv_content)
                    overall_score = (skill_score * 0.5) + (keyword_score * 0.3) + (tf_idf_score * 0.2)
                    
                    results.append({
                        'filename': filename,
                        'overall_score': overall_score * 100,
                        'skill_score': skill_score * 100,
                        'keyword_score': keyword_score * 100,
                        'tf_idf_score': tf_idf_score * 100
                    })
                    
                except Exception as e:
                    print(f"   ❌ Error processing {filename}: {e}")
            
            # Sort by overall score
            results.sort(key=lambda x: x['overall_score'], reverse=True)
            
            # Display results
            print(f"   📊 Results (sorted by match score):")
            for i, result in enumerate(results, 1):
                print(f"   {i}. {result['filename']}: {result['overall_score']:.1f}%")
                print(f"      Skills: {result['skill_score']:.1f}% | Keywords: {result['keyword_score']:.1f}% | Content: {result['tf_idf_score']:.1f}%")
            
            # Analyze results
            self.analyze_real_matching_results(job_title, results)

    def analyze_real_matching_results(self, job_title: str, results: List[Dict]):
        """Analyze real CV matching results"""
        print(f"\n   🔍 Analysis:")
        
        if 'java' in job_title.lower():
            # Maria should score higher than Max for Java positions
            maria_result = next((r for r in results if 'maria' in r['filename'].lower()), None)
            max_result = next((r for r in results if 'max' in r['filename'].lower()), None)
            
            if maria_result and max_result:
                if maria_result['overall_score'] > max_result['overall_score']:
                    print(f"      ✅ Maria (Java dev) scores higher than Max (sysadmin): {maria_result['overall_score']:.1f}% vs {max_result['overall_score']:.1f}%")
                else:
                    print(f"      ⚠️  Max scores higher than Maria: {max_result['overall_score']:.1f}% vs {maria_result['overall_score']:.1f}%")
        
        elif 'system' in job_title.lower() or 'administrator' in job_title.lower():
            # Max should score higher than Maria for admin positions
            maria_result = next((r for r in results if 'maria' in r['filename'].lower()), None)
            max_result = next((r for r in results if 'max' in r['filename'].lower()), None)
            
            if maria_result and max_result:
                if max_result['overall_score'] > maria_result['overall_score']:
                    print(f"      ✅ Max (sysadmin) scores higher than Maria (Java dev): {max_result['overall_score']:.1f}% vs {maria_result['overall_score']:.1f}%")
                else:
                    print(f"      ⚠️  Maria scores higher than Max: {maria_result['overall_score']:.1f}% vs {max_result['overall_score']:.1f}%")

    def test_extraction_consistency(self):
        """Test extraction consistency across multiple runs"""
        print(f"\n🔄 EXTRACTION CONSISTENCY TEST")
        print("-" * 35)
        
        cv_files = glob.glob("uploads/*.pdf")[:2]  # Test first 2 files
        
        for cv_file in cv_files:
            filename = os.path.basename(cv_file)
            print(f"\n📄 Testing consistency for: {filename}")
            
            # Run extraction 3 times
            results = []
            for i in range(3):
                try:
                    extracted_data = self.extractor.extract_cv_data(cv_file, self.all_fields)
                    results.append(extracted_data)
                except Exception as e:
                    print(f"   ❌ Error in run {i+1}: {e}")
            
            # Check consistency
            if len(results) >= 2:
                consistent = True
                for field in self.all_fields:
                    values = [result.get(field, '') for result in results]
                    unique_values = set(values)
                    if len(unique_values) > 1:  # Different values found
                        consistent = False
                        print(f"   ❌ {field}: Inconsistent results {list(unique_values)}")
                    else:
                        print(f"   ✅ {field}: Consistent")
                
                if consistent:
                    print(f"   ✅ All fields consistent across runs")
            else:
                print(f"   ⚠️  Not enough successful runs to test consistency")

def main():
    """Run real CV extraction tests"""
    print("🚀 REAL CV FILE EXTRACTION & MATCHING TEST SUITE")
    print("=" * 70)
    
    test_suite = RealCVExtractionTest()
    
    # Test extraction on real files
    extraction_results = test_suite.test_real_cv_extraction()
    
    # Test matching with real files
    test_suite.test_cv_matching_with_real_files()
    
    # Test consistency
    test_suite.test_extraction_consistency()
    
    # Summary
    print(f"\n📊 REAL CV TEST SUMMARY")
    print("=" * 35)
    
    if extraction_results:
        successful_files = sum(1 for result in extraction_results.values() if 'error' not in result)
        total_files = len(extraction_results)
        
        print(f"📁 Files processed: {successful_files}/{total_files}")
        
        if successful_files > 0:
            avg_success_rate = sum(result['success_rate'] for result in extraction_results.values() if 'success_rate' in result) / successful_files
            print(f"📊 Average extraction success rate: {avg_success_rate:.1f}%")
        
        print(f"🎯 CV matching tested with multiple job types")
        print(f"✅ Real CV testing completed!")
    else:
        print(f"❌ No CV files found for testing")

if __name__ == "__main__":
    main()
