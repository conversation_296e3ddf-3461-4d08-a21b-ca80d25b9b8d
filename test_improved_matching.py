#!/usr/bin/env python3
"""
Test the improved CNC matching algorithm
"""

from hr_database_working import HRDatabase
from matcher import <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_cnc_matching():
    """Test the improved CNC matching"""
    print("=== TESTING IMPROVED CNC MATCHING ===")
    
    try:
        db = HRDatabase()
        matcher = CVMatcher()
        
        # Get CNC job and CVs
        cnc_job = db.get_job_by_title('CNC Fräser')
        if not cnc_job:
            print("CNC job not found!")
            return
            
        cvs = db.get_cvs_for_job('CNC Fräser')
        print(f"Found {len(cvs)} CVs for CNC job")
        print(f"Job type detected: {matcher._detect_job_type(cnc_job.description.lower())}")
        print()
        
        # Calculate scores for each CV
        results = []
        for cv in cvs:
            try:
                # Calculate individual scores
                tf_idf_score = matcher.calculate_tf_idf_similarity(cnc_job.description, cv.content)
                keyword_score = matcher.calculate_keyword_match(cnc_job.description, cv.content)
                skill_score = matcher.calculate_skill_match(cnc_job.description, cv.content)
                
                # Calculate overall score with manufacturing weights
                overall_score = (skill_score * 0.6) + (keyword_score * 0.25) + (tf_idf_score * 0.15)
                
                results.append({
                    'name': cv.candidate_name or 'Unknown',
                    'overall': overall_score * 100,
                    'skill': skill_score * 100,
                    'keyword': keyword_score * 100,
                    'tfidf': tf_idf_score * 100
                })
                
            except Exception as e:
                print(f"Error processing CV {cv.candidate_name}: {e}")
        
        # Sort by overall score
        results.sort(key=lambda x: x['overall'], reverse=True)
        
        print("=== IMPROVED MATCHING RESULTS ===")
        for i, result in enumerate(results, 1):
            print(f"{i}. {result['name']}")
            print(f"   Overall: {result['overall']:.1f}% | Skill: {result['skill']:.1f}% | Keyword: {result['keyword']:.1f}% | TF-IDF: {result['tfidf']:.1f}%")
            print()
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_cnc_matching()
