# BAUCH HR Management System - Environment Configuration
# Copy this file to .env and fill in your actual values
# NEVER commit .env file to version control

# =============================================================================
# FLASK APPLICATION SETTINGS
# =============================================================================
FLASK_ENV=production
SECRET_KEY=your-super-secret-key-change-this-in-production-min-32-chars
DEBUG=False

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL=sqlite:///hr_database.db
# For PostgreSQL: postgresql://username:password@localhost:5432/hr_database

# =============================================================================
# LISTMONK EMAIL SERVICE CONFIGURATION
# =============================================================================
# Listmonk Server Configuration
LISTMONK_URL=http://localhost:9000
LISTMONK_USERNAME=admin
LISTMONK_PASSWORD=your-secure-listmonk-password

# Optional: API Token (more secure than username/password)
# LISTMONK_API_TOKEN=your-api-token-here

# =============================================================================
# SMTP CONFIGURATION FOR LISTMONK
# =============================================================================
# These are the actual email credentials that Listmonk will use to send emails
# Configure these in Listmonk's config.toml file or through the web interface

# Gmail Configuration Example:
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your-app-password
# SMTP_TLS=true

# Outlook Configuration Example:
# SMTP_HOST=smtp-mail.outlook.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your-password
# SMTP_TLS=true

# Custom SMTP Configuration Example:
# SMTP_HOST=mail.yourcompany.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your-smtp-password
# SMTP_TLS=true

# =============================================================================
# EMAIL SENDER CONFIGURATION
# =============================================================================
# This is the "From" address that will appear in emails
DEFAULT_SENDER_EMAIL=<EMAIL>
DEFAULT_SENDER_NAME=BAUCH HR Team

# =============================================================================
# LISTMONK DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL settings for Listmonk (recommended for production)
LISTMONK_DB_HOST=localhost
LISTMONK_DB_PORT=5432
LISTMONK_DB_USER=listmonk_user
LISTMONK_DB_PASSWORD=secure-database-password
LISTMONK_DB_NAME=listmonk

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
# Session configuration
SESSION_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax
PERMANENT_SESSION_LIFETIME=3600

# File upload settings
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=uploads

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=INFO
LOG_FILE=logs/hr_system.log

# =============================================================================
# RATE LIMITING (Optional)
# =============================================================================
RATELIMIT_STORAGE_URL=memory://
RATELIMIT_DEFAULT=100 per hour

# =============================================================================
# BACKUP CONFIGURATION (Optional)
# =============================================================================
BACKUP_ENABLED=True
BACKUP_SCHEDULE=daily
BACKUP_RETENTION_DAYS=30
