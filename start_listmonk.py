"""
Listmonk Email Server Startup Script for BAUCH HR System
"""

import subprocess
import time
import requests
import os
import sys
from pathlib import Path


def check_listmonk_running(url="http://localhost:9000"):
    """Check if Listmonk is already running"""
    try:
        response = requests.get(f"{url}/api/health", timeout=5)
        return response.status_code == 200
    except:
        return False


def start_listmonk():
    """Start Listmonk email server"""
    email_system_dir = Path(__file__).parent / "email_system"
    
    if not email_system_dir.exists():
        print("❌ Email system directory not found!")
        return False
    
    listmonk_exe = email_system_dir / "listmonk.exe"
    config_file = email_system_dir / "config.toml"
    
    if not listmonk_exe.exists():
        print("❌ Listmonk executable not found!")
        return False
    
    # Check if already running
    if check_listmonk_running():
        print("✅ Listmonk is already running at http://localhost:9000")
        return True
    
    print("🚀 Starting Listmonk email server...")
    
    try:
        # Change to email system directory
        os.chdir(email_system_dir)
        
        # Start Listmonk in background
        process = subprocess.Popen(
            [str(listmonk_exe)],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
        )
        
        # Wait for server to start
        print("⏳ Waiting for Listmonk to start...")
        for i in range(30):  # Wait up to 30 seconds
            if check_listmonk_running():
                print("✅ Listmonk started successfully!")
                print("📧 Email server available at: http://localhost:9000")
                print("👤 Default login: admin / listmonk")
                return True
            time.sleep(1)
            print(f"   Waiting... ({i+1}/30)")
        
        print("❌ Listmonk failed to start within 30 seconds")
        return False
        
    except Exception as e:
        print(f"❌ Error starting Listmonk: {e}")
        return False


def setup_listmonk():
    """Setup Listmonk database and configuration"""
    email_system_dir = Path(__file__).parent / "email_system"
    listmonk_exe = email_system_dir / "listmonk.exe"
    
    if not listmonk_exe.exists():
        print("❌ Listmonk executable not found!")
        return False
    
    try:
        os.chdir(email_system_dir)
        
        print("🔧 Setting up Listmonk database...")
        result = subprocess.run([str(listmonk_exe), "--install"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Listmonk database setup completed!")
            return True
        else:
            print(f"❌ Database setup failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error setting up Listmonk: {e}")
        return False


def main():
    """Main function"""
    print("📧 BAUCH HR - Listmonk Email Server Manager")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] == "--setup":
        setup_listmonk()
        return
    
    # Try to start Listmonk
    if start_listmonk():
        print("\n🎉 Email system ready!")
        print("You can now use the email features in the HR application.")
        print("\nTo access Listmonk admin panel:")
        print("URL: http://localhost:9000")
        print("Username: admin")
        print("Password: listmonk")
    else:
        print("\n❌ Failed to start email system!")
        print("Try running with --setup flag first:")
        print("python start_listmonk.py --setup")


if __name__ == "__main__":
    main()
