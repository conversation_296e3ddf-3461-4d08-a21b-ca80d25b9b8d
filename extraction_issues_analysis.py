#!/usr/bin/env python3
"""
CV Extraction Issues Analysis and Testing
Detailed analysis of extraction problems and proposed fixes
"""

import re
from cv_extractor import CVDataExtractor

class ExtractionIssuesAnalyzer:
    def __init__(self):
        self.extractor = CVDataExtractor()
        
    def analyze_name_extraction_issues(self):
        """Analyze name extraction problems"""
        print("🔍 NAME EXTRACTION ISSUES ANALYSIS")
        print("=" * 45)
        
        # Test cases that are failing
        test_cases = [
            {
                'name': 'German CV with umlauts',
                'content': 'Dr. <PERSON><PERSON>\nE-Mail: <EMAIL>',
                'expected': '<PERSON><PERSON><PERSON><PERSON>',
                'issues': ['Hyphenated names', 'German umlauts', 'Titles (Dr.)']
            },
            {
                'name': 'Name at beginning',
                'content': '<PERSON>\nSoftwareentwicklerin\nKontakt:',
                'expected': '<PERSON>',
                'issues': ['Name not in standard format', 'German umlauts']
            },
            {
                'name': 'Complex name patterns',
                'content': "<PERSON>\<EMAIL>",
                'expected': "<PERSON>",
                'issues': ['Apostrophes', 'Hyphenated surnames']
            }
        ]
        
        print("Current name extraction patterns:")
        patterns = [
            r'\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)+)\b',
            r'Name:\s*([A-Z][a-z]+(?:\s+[A-Z][a-z]+)+)',
            r'Full Name:\s*([A-Z][a-z]+(?:\s+[A-Z][a-z]+)+)'
        ]
        for i, pattern in enumerate(patterns, 1):
            print(f"   {i}. {pattern}")
        
        print(f"\nTesting current patterns:")
        for test_case in test_cases:
            extracted = self.extractor.extract_name(test_case['content'])
            success = test_case['expected'].lower() in extracted.lower() if extracted else False
            status = "✅" if success else "❌"
            print(f"   {status} {test_case['name']}: '{extracted}' (expected: '{test_case['expected']}')")
            if not success:
                print(f"      Issues: {', '.join(test_case['issues'])}")
        
        # Proposed improved patterns
        print(f"\nProposed improved patterns:")
        improved_patterns = [
            r'\b((?:Dr\.?\s+)?[A-ZÄÖÜ][a-zäöüß]+(?:[-\'\s]+[A-ZÄÖÜ][a-zäöüß]+)+)\b',  # German names with titles
            r'(?:Name|Vor-?\s*und\s*Nachname):\s*([A-ZÄÖÜ][a-zäöüß\-\'\s]+)',  # German name labels
            r'^([A-ZÄÖÜ][a-zäöüß]+(?:[-\'\s]+[A-ZÄÖÜ][a-zäöüß]+)+)',  # Names at start of line
            r'Lebenslauf:?\s*([A-ZÄÖÜ][a-zäöüß\-\'\s]+)',  # After "Lebenslauf"
        ]
        for i, pattern in enumerate(improved_patterns, 1):
            print(f"   {i}. {pattern}")

    def analyze_phone_extraction_issues(self):
        """Analyze phone extraction problems"""
        print(f"\n📞 PHONE EXTRACTION ISSUES ANALYSIS")
        print("=" * 45)
        
        test_cases = [
            {
                'content': 'Telefon: +49 30 12345678\nMobil: 0171 9876543',
                'expected': '+49 30 12345678',
                'issues': ['German phone formats', 'Multiple phone numbers']
            },
            {
                'content': 'Phone: +49 (0)89 123-456789',
                'expected': '+49 (0)89 123-456789',
                'issues': ['Parentheses in German format']
            },
            {
                'content': 'Tel.: 030 12345678',
                'expected': '030 12345678',
                'issues': ['German area codes without country code']
            }
        ]
        
        print("Current phone patterns:")
        patterns = [
            r'\+?\d{1,4}[\s\-\(\)]?\d{3,4}[\s\-\(\)]?\d{3,4}[\s\-]?\d{3,4}',
            r'\(\d{3}\)\s?\d{3}[\s\-]?\d{4}',
            r'\d{3}[\s\-]\d{3}[\s\-]\d{4}'
        ]
        for i, pattern in enumerate(patterns, 1):
            print(f"   {i}. {pattern}")
        
        print(f"\nTesting current patterns:")
        for test_case in test_cases:
            extracted = self.extractor.extract_phone(test_case['content'])
            success = test_case['expected'] in extracted if extracted != "Phone not found" else False
            status = "✅" if success else "❌"
            print(f"   {status} '{extracted}' (expected: '{test_case['expected']}')")
            if not success:
                print(f"      Issues: {', '.join(test_case['issues'])}")
        
        print(f"\nProposed improved patterns:")
        improved_patterns = [
            r'(?:Telefon|Tel\.?|Phone|Mobil):\s*(\+?\d{1,4}[\s\-\(\)]?\d{2,4}[\s\-\(\)]?\d{3,4}[\s\-]?\d{3,4})',
            r'\+49\s*\(0\)\s*\d{2,3}[\s\-]?\d{3,4}[\s\-]?\d{3,4}',  # German format
            r'0\d{2,4}[\s\-]?\d{3,4}[\s\-]?\d{3,4}',  # German local format
        ]
        for i, pattern in enumerate(improved_patterns, 1):
            print(f"   {i}. {pattern}")

    def analyze_experience_extraction_issues(self):
        """Analyze experience extraction problems"""
        print(f"\n💼 EXPERIENCE EXTRACTION ISSUES ANALYSIS")
        print("=" * 45)
        
        test_cases = [
            {
                'content': '5 Jahre Berufserfahrung in der Softwareentwicklung',
                'expected': '5 Jahre',
                'issues': ['German experience format']
            },
            {
                'content': '2018-heute: Senior Java Entwickler',
                'expected': '6+ Jahre',
                'issues': ['Date range calculation', 'German "heute"']
            },
            {
                'content': 'Berufserfahrung: 3 Jahre',
                'expected': '3 Jahre',
                'issues': ['German label format']
            },
            {
                'content': '4 years software development / 4 Jahre Softwareentwicklung',
                'expected': '4 years',
                'issues': ['Mixed language content']
            }
        ]
        
        print("Current experience patterns:")
        patterns = [
            r'(\d+)\+?\s*years?\s*(?:of\s*)?experience',
            r'(\d+)-(\d+)\s*years?\s*(?:of\s*)?experience',
            r'experience\s*:?\s*(\d+)\+?\s*years?',
            r'(\d+)\+?\s*years?\s*in\s*\w+'
        ]
        for i, pattern in enumerate(patterns, 1):
            print(f"   {i}. {pattern}")
        
        print(f"\nTesting current patterns:")
        for test_case in test_cases:
            extracted = self.extractor.extract_experience(test_case['content'])
            success = any(char.isdigit() for char in extracted) if extracted != "Experience not specified" else False
            status = "✅" if success else "❌"
            print(f"   {status} '{extracted}' (expected: '{test_case['expected']}')")
            if not success:
                print(f"      Issues: {', '.join(test_case['issues'])}")
        
        print(f"\nProposed improved patterns:")
        improved_patterns = [
            r'(\d+)\+?\s*(?:Jahre?|years?)\s*(?:Berufserfahrung|experience)',
            r'(?:Berufserfahrung|Experience):\s*(\d+)\+?\s*(?:Jahre?|years?)',
            r'(\d{4})-(?:heute|present|now)',  # Date ranges
            r'(?:seit|since)\s*(\d{4})',  # Since year
        ]
        for i, pattern in enumerate(improved_patterns, 1):
            print(f"   {i}. {pattern}")

    def analyze_education_extraction_issues(self):
        """Analyze education extraction problems"""
        print(f"\n🎓 EDUCATION EXTRACTION ISSUES ANALYSIS")
        print("=" * 45)
        
        test_cases = [
            {
                'content': 'Master of Science Informatik | TU Berlin | 2018',
                'expected': 'Master Informatik',
                'issues': ['German education terms']
            },
            {
                'content': 'Promotion in Informatik, TU München, 2010',
                'expected': 'Promotion Informatik',
                'issues': ['German PhD equivalent']
            },
            {
                'content': 'Fachinformatiker für Anwendungsentwicklung',
                'expected': 'Fachinformatiker',
                'issues': ['German vocational training']
            }
        ]
        
        print("Current education keywords:")
        keywords = self.extractor.education_keywords
        print(f"   {keywords}")
        
        print(f"\nTesting current extraction:")
        for test_case in test_cases:
            extracted = self.extractor.extract_education(test_case['content'])
            success = extracted != "Education not specified"
            status = "✅" if success else "❌"
            print(f"   {status} '{extracted}' (expected: '{test_case['expected']}')")
            if not success:
                print(f"      Issues: {', '.join(test_case['issues'])}")
        
        print(f"\nProposed additional German keywords:")
        german_keywords = [
            'informatik', 'promotion', 'diplom', 'fachinformatiker', 
            'studium', 'ausbildung', 'abschluss', 'universität', 'hochschule'
        ]
        print(f"   {german_keywords}")

    def test_improved_extraction_methods(self):
        """Test improved extraction methods"""
        print(f"\n🔧 TESTING IMPROVED EXTRACTION METHODS")
        print("=" * 45)
        
        # Test improved name extraction
        def improved_name_extraction(text, filename=""):
            """Improved name extraction with German support"""
            patterns = [
                r'\b((?:Dr\.?\s+)?[A-ZÄÖÜ][a-zäöüß]+(?:[-\'\s]+[A-ZÄÖÜ][a-zäöüß]+)+)\b',
                r'(?:Name|Vor-?\s*und\s*Nachname):\s*([A-ZÄÖÜ][a-zäöüß\-\'\s]+)',
                r'^([A-ZÄÖÜ][a-zäöüß]+(?:[-\'\s]+[A-ZÄÖÜ][a-zäöüß]+)+)',
                r'Lebenslauf:?\s*([A-ZÄÖÜ][a-zäöüß\-\'\s]+)',
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, text, re.MULTILINE)
                if matches:
                    name = matches[0].strip()
                    # Filter out common words
                    exclude_words = ['Sehr Geehrte', 'Sehr Geehrter', 'Mit Freundlichen']
                    if not any(exclude in name for exclude in exclude_words) and len(name.split()) >= 2:
                        return name
            
            # Fallback to filename
            if filename:
                name_from_file = re.sub(r'[_\-\.]', ' ', filename.split('.')[0])
                name_from_file = re.sub(r'\b(cv|resume)\b', '', name_from_file, flags=re.IGNORECASE)
                if name_from_file.strip():
                    return name_from_file.strip().title()
            
            return "Name not found"
        
        # Test cases
        test_cases = [
            'Dr. Klaus-Dieter Müller-Schmidt\nE-Mail: <EMAIL>',
            'Anna Müller\nSoftwareentwicklerin',
            "Sarah O'Connor-Williams\<EMAIL>"
        ]
        
        print("Testing improved name extraction:")
        for test_case in test_cases:
            original = self.extractor.extract_name(test_case)
            improved = improved_name_extraction(test_case)
            print(f"   Original: '{original}'")
            print(f"   Improved: '{improved}'")
            print()

def main():
    """Run extraction issues analysis"""
    analyzer = ExtractionIssuesAnalyzer()
    
    analyzer.analyze_name_extraction_issues()
    analyzer.analyze_phone_extraction_issues()
    analyzer.analyze_experience_extraction_issues()
    analyzer.analyze_education_extraction_issues()
    analyzer.test_improved_extraction_methods()
    
    print(f"\n📋 SUMMARY OF EXTRACTION ISSUES")
    print("=" * 35)
    print("🔴 Critical Issues Found:")
    print("   1. Name extraction fails with German umlauts and complex names")
    print("   2. Phone extraction misses German formats and multiple numbers")
    print("   3. Experience extraction doesn't handle German patterns")
    print("   4. Education extraction lacks German education keywords")
    print("   5. Email extraction works well ✅")
    print("   6. Skills extraction works well ✅")
    
    print(f"\n💡 Recommended Improvements:")
    print("   1. Add German character support to name patterns")
    print("   2. Improve phone regex for German formats")
    print("   3. Add German experience patterns (Jahre, Berufserfahrung)")
    print("   4. Expand education keywords with German terms")
    print("   5. Better handling of mixed-language content")
    print("   6. Improve pattern ordering and fallback logic")

if __name__ == "__main__":
    main()
