#!/usr/bin/env python3
"""
Improved CV Data Extractor
Enhanced version with better German and English support
"""

import re
import os
from typing import Dict, List
import fitz  # PyMuPDF
from docx import Document
from datetime import datetime

class ImprovedCVExtractor:
    def __init__(self):
        # Enhanced skill keywords with German terms
        self.skill_keywords = [
            # Programming languages
            'Python', 'Java', 'JavaScript', 'C++', 'C#', 'PHP', 'Ruby', 'Go', 'Rust',
            'TypeScript', 'Kotlin', 'Swift', 'Scala', 'R', 'MATLAB', 'SQL',
            
            # Web technologies
            'HTML', 'CSS', 'React', 'Angular', 'Vue.js', 'Node.js', 'Express',
            'Django', 'Flask', 'Spring', 'Laravel', 'Bootstrap', 'jQuery',
            
            # Databases
            'MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'SQLite', 'Oracle',
            
            # Tools and platforms
            'Git', 'Docker', 'Kubernetes', 'AWS', 'Azure', 'Linux', 'Windows',
            'Jenkins', 'Jira', 'Confluence', 'Slack', 'Teams',
            
            # Methodologies
            'Agile', 'Scrum', 'Kanban', 'DevOps', 'CI/CD', 'TDD', 'BDD',
            
            # Soft skills
            'Leadership', 'Communication', 'Teamwork', 'Problem Solving',
            'Project Management', 'Time Management', 'Critical Thinking'
        ]
        
        # Enhanced education keywords with German terms
        self.education_keywords = [
            # English terms
            'university', 'college', 'bachelor', 'master', 'phd', 'doctorate',
            'degree', 'diploma', 'certificate', 'education', 'school',
            'graduated', 'gpa', 'honors', 'magna cum laude', 'summa cum laude',
            
            # German terms
            'informatik', 'promotion', 'diplom', 'fachinformatiker', 
            'studium', 'ausbildung', 'abschluss', 'universität', 'hochschule',
            'technische universität', 'fachhochschule', 'berufsakademie'
        ]

    def extract_text_from_pdf(self, file_path: str) -> str:
        """Extract text from PDF file"""
        try:
            doc = fitz.open(file_path)
            text = ""
            for page in doc:
                text += page.get_text()
            doc.close()
            return text
        except Exception as e:
            print(f"Error extracting text from PDF: {e}")
            return ""

    def extract_text_from_docx(self, file_path: str) -> str:
        """Extract text from DOCX file"""
        try:
            doc = Document(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text
        except Exception as e:
            print(f"Error extracting text from DOCX: {e}")
            return ""

    def extract_text_from_file(self, file_path: str) -> str:
        """Extract text from file based on extension"""
        if file_path.lower().endswith('.pdf'):
            return self.extract_text_from_pdf(file_path)
        elif file_path.lower().endswith('.docx'):
            return self.extract_text_from_docx(file_path)
        elif file_path.lower().endswith('.txt'):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
            except Exception as e:
                print(f"Error reading text file: {e}")
                return ""
        else:
            return ""

    def extract_name(self, text: str, filename: str = "") -> str:
        """Enhanced name extraction with German support"""
        # Improved patterns for German and English names
        name_patterns = [
            # German patterns with umlauts and titles
            r'\b((?:Dr\.?\s+|Prof\.?\s+)?[A-ZÄÖÜ][a-zäöüß]+(?:[-\'\s]+[A-ZÄÖÜ][a-zäöüß]+)+)\b',
            
            # Names after German labels
            r'(?:Name|Vor-?\s*und\s*Nachname|Bewerbung\s+von|Lebenslauf\s+von?):\s*([A-ZÄÖÜ][a-zäöüß\-\'\s]+)',
            
            # Names at beginning of lines
            r'^([A-ZÄÖÜ][a-zäöüß]+(?:[-\'\s]+[A-ZÄÖÜ][a-zäöüß]+)+)',
            
            # After "Lebenslauf" or "CV"
            r'(?:Lebenslauf|CV|Resume):?\s*([A-ZÄÖÜ][a-zäöüß\-\'\s]+)',
            
            # Standard English patterns
            r'Name:\s*([A-Z][a-z]+(?:\s+[A-Z][a-z\']+)+)',
            r'Full Name:\s*([A-Z][a-z]+(?:\s+[A-Z][a-z\']+)+)',
            
            # General capitalized names
            r'\b([A-Z][a-z]+(?:\s+[A-Z][a-z\']+)+)\b'
        ]
        
        for pattern in name_patterns:
            matches = re.findall(pattern, text, re.MULTILINE | re.IGNORECASE)
            if matches:
                for match in matches:
                    name = match.strip()
                    # Filter out common words and false positives
                    exclude_words = [
                        'Dear Sir', 'Dear Madam', 'Yours Sincerely', 'Best Regards',
                        'Sehr Geehrte', 'Sehr Geehrter', 'Mit Freundlichen', 'Freundlichen Grüßen',
                        'Work History', 'Professional Experience', 'Berufserfahrung',
                        'Contact Information', 'Kontakt', 'Education', 'Ausbildung'
                    ]
                    
                    # Check if it's a valid name (at least 2 words, not in exclude list)
                    if (len(name.split()) >= 2 and 
                        not any(exclude in name for exclude in exclude_words) and
                        len(name) < 50):  # Reasonable name length
                        return name
        
        # Fallback: try to extract from filename
        if filename:
            name_from_file = re.sub(r'[_\-\.]', ' ', os.path.splitext(filename)[0])
            name_from_file = re.sub(r'\b(cv|resume|lebenslauf)\b', '', name_from_file, flags=re.IGNORECASE)
            if name_from_file.strip() and len(name_from_file.split()) >= 2:
                return name_from_file.strip().title()
        
        return "Name not found"

    def extract_email(self, text: str) -> str:
        """Extract email address from CV text"""
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        matches = re.findall(email_pattern, text)
        return matches[0] if matches else "Email not found"

    def extract_phone(self, text: str) -> str:
        """Enhanced phone extraction with German support"""
        phone_patterns = [
            # German phone patterns with labels
            r'(?:Telefon|Tel\.?|Phone|Mobil|Mobile):\s*(\+?\d{1,4}[\s\-\(\)]?\d{2,4}[\s\-\(\)]?\d{3,4}[\s\-]?\d{3,4})',
            
            # German format with country code
            r'(\+49\s*\(0\)\s*\d{2,3}[\s\-]?\d{3,4}[\s\-]?\d{3,4})',
            
            # German format without country code
            r'(0\d{2,4}[\s\-]?\d{3,4}[\s\-]?\d{3,4})',
            
            # International format
            r'(\+\d{1,4}[\s\-\(\)]?\d{2,4}[\s\-\(\)]?\d{3,4}[\s\-]?\d{3,4})',
            
            # US format
            r'(\(\d{3}\)\s?\d{3}[\s\-]?\d{4})',
            
            # General format
            r'(\d{3}[\s\-]\d{3}[\s\-]\d{4})'
        ]
        
        for pattern in phone_patterns:
            matches = re.findall(pattern, text)
            if matches:
                return matches[0]
        
        return "Phone not found"

    def extract_experience(self, text: str) -> str:
        """Enhanced experience extraction with German support"""
        text_lower = text.lower()
        
        # Enhanced patterns for German and English
        experience_patterns = [
            # German patterns
            r'(\d+)\+?\s*jahre?\s*berufserfahrung',
            r'berufserfahrung:\s*(\d+)\+?\s*jahre?',
            r'(\d+)\+?\s*jahre?\s*(?:erfahrung|in\s+der)',
            r'mindestens\s+(\d+)\s+jahre?\s+(?:berufserfahrung|erfahrung)',
            
            # English patterns
            r'(\d+)\+?\s*years?\s*(?:of\s*)?(?:experience|in)',
            r'(?:experience|work\s+experience):\s*(\d+)\+?\s*years?',
            r'(\d+)-(\d+)\s*years?\s*(?:of\s*)?experience',
            
            # Date range patterns
            r'(\d{4})-(?:heute|present|now)',
            r'(?:seit|since)\s*(\d{4})',
        ]
        
        # Try to find explicit experience mentions
        for pattern in experience_patterns:
            matches = re.findall(pattern, text_lower)
            if matches:
                if isinstance(matches[0], tuple):
                    if len(matches[0]) == 2:  # Range pattern
                        return f"{matches[0][0]}-{matches[0][1]} years"
                    else:
                        # Date range - calculate years
                        start_year = int(matches[0][0])
                        current_year = datetime.now().year
                        years = current_year - start_year
                        return f"{years} years"
                else:
                    return f"{matches[0]} years"
        
        # Check for experience level keywords
        if any(keyword in text_lower for keyword in ['senior', 'lead', 'manager', 'director', 'principal']):
            return "Senior level (5+ years)"
        elif any(keyword in text_lower for keyword in ['mid-level', 'intermediate', 'experienced']):
            return "Mid-level (2-5 years)"
        elif any(keyword in text_lower for keyword in ['junior', 'entry', 'graduate', 'intern', 'trainee']):
            return "Junior level (0-2 years)"
        
        return "Experience not specified"

    def extract_skills(self, text: str) -> str:
        """Extract skills from CV text"""
        text_lower = text.lower()
        found_skills = []
        
        for skill in self.skill_keywords:
            if skill.lower() in text_lower:
                found_skills.append(skill.title())
        
        if found_skills:
            return ", ".join(found_skills[:10])  # Limit to top 10 skills
        return "Skills not specified"

    def extract_education(self, text: str) -> str:
        """Enhanced education extraction with German support"""
        text_lower = text.lower()
        education_info = []
        
        # Look for education institutions and degrees
        lines = text.split('\n')
        for line in lines:
            line_lower = line.lower()
            if any(keyword in line_lower for keyword in self.education_keywords):
                # Clean and add the line if it looks like education info
                clean_line = line.strip()
                if clean_line and len(clean_line) > 5:
                    # Filter out non-education lines
                    if not any(exclude in line_lower for exclude in ['email', 'phone', 'telefon', 'address']):
                        education_info.append(clean_line)
        
        if education_info:
            return "; ".join(education_info[:3])  # Limit to top 3 entries
        return "Education not specified"

    def extract_cv_data(self, file_path: str, fields: List[str]) -> Dict[str, str]:
        """Extract specified fields from a CV file"""
        # Extract text based on file type
        text = self.extract_text_from_file(file_path)
        if not text:
            return {"error": "Could not extract text from file"}
        
        filename = os.path.basename(file_path)
        data = {}
        
        if 'name' in fields:
            data['name'] = self.extract_name(text, filename)
        if 'email' in fields:
            data['email'] = self.extract_email(text)
        if 'phone' in fields:
            data['phone'] = self.extract_phone(text)
        if 'experience' in fields:
            data['experience'] = self.extract_experience(text)
        if 'skills' in fields:
            data['skills'] = self.extract_skills(text)
        if 'education' in fields:
            data['education'] = self.extract_education(text)
        
        return data

    def extract_cv_data_from_text(self, text: str, filename: str = "", fields: List[str] = None) -> Dict[str, str]:
        """Extract specified fields directly from text"""
        if fields is None:
            fields = ['name', 'email', 'phone', 'experience', 'skills', 'education']
        
        data = {}
        
        if 'name' in fields:
            data['name'] = self.extract_name(text, filename)
        if 'email' in fields:
            data['email'] = self.extract_email(text)
        if 'phone' in fields:
            data['phone'] = self.extract_phone(text)
        if 'experience' in fields:
            data['experience'] = self.extract_experience(text)
        if 'skills' in fields:
            data['skills'] = self.extract_skills(text)
        if 'education' in fields:
            data['education'] = self.extract_education(text)
        
        return data
