{% extends 'base.html' %}

{% block title %}Dashboard - HR Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
        </h1>
    </div>
</div>

<div class="row">
    <!-- Jobs Card -->
    <div class="col-md-4 mb-4">
        <div class="card border-primary h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-briefcase me-2"></i>Jobs
                </h5>
            </div>
            <div class="card-body">
                <h2 class="display-4 text-center">{{ jobs_count }}</h2>
                <p class="text-center">Active job postings</p>
            </div>
            <div class="card-footer bg-transparent border-top-0">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('jobs') }}" class="btn btn-outline-primary">View Jobs</a>
                    <a href="{{ url_for('add_job') }}" class="btn btn-primary">Add New Job</a>
                </div>
            </div>
        </div>
    </div>

    <!-- CVs Card -->
    <div class="col-md-4 mb-4">
        <div class="card border-success h-100">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-alt me-2"></i>CVs
                </h5>
            </div>
            <div class="card-body">
                <p class="text-center">Upload and manage candidate CVs for your job postings.</p>
            </div>
            <div class="card-footer bg-transparent border-top-0">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('cvs') }}" class="btn btn-outline-success">View CVs</a>
                    <a href="{{ url_for('upload_cv') }}" class="btn btn-success">Upload CV</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Matching Card -->
    <div class="col-md-4 mb-4">
        <div class="card border-info h-100">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-check-circle me-2"></i>Matching
                </h5>
            </div>
            <div class="card-body">
                <p class="text-center">Use AI to match candidate CVs to your job requirements.</p>
            </div>
            <div class="card-footer bg-transparent border-top-0">
                <div class="d-grid">
                    <a href="{{ url_for('match') }}" class="btn btn-info text-white">Match CVs</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
