"""Bilingual CV extractor (patched version, July 2025).

Focus: robust name extraction for German CVs that put the name as an
unlabelled heading.  Stand‑alone; no external project deps.
"""

from __future__ import annotations
import re, os
from pathlib import Path

try:
    import fitz  # PyMuPDF
except ImportError:  # pragma: no cover
    raise ImportError("PyMuPDF (fitz) is required: pip install pymupdf")

# ---------- Helper ---------------------------------------------------------

_BAD_TOKENS = {
    # Company / legal forms
    "gmbh", "kg", "ag", "ug", "mbh", "sarl", "s.a.", "e.v.", "ev",
    # Sections / words that are *not* personal names
    "lebenslauf", "curriculum", "vitae", "bewerbung",
    "anschreiben", "anschrift", "straße", "str", "str.", "telefon",
    "tel", "email", "e-mail", "name", "vorname", "nachname",
    # CV sections
    "berufserfahrung", "education", "ausbildung", "qualifikationen",
    "fähigkeiten", "skills", "projekte", "projects", "kontakt", "contact",
    "persönliche", "daten", "personal", "data", "ort", "stadt", "city",
    "deutschland", "germany", "berlin", "münchen", "hamburg"
}

_GERMAN_NAME_PATTERNS = [
    # generic two‑plus word pattern (strict title‑case)
    r"\b([A-ZÄÖÜ][a-zäöüß]+(?:\s+[A-ZÄÖÜ][a-zäöüß]+){1,2})\b",
    # Labelled variants
    r"Name\s*[:–-]\s*([A-ZÄÖÜ][a-zäöüß]+(?:\s+[A-ZÄÖÜ][a-zäöüß]+)+)",
    r"Vor-?\s*und\s*Nachname\s*[:–-]\s*([A-ZÄÖÜ][a-zäöüß]+(?:\s+[A-ZÄÖÜ][a-zäöüß]+)+)",
    r"Bewerbung\s+von\s+([A-ZÄÖÜ][a-zäöüß]+(?:\s+[A-ZÄÖÜ][a-zäöüß]+)+)",
    r"Lebenslauf\s+von\s+([A-ZÄÖÜ][a-zäöüß]+(?:\s+[A-ZÄÖÜ][a-zäöüß]+)+)"
]

_EMAIL_RE = re.compile(r"[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}")
_PHONE_RES = [
    re.compile(r"\+49[\s\-/]?\d{2,4}[\s\-/]?\d{6,8}"),   # intl German (min 6 digits after area)
    re.compile(r"0\d{2,4}[\s\-/]?\d{6,8}"),                # national (min 6 digits after area)
    re.compile(r"\(\d{2,4}\)\s?\d{6,8}"),                 # (0xx) xxx (min 6 digits)
    re.compile(r"(?<!\d)\d{4,5}[\s\-/]\d{6,8}(?!\d)")     # split w/ space or hyphen, avoid dates
]


# ---------- Core class -----------------------------------------------------

class BilingualCVExtractorPatched:
    """Light‑weight extractor focusing on name/email/phone."""

    # -- public API ---------------------------------------------------------
    def extract_cv_data(self, file_path: str) -> dict[str, str]:
        text = self._extract_text_from_file(file_path)
        if not text:
            return {"error": "Could not read PDF text"}

        return {
            "name": self._extract_name(text, os.path.basename(file_path)) or "",
            "email": self._extract_email(text) or "",
            "phone": self._extract_phone(text) or ""
        }

    # -- implementation -----------------------------------------------------
    def _extract_text_from_file(self, file_path: str) -> str:
        with fitz.open(file_path) as doc:
            return "\f".join(page.get_text("text") for page in doc)

    # 1) NAME ---------------------------------------------------------------
    def _extract_name(self, text: str, filename: str = "") -> str | None:
        # A) heading detector on first page
        first_page = text.split("\f", 1)[0] if "\f" in text else text
        heading = self._heading_name(first_page)
        if heading:
            return heading

        # B) two-line split detector (first\nlast)
        lines = first_page.splitlines()
        for i in range(len(lines)-1):
            l1, l2 = lines[i].strip(), lines[i+1].strip()
            clean_l1 = self._clean_name(l1)
            clean_l2 = self._clean_name(l2)
            if (clean_l1 and clean_l2 and 
                self._looks_like_name(clean_l1) and self._looks_like_name(clean_l2)):
                return f"{clean_l1.title()} {clean_l2.title()}"

        # C) regex cascade (without IGNORECASE for generic)
        for idx, pat in enumerate(_GERMAN_NAME_PATTERNS):
            flags = 0 if idx == 0 else re.I
            m = re.search(pat, text, flags)
            if m:
                cand = m.group(1).strip()
                # Clean up the candidate name
                cand = self._clean_name(cand)
                if cand and not self._bad_token(cand):
                    return cand.title()

        # D) fallback: filename stem
        stem = Path(filename).stem.replace("_", " ").replace("-", " ")
        if self._looks_like_name(stem):
            return stem.title()

        return None

    # 1a) helpers
    def _heading_name(self, first_page: str) -> str | None:
        lines = [l.strip() for l in first_page.splitlines() if l.strip()][:20]
        for line in lines:
            # Clean the line first
            cleaned_line = self._clean_name(line)
            if cleaned_line and self._looks_like_name(cleaned_line):
                return cleaned_line.title()
        return None

    def _looks_like_name(self, txt: str) -> bool:
        tokens = txt.split()
        if not 1 <= len(tokens) <= 3:
            return False
        if any(self._bad_token(t) for t in tokens):
            return False
        return all(
            (t.isupper() and len(t) > 1) or
            (t[0].isupper() and t[1:].islower())
            for t in tokens
        )

    def _bad_token(self, token: str) -> bool:
        return token.lower().rstrip(".,") in _BAD_TOKENS
    
    def _clean_name(self, name: str) -> str:
        """Clean up extracted name by removing common prefixes and suffixes"""
        # Remove common prefixes
        prefixes_to_remove = ["lebenslauf:", "name:", "bewerbung von", "cv:", "curriculum vitae"]
        name_lower = name.lower()
        
        for prefix in prefixes_to_remove:
            if name_lower.startswith(prefix):
                name = name[len(prefix):].strip()
                break
        
        # Remove common suffixes and clean up
        name = re.sub(r'\s*[:–-]\s*$', '', name)  # Remove trailing colons/dashes
        name = re.sub(r'\s+', ' ', name)  # Normalize whitespace
        
        # Split and check each part
        parts = name.split()
        clean_parts = []
        
        for part in parts:
            # Skip parts that are clearly not names
            if (len(part) < 2 or 
                part.lower() in _BAD_TOKENS or
                part.isdigit() or
                '@' in part or
                part.lower() in ['von', 'der', 'die', 'das', 'und', 'the', 'of', 'and']):
                continue
            clean_parts.append(part)
        
        # Return cleaned name if we have 1-3 valid parts
        if 1 <= len(clean_parts) <= 3:
            return ' '.join(clean_parts)
        
        return ""

    # 2) EMAIL --------------------------------------------------------------
    def _extract_email(self, text: str) -> str | None:
        m = _EMAIL_RE.search(text)
        return m.group(0) if m else None

    # 3) PHONE --------------------------------------------------------------
    def _extract_phone(self, text: str) -> str | None:
        for reg in _PHONE_RES:
            m = reg.search(text)
            if m:
                phone = m.group(0)
                # Filter out obvious non-phone numbers
                if self._is_valid_phone(phone):
                    return phone
        return None
    
    def _is_valid_phone(self, phone: str) -> bool:
        """Check if extracted phone number is likely valid"""
        # Remove all non-digits to check
        digits_only = re.sub(r'\D', '', phone)
        
        # Must have at least 6 digits for a valid phone
        if len(digits_only) < 6:
            return False
        
        # Avoid common date patterns
        if re.match(r'^\d{2}[-/]\d{4}$', phone):  # MM/YYYY or MM-YYYY
            return False
        if re.match(r'^\d{3}[-/]\d{4}$', phone):  # MMM/YYYY or MMM-YYYY  
            return False
        
        # Must not be all the same digit
        if len(set(digits_only)) == 1:
            return False
            
        return True


# Backward compatibility alias
BilingualCVExtractor = BilingualCVExtractorPatched
