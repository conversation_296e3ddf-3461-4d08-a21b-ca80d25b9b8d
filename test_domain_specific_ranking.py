#!/usr/bin/env python3
"""
Test the new domain-specific, conservative ranking approach
"""

from hr_database_working import HRDatabase
from domain_specific_matcher import DomainSpecificMatcher

def test_domain_specific_ranking():
    """Test the new conservative domain-specific ranking"""
    print("=== DOMAIN-SPECIFIC CONSERVATIVE RANKING TEST ===")
    print()
    
    try:
        db = HRDatabase()
        matcher = DomainSpecificMatcher()
        
        # Get CNC job and CVs
        cnc_job = db.get_job_by_title('CNC Fräser')
        if not cnc_job:
            print("CNC job not found!")
            return
            
        cvs = db.get_cvs_for_job('C<PERSON> Fräser')
        print(f"Found {len(cvs)} CVs for CNC job")
        print()
        
        # Extract job requirements
        job_requirements = matcher.extract_job_requirements(cnc_job.description)
        
        print("=== JOB REQUIREMENTS ANALYSIS ===")
        print(f"Domain: {job_requirements.domain}")
        print(f"Required Qualifications: {job_requirements.required_qualifications}")
        print(f"Domain Knowledge: {job_requirements.domain_knowledge}")
        print(f"Specific Skills: {job_requirements.specific_skills}")
        print(f"Production Type: {job_requirements.production_type}")
        print(f"Leadership Required: {job_requirements.leadership_required}")
        print(f"Min Experience Years: {job_requirements.min_experience_years}")
        print()
        
        # Evaluate each candidate
        results = []
        
        print("=== CANDIDATE EVALUATIONS ===")
        for cv in cvs:
            print(f"\n--- {cv.candidate_name or 'Unknown'} ---")
            
            # Evaluate candidate
            candidate_profile = matcher.evaluate_candidate(cv.content, job_requirements)
            
            # Calculate conservative score
            scores = matcher.calculate_conservative_score(candidate_profile, job_requirements, cv.content)
            
            # Display detailed analysis
            print(f"Relevant Experience: {candidate_profile.relevant_experience_years} years")
            print(f"Recent Experience: {candidate_profile.recent_experience}")
            print(f"Domain Skills: {candidate_profile.domain_skills}")
            print(f"Education Level: {candidate_profile.education_level}")
            print(f"Gaps/Mismatches: {candidate_profile.gaps_mismatches}")
            print(f"Irrelevant Experience: {candidate_profile.irrelevant_experience}")
            print()
            print(f"SCORES:")
            print(f"  Final Score: {scores['final_score']:.1f}%")
            print(f"  Experience: {scores['experience_score']:.1f}%")
            print(f"  Skills: {scores['skills_score']:.1f}%")
            print(f"  Education: {scores['education_score']:.1f}%")
            print(f"  Penalties: -{scores['penalties']:.1f}%")
            
            results.append({
                'name': candidate_profile.name,
                'profile': candidate_profile,
                'scores': scores,
                'final_score': scores['final_score']
            })
        
        # Sort by final score
        results.sort(key=lambda x: x['final_score'], reverse=True)
        
        print("\n" + "="*60)
        print("=== FINAL RANKING ===")
        print()
        
        for i, result in enumerate(results, 1):
            profile = result['profile']
            scores = result['scores']
            
            # Determine strengths and weaknesses
            strengths = []
            weaknesses = []
            
            if scores['experience_score'] > 60:
                strengths.append(f"Relevant experience ({profile.relevant_experience_years} years)")
            else:
                weaknesses.append("Limited relevant experience")
            
            if len(profile.domain_skills) > 2:
                strengths.append(f"Good domain skills ({len(profile.domain_skills)} skills)")
            else:
                weaknesses.append("Limited domain skills")
            
            if profile.education_level == 'vocational':
                strengths.append("Appropriate vocational training")
            elif profile.education_level in ['bachelor', 'master']:
                strengths.append("Academic qualification")
            else:
                weaknesses.append("Education level unclear")
            
            if profile.gaps_mismatches:
                weaknesses.extend(profile.gaps_mismatches)
            
            # Recommendation
            if scores['final_score'] >= 70:
                recommendation = "Sehr geeignet"
                emoji = "🥇"
            elif scores['final_score'] >= 50:
                recommendation = "Geeignet mit Einschränkungen"
                emoji = "🥈"
            elif scores['final_score'] >= 30:
                recommendation = "Ausbaufähig"
                emoji = "🥉"
            else:
                recommendation = "Nicht geeignet"
                emoji = "❌"
            
            print(f"{emoji} {i}. {result['name']} ({scores['final_score']:.1f}%)")
            print(f"   Stärken: {'; '.join(strengths) if strengths else 'Keine erkannt'}")
            print(f"   Schwächen: {'; '.join(weaknesses) if weaknesses else 'Keine erkannt'}")
            print(f"   Empfehlung: {recommendation}")
            print()
        
        # Compare with expected ranking from your analysis
        print("=== COMPARISON WITH EXPECTED RANKING ===")
        expected_order = ["Daniel Meixner", "Horst Lippert", "Pascal Baun", "Werner Steiger"]
        
        actual_order = [result['name'] for result in results]
        
        print("Expected order (from your analysis):")
        for i, name in enumerate(expected_order, 1):
            print(f"  {i}. {name}")
        
        print("\nActual order (domain-specific algorithm):")
        for i, name in enumerate(actual_order, 1):
            print(f"  {i}. {name}")
        
        # Check if Daniel is ranked appropriately high
        daniel_rank = next((i for i, result in enumerate(results, 1) 
                          if 'Daniel' in result['name']), None)
        
        print(f"\nDaniel Meixner ranking: #{daniel_rank}")
        
        if daniel_rank and daniel_rank <= 2:
            print("✅ SUCCESS: Daniel is ranked in top 2 (appropriate for his QS+CNC experience)")
        elif daniel_rank and daniel_rank <= 3:
            print("⚠️  ACCEPTABLE: Daniel is ranked #3 (could be higher given his domain experience)")
        else:
            print("❌ ISSUE: Daniel should be ranked higher given his QS and manufacturing experience")
        
        # Show detailed analysis for Daniel
        daniel_result = next((r for r in results if 'Daniel' in r['name']), None)
        if daniel_result:
            print(f"\nDETAILED ANALYSIS - Daniel Meixner:")
            profile = daniel_result['profile']
            print(f"  Relevant Experience: {profile.relevant_experience_years} years")
            print(f"  Domain Skills Found: {profile.domain_skills}")
            print(f"  Recent Experience: {profile.recent_experience}")
            print(f"  Final Score: {daniel_result['scores']['final_score']:.1f}%")
            
            # Check if QS/Messtechnik experience was detected
            qs_detected = any('qs' in skill.lower() or 'mess' in skill.lower() 
                            for skill in profile.domain_skills)
            if qs_detected:
                print("  ✅ QS/Messtechnik experience detected")
            else:
                print("  ❌ QS/Messtechnik experience NOT detected - algorithm needs improvement")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_domain_specific_ranking()
