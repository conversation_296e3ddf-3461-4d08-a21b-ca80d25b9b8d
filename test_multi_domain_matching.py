#!/usr/bin/env python3
"""
Test the improved matching system across multiple domains to verify it works universally
"""

from matcher import <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_multi_domain_matching():
    """Test matching across different job domains"""
    print("=== TESTING MULTI-DOMAIN MATCHING SYSTEM ===\n")
    
    matcher = CVMatcher(use_enhanced_matching=False, use_domain_specific=False, use_job_specific=False)
    
    # Test cases for different domains
    test_cases = [
        {
            'domain': 'Manufacturing/CNC',
            'job_description': """
            CNC-Fräser (m/w/d) gesucht
            Ihre Tätigkeit:
            - Selbstständiges Programmieren, Rüsten und Einfahren der CNC-Maschinen
            - Erstbemusterung und Übergabe in die Serienproduktion
            - Qualitätskontrolle und Messtechnik
            - Fanuc und Heidenhain Steuerungen
            Anforderungen:
            - Ausbildung als Zerspanungsmechaniker oder Industriemechaniker
            - Mehrjährige Berufserfahrung in der Zerspanung
            """,
            'candidates': [
                {
                    'name': 'CNC Expert',
                    'cv': """
                    Max Mustermann
                    Zerspanungsmechaniker
                    Berufserfahrung:
                    2018-2024: CNC-Programmierer bei Maschinenbau GmbH
                    - CNC-Programmierung für Fanuc und Heidenhain Steuerungen
                    - Rüsten und Einfahren von CNC-Fräsmaschinen
                    - Erstbemusterung und Serienproduktion
                    - Qualitätskontrolle und Messtechnik
                    """
                },
                {
                    'name': 'Software Developer',
                    'cv': """
                    Anna Schmidt
                    Software Entwicklerin
                    Berufserfahrung:
                    2019-2024: Java Entwicklerin bei Tech Company
                    - Java Spring Boot Entwicklung
                    - React Frontend Entwicklung
                    - Datenbank Design mit PostgreSQL
                    - Agile Scrum Methoden
                    """
                }
            ]
        },
        {
            'domain': 'Software Development',
            'job_description': """
            Java Entwickler (m/w/d) gesucht
            Ihre Aufgaben:
            - Entwicklung von Java Spring Boot Anwendungen
            - Frontend Entwicklung mit React
            - Datenbank Design und Optimierung
            - Agile Entwicklung im Scrum Team
            Anforderungen:
            - Studium der Informatik oder vergleichbare Qualifikation
            - Mehrjährige Erfahrung in Java Entwicklung
            - Kenntnisse in Spring Framework und React
            """,
            'candidates': [
                {
                    'name': 'Java Expert',
                    'cv': """
                    Peter Müller
                    Software Entwickler
                    Ausbildung: Bachelor Informatik
                    Berufserfahrung:
                    2020-2024: Senior Java Entwickler bei Software AG
                    - Java Spring Boot Microservices Entwicklung
                    - React und Angular Frontend Entwicklung
                    - PostgreSQL und MongoDB Datenbanken
                    - Scrum Master Zertifizierung
                    """
                },
                {
                    'name': 'CNC Machinist',
                    'cv': """
                    Klaus Weber
                    Zerspanungsmechaniker
                    Berufserfahrung:
                    2015-2024: CNC-Fräser bei Metallbau GmbH
                    - CNC-Programmierung und Maschinenbedienung
                    - Qualitätskontrolle und Messtechnik
                    - Fanuc Steuerungen
                    """
                }
            ]
        },
        {
            'domain': 'Finance',
            'job_description': """
            Finanzanalyst (m/w/d) gesucht
            Ihre Aufgaben:
            - Finanzanalyse und Reporting
            - Budgetplanung und Forecasting
            - SAP Controlling
            - Jahresabschluss nach HGB und IFRS
            Anforderungen:
            - Studium BWL, Wirtschaftswissenschaften oder vergleichbar
            - Mehrjährige Erfahrung im Controlling
            - SAP Kenntnisse erforderlich
            """,
            'candidates': [
                {
                    'name': 'Finance Expert',
                    'cv': """
                    Maria Fischer
                    Diplom-Betriebswirtin
                    Berufserfahrung:
                    2018-2024: Senior Controller bei Finance Corp
                    - Finanzanalyse und Management Reporting
                    - Budgetplanung und Variance Analysis
                    - SAP Controlling und CO-PA
                    - Jahresabschluss nach HGB und IFRS
                    """
                },
                {
                    'name': 'Marketing Manager',
                    'cv': """
                    Thomas Klein
                    Marketing Manager
                    Berufserfahrung:
                    2019-2024: Digital Marketing Manager
                    - Social Media Marketing
                    - Google Analytics und SEO
                    - Content Marketing
                    - Brand Management
                    """
                }
            ]
        }
    ]
    
    # Test each domain
    for test_case in test_cases:
        print(f"=== {test_case['domain'].upper()} DOMAIN TEST ===")
        print(f"Job: {test_case['job_description'][:100]}...")
        print()
        
        results = []
        for candidate in test_case['candidates']:
            # Calculate scores
            tf_idf_score = matcher.calculate_tf_idf_similarity(test_case['job_description'], candidate['cv'])
            keyword_score = matcher.calculate_keyword_match(test_case['job_description'], candidate['cv'])
            skill_score = matcher.calculate_skill_match(test_case['job_description'], candidate['cv'])
            
            # Detect job type for proper weighting
            job_type = matcher._detect_job_type(test_case['job_description'].lower())
            
            if job_type == 'manufacturing':
                overall_score = (skill_score * 0.6) + (keyword_score * 0.25) + (tf_idf_score * 0.15)
            else:
                overall_score = (skill_score * 0.5) + (keyword_score * 0.3) + (tf_idf_score * 0.2)
            
            results.append({
                'name': candidate['name'],
                'overall': overall_score * 100,
                'skill': skill_score * 100,
                'keyword': keyword_score * 100,
                'tfidf': tf_idf_score * 100,
                'job_type': job_type
            })
        
        # Sort by overall score
        results.sort(key=lambda x: x['overall'], reverse=True)
        
        print("RANKING RESULTS:")
        for i, result in enumerate(results, 1):
            print(f"{i}. {result['name']}")
            print(f"   Overall: {result['overall']:.1f}% (Skill: {result['skill']:.1f}%, Keyword: {result['keyword']:.1f}%, TF-IDF: {result['tfidf']:.1f}%)")
            print(f"   Job Type Detected: {result['job_type']}")
            
            # Analyze if ranking is correct
            if test_case['domain'] == 'Manufacturing/CNC':
                if 'CNC Expert' in result['name'] and i == 1:
                    print("   ✅ CORRECT: CNC expert ranked first for CNC job")
                elif 'Software Developer' in result['name'] and i == 2:
                    print("   ✅ CORRECT: Software developer ranked lower for CNC job")
                else:
                    print("   ❌ INCORRECT: Wrong ranking for manufacturing job")
                    
            elif test_case['domain'] == 'Software Development':
                if 'Java Expert' in result['name'] and i == 1:
                    print("   ✅ CORRECT: Java expert ranked first for Java job")
                elif 'CNC Machinist' in result['name'] and i == 2:
                    print("   ✅ CORRECT: CNC machinist ranked lower for Java job")
                else:
                    print("   ❌ INCORRECT: Wrong ranking for software job")
                    
            elif test_case['domain'] == 'Finance':
                if 'Finance Expert' in result['name'] and i == 1:
                    print("   ✅ CORRECT: Finance expert ranked first for finance job")
                elif 'Marketing Manager' in result['name'] and i == 2:
                    print("   ✅ CORRECT: Marketing manager ranked lower for finance job")
                else:
                    print("   ❌ INCORRECT: Wrong ranking for finance job")
        
        print("\n" + "="*60 + "\n")

if __name__ == "__main__":
    test_multi_domain_matching()
