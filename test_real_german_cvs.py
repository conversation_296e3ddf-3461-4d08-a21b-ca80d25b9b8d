#!/usr/bin/env python3
"""
Test Enhanced German CV Extractor with Real CV Files
Validates the improved experience and skills extraction on actual CV files
"""

import os
import glob
from typing import Dict, List
from bilingual_cv_extractor_patched import BilingualCVExtractorPatched

class RealGermanCVTest:
    def __init__(self):
        self.extractor = BilingualCVExtractorPatched()
        self.cv_files = self._find_cv_files()
        
    def _find_cv_files(self) -> List[str]:
        """Find all CV files in uploads directory"""
        cv_files = []
        uploads_dir = "uploads"
        
        if os.path.exists(uploads_dir):
            pdf_files = glob.glob(os.path.join(uploads_dir, "*.pdf"))
            cv_files.extend(pdf_files)
            docx_files = glob.glob(os.path.join(uploads_dir, "*.docx"))
            cv_files.extend(docx_files)
        
        return cv_files
    
    def test_enhanced_extraction(self):
        """Test enhanced extraction on real CV files"""
        print("🚀 TESTING ENHANCED GERMAN CV EXTRACTOR ON REAL FILES")
        print("=" * 80)
        print(f"📁 Found {len(self.cv_files)} CV files to test")
        print()
        
        all_fields = ['name', 'email', 'phone', 'experience', 'skills', 'education', 'seniority']
        
        for i, cv_file in enumerate(self.cv_files, 1):
            filename = os.path.basename(cv_file)
            print(f"📄 {i}. Testing: {filename}")
            print("-" * 60)
            
            try:
                # Extract all fields
                result = self.extractor.extract_cv_data(cv_file, all_fields)
                
                if 'error' in result:
                    print(f"   ❌ Error: {result['error']}")
                    continue
                
                # Display results
                print(f"   👤 Name: '{result.get('name', 'N/A')}'")
                print(f"   📧 Email: '{result.get('email', 'N/A')}'")
                print(f"   📞 Phone: '{result.get('phone', 'N/A')}'")
                print(f"   💼 Experience: '{result.get('experience', 'N/A')}'")
                print(f"   🛠️  Skills: '{result.get('skills', 'N/A')}'")
                print(f"   🎓 Education: '{result.get('education', 'N/A')}'")
                print(f"   📊 Seniority: '{result.get('seniority', 'N/A')}'")
                
                # Analyze quality
                self._analyze_extraction_quality(filename, result)
                
            except Exception as e:
                print(f"   ❌ Exception: {e}")
            
            print()
    
    def _analyze_extraction_quality(self, filename: str, result: Dict[str, str]):
        """Analyze the quality of extraction results"""
        issues = []
        improvements = []
        
        # Check for common issues
        experience = result.get('experience', '')
        skills = result.get('skills', '')
        
        # Experience analysis
        if experience == "Berufserfahrung nicht spezifiziert":
            issues.append("❌ Experience not detected")
        elif "Jahre" in experience:
            improvements.append("✅ Years of experience detected")
        elif any(word in experience.lower() for word in ['bei', 'company', 'position']):
            improvements.append("✅ Job position/company detected")
        
        # Skills analysis
        if skills == "Fähigkeiten nicht spezifiziert":
            issues.append("❌ Skills not detected")
        elif skills == "R":
            issues.append("❌ Only placeholder 'R' detected as skill")
        elif len(skills.split(',')) >= 3:
            improvements.append(f"✅ Multiple skills detected ({len(skills.split(','))} skills)")
        
        # Check for German-specific improvements
        if any(german_word in experience.lower() for german_word in ['jahre', 'berufserfahrung', 'entwickler']):
            improvements.append("✅ German experience patterns recognized")
        
        if any(german_skill in skills.lower() for german_skill in ['programmierung', 'webentwicklung', 'projektmanagement']):
            improvements.append("✅ German skill terms recognized")
        
        # Display analysis
        if improvements:
            print(f"   🎯 Improvements:")
            for improvement in improvements:
                print(f"      {improvement}")
        
        if issues:
            print(f"   ⚠️  Issues:")
            for issue in issues:
                print(f"      {issue}")
        
        if not issues and improvements:
            print(f"   🏆 Excellent extraction quality!")
    
    def compare_with_basic_extractor(self):
        """Compare enhanced extractor with basic extractor"""
        print("🔄 COMPARING ENHANCED VS BASIC EXTRACTOR")
        print("=" * 80)
        
        # Import basic extractor for comparison
        try:
            from cv_extractor import CVDataExtractor
            basic_extractor = CVDataExtractor()
        except ImportError:
            print("❌ Basic extractor not available for comparison")
            return
        
        comparison_results = []
        
        for cv_file in self.cv_files[:5]:  # Test first 5 files
            filename = os.path.basename(cv_file)
            print(f"\n📄 Comparing: {filename}")
            print("-" * 40)
            
            try:
                # Enhanced extractor
                enhanced_result = self.extractor.extract_cv_data(cv_file, ['experience', 'skills'])
                
                # Basic extractor
                basic_result = basic_extractor.extract_cv_data(cv_file, ['experience', 'skills'])
                
                print(f"   Enhanced Experience: '{enhanced_result.get('experience', 'N/A')}'")
                print(f"   Basic Experience:    '{basic_result.get('experience', 'N/A')}'")
                print(f"   Enhanced Skills:     '{enhanced_result.get('skills', 'N/A')}'")
                print(f"   Basic Skills:        '{basic_result.get('skills', 'N/A')}'")
                
                # Determine which is better
                enhanced_better = self._is_extraction_better(enhanced_result, basic_result)
                print(f"   Winner: {'🏆 Enhanced' if enhanced_better else '🏆 Basic' if enhanced_better is False else '🤝 Tie'}")
                
                comparison_results.append({
                    'file': filename,
                    'enhanced_better': enhanced_better,
                    'enhanced': enhanced_result,
                    'basic': basic_result
                })
                
            except Exception as e:
                print(f"   ❌ Error comparing {filename}: {e}")
        
        # Summary
        if comparison_results:
            enhanced_wins = sum(1 for r in comparison_results if r['enhanced_better'] is True)
            basic_wins = sum(1 for r in comparison_results if r['enhanced_better'] is False)
            ties = sum(1 for r in comparison_results if r['enhanced_better'] is None)
            
            print(f"\n📊 COMPARISON SUMMARY:")
            print(f"   Enhanced Extractor Wins: {enhanced_wins}")
            print(f"   Basic Extractor Wins: {basic_wins}")
            print(f"   Ties: {ties}")
            print(f"   Enhanced Success Rate: {(enhanced_wins / len(comparison_results)) * 100:.1f}%")
    
    def _is_extraction_better(self, enhanced: Dict, basic: Dict) -> bool:
        """Determine if enhanced extraction is better than basic"""
        enhanced_exp = enhanced.get('experience', '')
        basic_exp = basic.get('experience', '')
        enhanced_skills = enhanced.get('skills', '')
        basic_skills = basic.get('skills', '')
        
        enhanced_score = 0
        basic_score = 0
        
        # Experience scoring
        if enhanced_exp != "Berufserfahrung nicht spezifiziert" and "Jahre" in enhanced_exp:
            enhanced_score += 2
        if basic_exp != "Experience not specified" and basic_exp.strip():
            basic_score += 1
        
        # Skills scoring
        if enhanced_skills != "Fähigkeiten nicht spezifiziert" and enhanced_skills != "R":
            enhanced_skills_count = len(enhanced_skills.split(','))
            enhanced_score += min(enhanced_skills_count, 3)
        
        if basic_skills != "Skills not specified" and basic_skills.strip():
            basic_skills_count = len(basic_skills.split(','))
            basic_score += min(basic_skills_count, 3)
        
        if enhanced_score > basic_score:
            return True
        elif basic_score > enhanced_score:
            return False
        else:
            return None  # Tie
    
    def test_specific_improvements(self):
        """Test specific improvements for German CVs"""
        print("🎯 TESTING SPECIFIC GERMAN CV IMPROVEMENTS")
        print("=" * 80)
        
        improvements_tested = {
            'german_experience_patterns': 0,
            'german_skills_detection': 0,
            'job_position_extraction': 0,
            'seniority_classification': 0,
            'education_extraction': 0
        }
        
        improvements_found = {key: 0 for key in improvements_tested}
        
        for cv_file in self.cv_files:
            filename = os.path.basename(cv_file)
            
            try:
                result = self.extractor.extract_cv_data(cv_file, ['experience', 'skills', 'education', 'seniority'])
                
                experience = result.get('experience', '').lower()
                skills = result.get('skills', '').lower()
                education = result.get('education', '').lower()
                seniority = result.get('seniority', '').lower()
                
                # Test German experience patterns
                improvements_tested['german_experience_patterns'] += 1
                if any(word in experience for word in ['jahre', 'berufserfahrung', 'entwickler', 'bei']):
                    improvements_found['german_experience_patterns'] += 1
                
                # Test German skills detection
                improvements_tested['german_skills_detection'] += 1
                if any(word in skills for word in ['programmierung', 'webentwicklung', 'projektmanagement', 'teamarbeit']):
                    improvements_found['german_skills_detection'] += 1
                
                # Test job position extraction
                improvements_tested['job_position_extraction'] += 1
                if 'bei' in experience or any(word in experience for word in ['entwickler', 'manager', 'leiter']):
                    improvements_found['job_position_extraction'] += 1
                
                # Test seniority classification
                improvements_tested['seniority_classification'] += 1
                if seniority in ['junior', 'mid', 'senior']:
                    improvements_found['seniority_classification'] += 1
                
                # Test education extraction
                improvements_tested['education_extraction'] += 1
                if any(word in education for word in ['bachelor', 'master', 'diplom', 'studium', 'informatik']):
                    improvements_found['education_extraction'] += 1
                    
            except Exception as e:
                print(f"Error testing {filename}: {e}")
        
        # Display results
        print("📊 IMPROVEMENT DETECTION RESULTS:")
        print("-" * 40)
        for improvement, tested in improvements_tested.items():
            found = improvements_found[improvement]
            percentage = (found / tested * 100) if tested > 0 else 0
            print(f"   {improvement.replace('_', ' ').title()}: {found}/{tested} ({percentage:.1f}%)")
    
    def run_all_tests(self):
        """Run all real CV tests"""
        self.test_enhanced_extraction()
        print("\n" + "="*80 + "\n")
        self.compare_with_basic_extractor()
        print("\n" + "="*80 + "\n")
        self.test_specific_improvements()
        
        print(f"\n✅ REAL CV TESTING COMPLETED!")
        print("=" * 80)

def main():
    """Run real German CV tests"""
    test_suite = RealGermanCVTest()
    test_suite.run_all_tests()

if __name__ == "__main__":
    main()
