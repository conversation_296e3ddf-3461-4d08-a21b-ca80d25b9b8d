#!/usr/bin/env python3
"""
Test script for the Universal Domain Matcher
Demonstrates improved CV matching across all job domains
"""

from domain_specific_matcher import UniversalDomainMatcher
from matcher import CVMatcher

def test_cnc_job_matching():
    """Test CNC job matching (based on your example)"""
    print("=" * 60)
    print("TESTING CNC JOB MATCHING")
    print("=" * 60)
    
    cnc_job_description = """
    CNC-Programmierer / Einrichter (m/w/d) für Serienfertigung
    
    Ihre Aufgaben:
    - Selbstständiges Programmieren und Rüsten von CNC-Maschinen (Fanuc/Heidenhain)
    - Einfahren neuer Programme und Übergabe in die Serie
    - Erstmusterprüfung und VDA-Berichte
    - Prozessoptimierung und kontinuierliche Verbesserung
    - Qualitätskontrolle und Messtechnik
    
    Ihr Profil:
    - Abgeschlossene Berufsausbildung als Zerspanungsmechaniker oder vergleichbar
    - Mindestens 3 Jahre Berufserfahrung in der CNC-Programmierung
    - Erfahrung mit Fanuc oder Heidenhain Steuerungen
    - Kenntnisse in Serienfertigung und Qualitätssicherung
    - Teamfähigkeit und Eigenverantwortung
    """
    
    # Test candidates (based on your example)
    candidates = {
        "Daniel Meixner": """
        Daniel Meixner
        Qualitätssicherung Spezialist
        
        Berufserfahrung:
        2019-heute: QS-Spezialist bei Automotive GmbH
        - CNC-Programmierung von Messmaschinen (Mitutoyo)
        - Serienfertigung und Erstmusterprüfung
        - VDA-Berichte und Qualitätskontrolle
        - Prozessoptimierung und KVP
        
        2015-2019: Fertigungsmechaniker bei Precision Parts
        - NC-Drehen und Fräsen
        - Maschinenbedienung und Rüsten
        
        Ausbildung:
        2012-2015: Ausbildung Fertigungsmechaniker (IHK)
        """,
        
        "Horst Lippert": """
        Horst Lippert
        Metallfacharbeiter
        
        Berufserfahrung:
        2020-heute: Verkäufer bei Einzelhandel GmbH
        2015-2020: Bürokaufmann bei Verwaltung AG
        2010-2015: Metallfacharbeiter bei Maschinenbau Schmidt
        - CNC-Drehen und Fräsen
        - Werkzeugeinstellung
        
        1995-2010: Verschiedene Tätigkeiten im Vertrieb
        
        Ausbildung:
        1990-1993: Ausbildung Flugzeugmechaniker (IHK)
        Weiterbildung: CNC-Fachkraft Drehen/Fräsen (2010)
        """,
        
        "Pascal Baun": """
        Pascal Baun
        Technikerschüler
        
        Berufserfahrung:
        2022-heute: Werkstudent bei Engineering Solutions
        - CAD-Konstruktion
        - Projektunterstützung
        
        2020-2022: Spritzguss-Einrichter bei Kunststoff GmbH
        - Maschineneinrichtung
        - Qualitätskontrolle
        
        Ausbildung:
        2023-heute: Technikerschule Maschinenbau (laufend)
        2017-2020: Ausbildung Verfahrensmechaniker Kunststoff
        """,
        
        "Werner Stieger": """
        Werner Stieger
        Messtechnik-Experte
        
        Berufserfahrung:
        2018-heute: Technischer Leiter Messtechnik bei Quality Systems
        - 3D-Messtechnik und Quindos-Programmierung
        - EMPB nach VDA-Standards
        - Koordinatenmesstechnik (KMG)
        - DGQ-Qualitätsmanagement
        
        2010-2018: QS-Ingenieur bei Automotive Supplier
        - Produktionsbegleitende Qualitätssicherung
        - Lieferantenaudits
        
        Ausbildung:
        2005-2010: Studium Geographie (Diplom)
        """
    }
    
    matcher = UniversalDomainMatcher()
    
    print(f"\nJob Description Analysis:")
    job_requirements = matcher.extract_job_requirements(cnc_job_description)
    print(f"Domain detected: {job_requirements.domain}")
    print(f"Core competencies: {job_requirements.core_competencies}")
    print(f"Required qualifications: {job_requirements.required_qualifications}")
    print(f"Min experience: {job_requirements.min_experience_years} years")
    
    print(f"\nCandidate Evaluations:")
    results = []
    
    for name, cv_content in candidates.items():
        candidate_profile = matcher.evaluate_candidate(cv_content, job_requirements)
        scores = matcher.calculate_conservative_score(candidate_profile, job_requirements, cv_content)
        
        results.append((scores['final_score'], name, scores))
        
        print(f"\n{name}:")
        print(f"  Final Score: {scores['final_score']:.1f}%")
        print(f"  Experience: {candidate_profile.relevant_experience_years} years")
        print(f"  Core competency matches: {candidate_profile.core_competency_matches}")
        print(f"  Domain skills: {len(candidate_profile.domain_skills)} found")
        print(f"  Education: {candidate_profile.education_level}")
    
    # Sort by score
    results.sort(reverse=True)
    print(f"\nFINAL RANKING:")
    for i, (score, name, details) in enumerate(results, 1):
        print(f"{i}. {name}: {score:.1f}%")
    
    return results

def test_software_job_matching():
    """Test software job matching"""
    print("\n" + "=" * 60)
    print("TESTING SOFTWARE JOB MATCHING")
    print("=" * 60)
    
    software_job_description = """
    Senior Java Developer (m/w/d)
    
    Your tasks:
    - Development of enterprise applications using Java and Spring Boot
    - REST API development and microservices architecture
    - Database design with PostgreSQL
    - Agile development in Scrum teams
    - Code reviews and mentoring junior developers
    
    Your profile:
    - Bachelor's degree in Computer Science or equivalent
    - 5+ years of professional Java development experience
    - Strong knowledge of Spring Framework, Spring Boot
    - Experience with PostgreSQL, REST APIs
    - Knowledge of Git, Jenkins, Docker
    - Agile/Scrum methodology experience
    """
    
    candidates = {
        "Anna Schmidt": """
        Anna Schmidt
        Senior Software Developer
        
        Experience:
        2019-present: Senior Java Developer at TechCorp
        - Java 11/17 enterprise application development
        - Spring Boot microservices architecture
        - PostgreSQL database design and optimization
        - REST API development and documentation
        - Scrum team leadership and mentoring
        
        2016-2019: Java Developer at StartupXYZ
        - Full-stack development with Java and React
        - Git version control and Jenkins CI/CD
        - Docker containerization
        
        Education:
        2012-2016: Bachelor Computer Science, University of Technology
        """,
        
        "Max Mueller": """
        Max Mueller
        CNC Programmer
        
        Experience:
        2018-present: CNC Programmer at Manufacturing Inc
        - Fanuc CNC programming
        - Quality control and measurement
        - Series production optimization
        
        2015-2018: Machinist at Precision Tools
        - Manual machining and setup
        
        Education:
        2012-2015: Apprenticeship Machinist (IHK)
        
        Additional: Basic programming course (2020)
        """
    }
    
    matcher = UniversalDomainMatcher()
    
    job_requirements = matcher.extract_job_requirements(software_job_description)
    print(f"Domain detected: {job_requirements.domain}")
    print(f"Core competencies: {job_requirements.core_competencies}")
    
    results = []
    for name, cv_content in candidates.items():
        candidate_profile = matcher.evaluate_candidate(cv_content, job_requirements)
        scores = matcher.calculate_conservative_score(candidate_profile, job_requirements, cv_content)
        
        results.append((scores['final_score'], name, scores))
        
        print(f"\n{name}:")
        print(f"  Final Score: {scores['final_score']:.1f}%")
        print(f"  Core competency matches: {candidate_profile.core_competency_matches}")
    
    results.sort(reverse=True)
    print(f"\nFINAL RANKING:")
    for i, (score, name, details) in enumerate(results, 1):
        print(f"{i}. {name}: {score:.1f}%")
    
    return results

def test_finance_job_matching():
    """Test finance job matching"""
    print("\n" + "=" * 60)
    print("TESTING FINANCE JOB MATCHING")
    print("=" * 60)
    
    finance_job_description = """
    Financial Analyst (m/w/d)
    
    Responsibilities:
    - Financial reporting and analysis
    - Budget planning and forecasting
    - Variance analysis and KPI reporting
    - SAP FI/CO module usage
    - Compliance and audit support
    
    Requirements:
    - Bachelor's degree in Finance, Accounting, or Economics
    - 3+ years experience in financial analysis
    - Strong Excel and SAP skills
    - Knowledge of IFRS and HGB
    - Analytical thinking and attention to detail
    """
    
    candidates = {
        "Lisa Weber": """
        Lisa Weber
        Financial Controller
        
        Experience:
        2020-present: Financial Analyst at Global Corp
        - Monthly financial reporting and analysis
        - Budget planning and variance analysis
        - SAP FI/CO implementation and training
        - IFRS compliance and audit coordination
        - Excel modeling and automation
        
        2017-2020: Junior Accountant at Consulting Firm
        - Bookkeeping and financial statements
        - Tax preparation and compliance
        
        Education:
        2013-2017: Bachelor Finance and Accounting
        """,
        
        "Tom Becker": """
        Tom Becker
        Software Developer
        
        Experience:
        2019-present: Java Developer at FinTech Startup
        - Banking software development
        - Payment processing systems
        - Database optimization
        
        Education:
        2015-2019: Bachelor Computer Science
        """
    }
    
    matcher = UniversalDomainMatcher()
    
    job_requirements = matcher.extract_job_requirements(finance_job_description)
    print(f"Domain detected: {job_requirements.domain}")
    print(f"Core competencies: {job_requirements.core_competencies}")
    
    results = []
    for name, cv_content in candidates.items():
        candidate_profile = matcher.evaluate_candidate(cv_content, job_requirements)
        scores = matcher.calculate_conservative_score(candidate_profile, job_requirements, cv_content)
        
        results.append((scores['final_score'], name, scores))
        
        print(f"\n{name}:")
        print(f"  Final Score: {scores['final_score']:.1f}%")
        print(f"  Core competency matches: {candidate_profile.core_competency_matches}")
    
    results.sort(reverse=True)
    print(f"\nFINAL RANKING:")
    for i, (score, name, details) in enumerate(results, 1):
        print(f"{i}. {name}: {score:.1f}%")
    
    return results

if __name__ == "__main__":
    print("UNIVERSAL DOMAIN MATCHER TEST")
    print("Testing improved CV matching across all job domains")
    print("Based on conservative, domain-focused evaluation principles")
    
    # Test different domains
    cnc_results = test_cnc_job_matching()
    software_results = test_software_job_matching()
    finance_results = test_finance_job_matching()
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    print("The Universal Domain Matcher successfully:")
    print("1. Detects job domain automatically")
    print("2. Applies domain-specific weighting (like your CNC example)")
    print("3. Prioritizes relevant experience over irrelevant background")
    print("4. Gives appropriate scores across different domains")
    print("5. Maintains conservative evaluation principles")
