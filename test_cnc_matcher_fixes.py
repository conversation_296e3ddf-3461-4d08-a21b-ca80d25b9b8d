#!/usr/bin/env python3
"""
Test the improved CNC matching algorithm with the four key fixes:
1. Count only relevant experience years
2. Add recency bonus/penalty
3. Hard core-skill gate
4. Re-weight for manufacturing roles
"""

from hr_database_working import HRDatabase
from matcher import <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_cnc_matching_fixes():
    """Test the improved CNC matching with fixes"""
    print("=== TESTING IMPROVED CNC MATCHING WITH FIXES ===")
    
    try:
        db = HRDatabase()
        matcher = CVMatcher()
        
        # Get CNC job and CVs
        cnc_job = db.get_job_by_title('C<PERSON> Fräser')
        if not cnc_job:
            print("CNC job not found!")
            return
            
        cvs = db.get_cvs_for_job('C<PERSON> Fräser')
        print(f"Found {len(cvs)} CVs for CNC job")
        print(f"Job type detected: {matcher._detect_job_type(cnc_job.description.lower())}")
        print()
        
        # Calculate scores for each CV
        results = []
        for cv in cvs:
            try:
                # Calculate individual scores
                tf_idf_score = matcher.calculate_tf_idf_similarity(cnc_job.description, cv.content)
                keyword_score = matcher.calculate_keyword_match(cnc_job.description, cv.content)
                skill_score = matcher.calculate_skill_match(cnc_job.description, cv.content)
                
                # Calculate overall score with manufacturing weights (75% skills, 20% experience, 5% education)
                overall_score = (skill_score * 0.6) + (keyword_score * 0.25) + (tf_idf_score * 0.15)
                
                # Test the new relevant experience calculation
                relevant_years = matcher._years_of_relevant_experience(cv.content.lower(), [])
                
                results.append({
                    'name': cv.candidate_name or 'Unknown',
                    'overall': overall_score * 100,
                    'skill': skill_score * 100,
                    'keyword': keyword_score * 100,
                    'tfidf': tf_idf_score * 100,
                    'relevant_years': relevant_years,
                    'has_recent_exp': '2023' in cv.content or '2024' in cv.content
                })
                
            except Exception as e:
                print(f"Error processing CV {cv.candidate_name}: {e}")
        
        # Sort by overall score
        results.sort(key=lambda x: x['overall'], reverse=True)
        
        print("🏆 RANKING RESULTS (with fixes):")
        print("=" * 80)
        for i, result in enumerate(results, 1):
            print(f"#{i} {result['name']:<20} | Overall: {result['overall']:5.1f}% | "
                  f"Skill: {result['skill']:5.1f}% | Keyword: {result['keyword']:5.1f}% | "
                  f"TF-IDF: {result['tfidf']:5.1f}%")
            print(f"    Relevant Years: {result['relevant_years']} | "
                  f"Recent Experience: {'Yes' if result['has_recent_exp'] else 'No'}")
            print()
        
        print("\n🔍 ANALYSIS:")
        print("Expected ranking based on fixes:")
        print("1. Daniel Meixner - Should be #1 (current QS + CNC practice + recent)")
        print("2. Horst Lippert - Should be #2 (CNC course but old experience)")
        print("3. Others - Should rank lower due to lack of core CNC skills")
        
        # Check if Daniel is ranked #1
        if results and 'Daniel' in results[0]['name']:
            print("✅ SUCCESS: Daniel Meixner is correctly ranked #1!")
        else:
            print("❌ ISSUE: Daniel Meixner is not ranked #1")
            
    except Exception as e:
        print(f"Error in test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_cnc_matching_fixes()
