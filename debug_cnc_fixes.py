#!/usr/bin/env python3
"""
Debug the CNC matching fixes to understand why <PERSON> isn't ranking #1
"""

from hr_database_working import HRDatabase
from matcher import CVMatcher
import re

def debug_cnc_fixes():
    """Debug the CNC matching fixes"""
    print("=== DEBUGGING CNC MATCHING FIXES ===")
    
    try:
        db = HRDatabase()
        matcher = CVMatcher()
        
        # Get CNC job and CVs
        cnc_job = db.get_job_by_title('C<PERSON> Fräser')
        cvs = db.get_cvs_for_job('C<PERSON> Fräser')
        
        # Focus on <PERSON> and Horst
        daniel_cv = None
        horst_cv = None
        
        for cv in cvs:
            if 'Daniel' in cv.candidate_name:
                daniel_cv = cv
            elif 'Horst' in cv.candidate_name:
                horst_cv = cv
        
        if not daniel_cv or not horst_cv:
            print("Could not find <PERSON> or Horst CVs")
            return
        
        print("🔍 DEBUGGING DANIEL MEIXNER:")
        print("=" * 50)
        debug_candidate(matcher, cnc_job, daniel_cv)
        
        print("\n🔍 DEBUGGING HORST LIPPERT:")
        print("=" * 50)
        debug_candidate(matcher, cnc_job, horst_cv)
        
    except Exception as e:
        print(f"Error in debug: {e}")
        import traceback
        traceback.print_exc()

def debug_candidate(matcher, job, cv):
    """Debug a specific candidate"""
    print(f"Candidate: {cv.candidate_name}")
    
    # Check for year ranges in CV
    year_pattern = r'(\d{4})-(\d{4}|\bheute\b|\bpresent\b)'
    year_matches = list(re.finditer(year_pattern, cv.content.lower(), re.IGNORECASE))
    print(f"Year ranges found: {len(year_matches)}")
    
    for i, match in enumerate(year_matches[:3]):  # Show first 3
        start_pos = max(0, match.start() - 100)
        end_pos = min(len(cv.content), match.end() + 100)
        context = cv.content[start_pos:end_pos].replace('\n', ' ')
        print(f"  {i+1}. {match.group()} - Context: ...{context}...")
    
    # Check for CNC skills
    core_cnc_skills = ['cnc', 'fanuc', 'heidenhain', 'machining', 'fräsen', 'drehen', 'programmieren']
    found_skills = []
    for skill in core_cnc_skills:
        if skill in cv.content.lower():
            found_skills.append(skill)
    print(f"CNC skills found: {found_skills}")
    
    # Check for recent experience
    has_recent = '2023' in cv.content or '2024' in cv.content
    print(f"Has recent experience (2023/2024): {has_recent}")
    
    # Calculate relevant years
    relevant_years = matcher._years_of_relevant_experience(cv.content.lower(), [])
    print(f"Relevant years calculated: {relevant_years}")
    
    # Calculate skill score
    skill_score = matcher.calculate_skill_match(job.description, cv.content)
    print(f"Skill score: {skill_score:.3f}")
    
    # Show CV content preview
    print(f"CV content preview (first 300 chars):")
    print(f"  {cv.content[:300]}...")
    print()

if __name__ == "__main__":
    debug_cnc_fixes()
