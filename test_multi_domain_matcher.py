#!/usr/bin/env python3
"""
Test the improved matcher across different job types and domains
"""

def test_multi_domain_matcher():
    """Test matcher across different domains"""
    print("=== TESTING MULTI-DOMAIN MATCHER ===")
    
    # Test CNC job (we know this works)
    print("\n🔧 TESTING CNC/MANUFACTURING DOMAIN:")
    print("-" * 50)
    
    cnc_job = """
    CNC Fräser (m/w/d)
    Anforderungen: CNC-Programmierung, Fanuc Steuerung, Zerspanung
    """
    
    good_cnc_cv = """
    Berufserfahrung:
    2020-2024: CNC-Programmierer bei Firma ABC
    - CNC-Programmierung von Frästeilen
    - Fanuc Steuerung, Rüsten und Einfahren
    Ausbildung: Zerspanungsmechaniker
    """
    
    fake_cnc_cv = """
    CNC CNC CNC Fanuc Heidenhain Programmierung
    Ich kenne CNC sehr gut. CNC ist toll.
    Berufserfahrung: 2020-2024: Verkäufer
    """
    
    # Test Software job
    print("\n💻 TESTING SOFTWARE DEVELOPMENT DOMAIN:")
    print("-" * 50)
    
    software_job = """
    Senior Java Developer (m/w/d)
    Anforderungen: Java, Spring Boot, REST API, Informatik Studium
    """
    
    good_software_cv = """
    Berufserfahrung:
    2019-2024: Senior Java Developer bei Tech Firma
    - Java und Spring Boot Entwicklung
    - REST API Design und Implementierung
    Ausbildung: Bachelor Informatik
    """
    
    fake_software_cv = """
    Java Python JavaScript React Angular Spring Boot
    Ich kann alles: Java, Python, JavaScript, React
    Berufserfahrung: 2022-2024: Praktikant
    """
    
    # Test Generic job
    print("\n📈 TESTING GENERIC/SALES DOMAIN:")
    print("-" * 50)
    
    sales_job = """
    Vertriebsmitarbeiter (m/w/d)
    Anforderungen: Verkaufserfahrung, Kundenbetreuung
    """
    
    good_sales_cv = """
    Berufserfahrung:
    2018-2024: Vertriebsmitarbeiter bei Firma XYZ
    - Kundenakquise und -betreuung
    - Verkaufsgespräche und Abschlüsse
    Ausbildung: Kaufmann im Einzelhandel
    """
    
    entry_sales_cv = """
    Berufserfahrung:
    2023-2024: Aushilfe im Einzelhandel
    - Kundenberatung, Kassentätigkeit
    Ausbildung: Abitur
    """
    
    try:
        from matcher import CVMatcher
        matcher = CVMatcher()
        
        # Test each domain
        test_cases = [
            ("CNC/Manufacturing", cnc_job, [("Good CNC", good_cnc_cv), ("Fake CNC", fake_cnc_cv)]),
            ("Software Development", software_job, [("Good Software", good_software_cv), ("Fake Software", fake_software_cv)]),
            ("Sales/Generic", sales_job, [("Experienced Sales", good_sales_cv), ("Entry Level", entry_sales_cv)])
        ]
        
        for domain, job_desc, candidates in test_cases:
            print(f"\n🎯 {domain}:")
            job_type = matcher._detect_job_type(job_desc.lower())
            print(f"   Detected job type: {job_type}")
            
            results = []
            for name, cv in candidates:
                skill_score = matcher.calculate_skill_match(job_desc, cv)
                experience_analysis = matcher._validate_experience_context(cv.lower(), job_type)
                
                results.append({
                    'name': name,
                    'skill_score': skill_score * 100,
                    'experience_quality': experience_analysis['experience_quality'],
                    'relevant_years': experience_analysis['relevant_years']
                })
            
            for result in results:
                print(f"   {result['name']:<20}: Skill {result['skill_score']:5.1f}% | "
                      f"Quality {result['experience_quality']:.2f} | Years {result['relevant_years']}")
            
            # Check ranking
            if results[0]['skill_score'] > results[1]['skill_score']:
                print("   ✅ Good candidate ranked higher!")
            else:
                print("   ❌ Issue with ranking!")
        
        print(f"\n🎉 UNIVERSAL MATCHER TEST COMPLETE!")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_multi_domain_matcher()
