#!/usr/bin/env python3
"""
Comprehensive Performance Test for Your New Parser and Extractor
Tests the updated bilingual_cv_extractor_patched.py to evaluate performance
"""

import os
import glob
import time
from typing import Dict, List
from bilingual_cv_extractor_patched import BilingualCVExtractorPatched

class NewParserPerformanceTest:
    def __init__(self):
        self.extractor = BilingualCVExtractorPatched()
        self.cv_files = self._find_cv_files()
        
    def _find_cv_files(self) -> List[str]:
        """Find all CV files in uploads directory"""
        cv_files = []
        uploads_dir = "uploads"
        
        if os.path.exists(uploads_dir):
            pdf_files = glob.glob(os.path.join(uploads_dir, "*.pdf"))
            cv_files.extend(pdf_files)
            docx_files = glob.glob(os.path.join(uploads_dir, "*.docx"))
            cv_files.extend(docx_files)
        
        return cv_files
    
    def test_extraction_accuracy(self):
        """Test extraction accuracy across all CV files"""
        print("🔍 TESTING YOUR NEW PARSER - EXTRACTION ACCURACY")
        print("=" * 70)
        print(f"📁 Found {len(self.cv_files)} CV files to test")
        print()
        
        results = {
            'successful_extractions': 0,
            'failed_extractions': 0,
            'field_success_rates': {
                'name': 0, 'email': 0, 'phone': 0, 
                'experience': 0, 'skills': 0, 'education': 0
            },
            'detailed_results': []
        }
        
        for i, cv_file in enumerate(self.cv_files, 1):
            filename = os.path.basename(cv_file)
            print(f"📄 {i}. Testing: {filename}")
            print("-" * 50)
            
            try:
                # Extract all fields
                start_time = time.time()
                result = self.extractor.extract_cv_data(cv_file, 
                    ['name', 'email', 'phone', 'experience', 'skills', 'education', 'seniority'])
                extraction_time = time.time() - start_time
                
                if 'error' in result:
                    print(f"   ❌ Error: {result['error']}")
                    results['failed_extractions'] += 1
                    continue
                
                results['successful_extractions'] += 1
                
                # Display results
                print(f"   👤 Name: '{result.get('name', 'N/A')}'")
                print(f"   📧 Email: '{result.get('email', 'N/A')}'")
                print(f"   📞 Phone: '{result.get('phone', 'N/A')}'")
                print(f"   💼 Experience: '{result.get('experience', 'N/A')[:100]}{'...' if len(result.get('experience', '')) > 100 else ''}'")
                print(f"   🛠️  Skills: '{result.get('skills', 'N/A')[:100]}{'...' if len(result.get('skills', '')) > 100 else ''}'")
                print(f"   🎓 Education: '{result.get('education', 'N/A')[:100]}{'...' if len(result.get('education', '')) > 100 else ''}'")
                print(f"   📊 Seniority: '{result.get('seniority', 'N/A')}'")
                print(f"   ⏱️  Time: {extraction_time:.3f}s")
                
                # Count successful field extractions
                for field in ['name', 'email', 'phone', 'experience', 'skills', 'education']:
                    value = result.get(field, '')
                    if value and value not in ['', 'N/A', 'nicht spezifiziert', 'not specified', 'R']:
                        results['field_success_rates'][field] += 1
                
                # Analyze quality
                quality_score = self._analyze_extraction_quality(result)
                print(f"   🏆 Quality Score: {quality_score}/10")
                
                results['detailed_results'].append({
                    'file': filename,
                    'result': result,
                    'time': extraction_time,
                    'quality': quality_score
                })
                
            except Exception as e:
                print(f"   ❌ Exception: {e}")
                results['failed_extractions'] += 1
            
            print()
        
        return results
    
    def _analyze_extraction_quality(self, result: Dict[str, str]) -> int:
        """Analyze the quality of extraction results (0-10 scale)"""
        score = 0
        
        # Name quality (0-2 points)
        name = result.get('name', '')
        if name and len(name.split()) >= 2 and not any(bad in name.lower() for bad in ['lebenslauf', 'bewerbung']):
            score += 2
        elif name:
            score += 1
        
        # Email quality (0-1 point)
        email = result.get('email', '')
        if email and '@' in email and '.' in email:
            score += 1
        
        # Phone quality (0-1 point)
        phone = result.get('phone', '')
        if phone and ('+49' in phone or phone.startswith('0')):
            score += 1
        
        # Experience quality (0-3 points)
        experience = result.get('experience', '')
        if experience and experience not in ['Berufserfahrung nicht spezifiziert', 'Experience not specified']:
            if 'Jahre' in experience or any(word in experience.lower() for word in ['bei', 'company', 'entwickler', 'manager']):
                score += 3
            elif len(experience) > 20:
                score += 2
            else:
                score += 1
        
        # Skills quality (0-2 points)
        skills = result.get('skills', '')
        if skills and skills not in ['Fähigkeiten nicht spezifiziert', 'Skills not specified', 'R']:
            skill_count = len(skills.split(','))
            if skill_count >= 5:
                score += 2
            elif skill_count >= 2:
                score += 1
        
        # Education quality (0-1 point)
        education = result.get('education', '')
        if education and education not in ['Ausbildung nicht spezifiziert', 'Education not specified']:
            score += 1
        
        return score
    
    def test_german_specific_features(self):
        """Test German-specific extraction features"""
        print("🇩🇪 TESTING GERMAN-SPECIFIC FEATURES")
        print("=" * 70)
        
        # Test German experience patterns
        german_test_cases = [
            ("5 Jahre Berufserfahrung in der Softwareentwicklung", "experience"),
            ("2018-2023: Senior Java Entwickler bei SAP AG", "experience"),
            ("Programmierung mit Python, Java und JavaScript", "skills"),
            ("Fähigkeiten: Docker, Kubernetes, DevOps", "skills"),
            ("Bachelor of Science Informatik", "education"),
            ("Studium der Informatik an der TU München", "education"),
        ]
        
        print("📊 German Pattern Recognition Test:")
        for i, (test_text, field_type) in enumerate(german_test_cases, 1):
            print(f"\n{i}. Testing {field_type}: '{test_text}'")
            
            if field_type == "experience":
                result = self.extractor._extract_experience(test_text)
            elif field_type == "skills":
                result = self.extractor._extract_skills(test_text)
            elif field_type == "education":
                result = self.extractor._extract_education(test_text)
            
            print(f"   Result: '{result}'")
            
            # Evaluate result
            if result and result not in ['nicht spezifiziert', 'not specified']:
                print(f"   Status: ✅ PASS")
            else:
                print(f"   Status: ❌ FAIL")
    
    def test_performance_benchmarks(self):
        """Test performance benchmarks"""
        print("⚡ PERFORMANCE BENCHMARKS")
        print("=" * 70)
        
        if not self.cv_files:
            print("❌ No CV files found for performance testing")
            return
        
        # Test extraction speed
        total_time = 0
        successful_extractions = 0
        
        print("📊 Speed Test (first 5 CVs):")
        for i, cv_file in enumerate(self.cv_files[:5], 1):
            filename = os.path.basename(cv_file)
            
            try:
                start_time = time.time()
                result = self.extractor.extract_cv_data(cv_file)
                end_time = time.time()
                
                extraction_time = end_time - start_time
                total_time += extraction_time
                successful_extractions += 1
                
                print(f"   {i}. {filename}: {extraction_time:.3f}s")
                
            except Exception as e:
                print(f"   {i}. {filename}: ❌ Error - {e}")
        
        if successful_extractions > 0:
            avg_time = total_time / successful_extractions
            print(f"\n📈 Performance Summary:")
            print(f"   Average extraction time: {avg_time:.3f}s")
            print(f"   Total time for {successful_extractions} CVs: {total_time:.3f}s")
            print(f"   Throughput: {successful_extractions/total_time:.1f} CVs/second")
    
    def generate_performance_report(self, results: Dict):
        """Generate comprehensive performance report"""
        print("📋 COMPREHENSIVE PERFORMANCE REPORT")
        print("=" * 70)
        
        total_files = len(self.cv_files)
        successful = results['successful_extractions']
        failed = results['failed_extractions']
        
        print(f"📊 Overall Statistics:")
        print(f"   Total CV files tested: {total_files}")
        print(f"   Successful extractions: {successful}")
        print(f"   Failed extractions: {failed}")
        print(f"   Success rate: {(successful/total_files)*100:.1f}%")
        print()
        
        print(f"📈 Field Extraction Success Rates:")
        for field, count in results['field_success_rates'].items():
            rate = (count / successful) * 100 if successful > 0 else 0
            print(f"   {field.capitalize()}: {count}/{successful} ({rate:.1f}%)")
        print()
        
        if results['detailed_results']:
            # Calculate average quality score
            quality_scores = [r['quality'] for r in results['detailed_results']]
            avg_quality = sum(quality_scores) / len(quality_scores)
            
            # Calculate average extraction time
            extraction_times = [r['time'] for r in results['detailed_results']]
            avg_time = sum(extraction_times) / len(extraction_times)
            
            print(f"🏆 Quality Metrics:")
            print(f"   Average quality score: {avg_quality:.1f}/10")
            print(f"   Average extraction time: {avg_time:.3f}s")
            print()
            
            # Top performing files
            top_files = sorted(results['detailed_results'], key=lambda x: x['quality'], reverse=True)[:3]
            print(f"🥇 Top Performing Extractions:")
            for i, file_result in enumerate(top_files, 1):
                print(f"   {i}. {file_result['file']}: {file_result['quality']}/10")
    
    def run_comprehensive_test(self):
        """Run all performance tests"""
        print("🚀 COMPREHENSIVE PERFORMANCE TEST FOR YOUR NEW PARSER")
        print("=" * 80)
        print(f"📅 Test Date: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        start_time = time.time()
        
        # Run extraction accuracy test
        results = self.test_extraction_accuracy()
        print("\n" + "="*80 + "\n")
        
        # Run German-specific feature tests
        self.test_german_specific_features()
        print("\n" + "="*80 + "\n")
        
        # Run performance benchmarks
        self.test_performance_benchmarks()
        print("\n" + "="*80 + "\n")
        
        # Generate final report
        self.generate_performance_report(results)
        
        total_time = time.time() - start_time
        print(f"\n✅ COMPREHENSIVE TESTING COMPLETED!")
        print(f"⏱️  Total test time: {total_time:.2f} seconds")
        print("=" * 80)

def main():
    """Run comprehensive performance test"""
    test_suite = NewParserPerformanceTest()
    test_suite.run_comprehensive_test()

if __name__ == "__main__":
    main()
