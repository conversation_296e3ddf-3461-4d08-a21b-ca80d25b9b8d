"""
Configuration for Bilingual HR Management System
"""

import os
from typing import Dict, List


class BilingualConfig:
    """Configuration class for bilingual HR management system"""
    
    # Language settings
    DEFAULT_LANGUAGE = 'en'
    SUPPORTED_LANGUAGES = ['en', 'de']
    LANGUAGE_NAMES = {
        'en': 'English',
        'de': 'Deutsch'
    }
    
    # NLP Model settings
    SPACY_MODELS = {
        'en': 'en_core_web_sm',
        'de': 'de_core_news_sm'
    }
    
    # Language detection settings
    LANGUAGE_DETECTION_MIN_CONFIDENCE = 0.6
    BILINGUAL_THRESHOLD = 0.4  # Threshold for considering text as bilingual
    
    # Matching algorithm weights
    MATCHING_WEIGHTS = {
        'tf_idf': 0.35,
        'keyword': 0.25,
        'skill': 0.35,
        'language_bonus': 0.05
    }
    
    # Skill matching weights
    SKILL_WEIGHTS = {
        'technical': 0.7,
        'soft': 0.3
    }
    
    # Translation settings
    ENABLE_AUTO_TRANSLATION = True
    TRANSLATION_CACHE_SIZE = 1000
    TRANSLATION_TIMEOUT = 30  # seconds
    
    # File processing settings
    SUPPORTED_FILE_TYPES = ['.pdf', '.docx', '.doc']
    MAX_FILE_SIZE = 16 * 1024 * 1024  # 16MB
    
    # Database settings for language support
    DEFAULT_CV_LANGUAGE = 'auto'  # auto-detect
    DEFAULT_JOB_LANGUAGE = 'en'
    
    # UI settings
    ENABLE_LANGUAGE_SWITCHER = True
    REMEMBER_LANGUAGE_PREFERENCE = True
    
    # Performance settings
    ENABLE_CACHING = True
    CACHE_EXPIRY_HOURS = 24
    
    # Logging settings
    LOG_LANGUAGE_DETECTION = True
    LOG_TRANSLATION_REQUESTS = True
    LOG_MATCHING_DETAILS = True


class LanguageConfig:
    """Language-specific configuration"""
    
    @staticmethod
    def get_language_config(language: str) -> Dict:
        """Get configuration for specific language"""
        configs = {
            'en': {
                'name': 'English',
                'code': 'en',
                'spacy_model': 'en_core_web_sm',
                'date_format': '%Y-%m-%d',
                'datetime_format': '%Y-%m-%d %H:%M:%S',
                'currency_symbol': '$',
                'decimal_separator': '.',
                'thousand_separator': ',',
                'rtl': False,
                'stop_words_file': 'english_stopwords.txt',
                'skills_file': 'english_skills.json'
            },
            'de': {
                'name': 'Deutsch',
                'code': 'de',
                'spacy_model': 'de_core_news_sm',
                'date_format': '%d.%m.%Y',
                'datetime_format': '%d.%m.%Y %H:%M:%S',
                'currency_symbol': '€',
                'decimal_separator': ',',
                'thousand_separator': '.',
                'rtl': False,
                'stop_words_file': 'german_stopwords.txt',
                'skills_file': 'german_skills.json'
            }
        }
        
        return configs.get(language, configs['en'])


class MatchingConfig:
    """Configuration for CV matching algorithms"""
    
    # Similarity thresholds
    EXCELLENT_MATCH_THRESHOLD = 0.8
    GOOD_MATCH_THRESHOLD = 0.6
    MODERATE_MATCH_THRESHOLD = 0.4
    POOR_MATCH_THRESHOLD = 0.2
    
    # Language compatibility bonuses
    LANGUAGE_BONUSES = {
        'perfect_match': 0.1,      # Same language, high confidence
        'bilingual_content': 0.05,  # Mixed/bilingual content
        'different_languages': -0.02  # Different languages
    }
    
    # Skill matching parameters
    MIN_SKILLS_FOR_BONUS = 3
    TECHNICAL_SKILL_WEIGHT = 1.5
    SOFT_SKILL_WEIGHT = 1.0
    LANGUAGE_SKILL_WEIGHT = 1.2
    
    # Text preprocessing parameters
    MIN_WORD_LENGTH = 3
    MAX_STOP_WORDS_RATIO = 0.7
    ENABLE_STEMMING = True
    ENABLE_LEMMATIZATION = True


class ExtractionConfig:
    """Configuration for CV data extraction"""
    
    # Field extraction settings
    EXTRACTION_FIELDS = [
        'name', 'email', 'phone', 'experience', 
        'skills', 'education', 'languages'
    ]
    
    # Pattern matching settings
    NAME_PATTERNS = {
        'en': [
            r'\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)+)\b',
            r'Name:\s*([A-Z][a-z]+(?:\s+[A-Z][a-z]+)+)',
            r'Full Name:\s*([A-Z][a-z]+(?:\s+[A-Z][a-z]+)+)'
        ],
        'de': [
            r'\b([A-ZÄÖÜ][a-zäöüß]+(?:\s+[A-ZÄÖÜ][a-zäöüß]+)+)\b',
            r'Name:\s*([A-ZÄÖÜ][a-zäöüß]+(?:\s+[A-ZÄÖÜ][a-zäöüß]+)+)',
            r'Vor-?\s*und\s*Nachname:\s*([A-ZÄÖÜ][a-zäöüß]+(?:\s+[A-ZÄÖÜ][a-zäöüß]+)+)'
        ]
    }
    
    # Experience extraction patterns
    EXPERIENCE_PATTERNS = {
        'en': [
            r'(\d+)\+?\s*years?\s*(?:of\s*)?experience',
            r'(\d+)-(\d+)\s*years?\s*(?:of\s*)?experience',
            r'experience\s*:?\s*(\d+)\+?\s*years?'
        ],
        'de': [
            r'(\d+)\+?\s*Jahre?\s*(?:Berufs)?(?:erfahrung|tätigkeit)',
            r'(\d+)-(\d+)\s*Jahre?\s*(?:Berufs)?(?:erfahrung|tätigkeit)',
            r'(?:Berufs)?(?:erfahrung|tätigkeit)\s*:?\s*(\d+)\+?\s*Jahre?'
        ]
    }
    
    # Phone number patterns
    PHONE_PATTERNS = {
        'international': r'\+\d{1,4}[\s\-]?\d{2,4}[\s\-]?\d{3,8}',
        'us': r'\(\d{3}\)\s?\d{3}[\s\-]?\d{4}',
        'de': r'0\d{2,4}[\s\-/]?\d{3,8}',
        'generic': r'\d{3,4}[\s\-]\d{3,8}'
    }


class UIConfig:
    """UI and template configuration"""
    
    # Theme settings
    DEFAULT_THEME = 'light'
    AVAILABLE_THEMES = ['light', 'dark']
    
    # Language switcher settings
    LANGUAGE_SWITCHER_POSITION = 'top-right'
    SHOW_LANGUAGE_FLAGS = True
    
    # Table and list settings
    DEFAULT_PAGE_SIZE = 20
    MAX_PAGE_SIZE = 100
    
    # File upload settings
    ALLOWED_EXTENSIONS = {'pdf', 'docx', 'doc'}
    MAX_FILENAME_LENGTH = 255
    
    # Display formats
    DATE_DISPLAY_FORMAT = {
        'en': '%B %d, %Y',
        'de': '%d. %B %Y'
    }
    
    DATETIME_DISPLAY_FORMAT = {
        'en': '%B %d, %Y at %I:%M %p',
        'de': '%d. %B %Y um %H:%M'
    }


# Environment-specific settings
class EnvironmentConfig:
    """Environment-specific configuration"""
    
    @staticmethod
    def get_config():
        """Get configuration based on environment"""
        env = os.getenv('FLASK_ENV', 'development')
        
        if env == 'production':
            return ProductionConfig()
        elif env == 'testing':
            return TestingConfig()
        else:
            return DevelopmentConfig()


class DevelopmentConfig:
    """Development environment configuration"""
    DEBUG = True
    TESTING = False
    ENABLE_TRANSLATION_LOGGING = True
    CACHE_TRANSLATIONS = False
    USE_MOCK_TRANSLATION = False


class ProductionConfig:
    """Production environment configuration"""
    DEBUG = False
    TESTING = False
    ENABLE_TRANSLATION_LOGGING = False
    CACHE_TRANSLATIONS = True
    USE_MOCK_TRANSLATION = False


class TestingConfig:
    """Testing environment configuration"""
    DEBUG = True
    TESTING = True
    ENABLE_TRANSLATION_LOGGING = False
    CACHE_TRANSLATIONS = False
    USE_MOCK_TRANSLATION = True


# Utility functions
def get_supported_languages() -> List[str]:
    """Get list of supported languages"""
    return BilingualConfig.SUPPORTED_LANGUAGES


def get_language_name(language_code: str) -> str:
    """Get display name for language code"""
    return BilingualConfig.LANGUAGE_NAMES.get(language_code, language_code)


def get_spacy_model(language: str) -> str:
    """Get spaCy model name for language"""
    return BilingualConfig.SPACY_MODELS.get(language, BilingualConfig.SPACY_MODELS['en'])


def is_language_supported(language: str) -> bool:
    """Check if language is supported"""
    return language in BilingualConfig.SUPPORTED_LANGUAGES


def get_matching_weights() -> Dict[str, float]:
    """Get matching algorithm weights"""
    return BilingualConfig.MATCHING_WEIGHTS.copy()


def get_extraction_fields() -> List[str]:
    """Get list of fields to extract from CVs"""
    return ExtractionConfig.EXTRACTION_FIELDS.copy()
