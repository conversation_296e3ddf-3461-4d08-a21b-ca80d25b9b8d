#!/usr/bin/env python3
"""
Test Application Integration with Hybrid Parser
Verifies that the app is using the hybrid parser correctly
"""

import os
import sys
import tempfile
from pathlib import Path

# Add current directory to path to import app modules
sys.path.insert(0, os.getcwd())

def test_hybrid_parser_integration():
    """Test that the hybrid parser is properly integrated into the app"""
    print("🔍 TESTING HYBRID PARSER INTEGRATION")
    print("=" * 60)
    
    try:
        # Import the hybrid parser
        from hybrid_german_cv_parser import HybridGermanCVParser
        print("✅ Hybrid parser import successful")
        
        # Test parser initialization
        parser = HybridGermanCVParser()
        print("✅ Hybrid parser initialization successful")
        
        # Test parser info
        info = parser.get_parser_info()
        print(f"✅ Parser: {info['name']} v{info['version']}")
        print(f"✅ spaCy Available: {info['spacy_available']}")
        
        # Test compatibility method
        if hasattr(parser, 'extract_cv_data_bilingual'):
            print("✅ Bilingual compatibility method exists")
        else:
            print("❌ Missing bilingual compatibility method")
            return False
        
        # Test with a sample CV file
        cv_files = []
        uploads_dir = "uploads"
        if os.path.exists(uploads_dir):
            import glob
            cv_files = glob.glob(os.path.join(uploads_dir, "*.pdf"))
        
        if cv_files:
            test_cv = cv_files[0]
            print(f"📄 Testing with: {os.path.basename(test_cv)}")
            
            # Test the bilingual method (as used by app.py)
            result = parser.extract_cv_data_bilingual(
                test_cv, ['name', 'email', 'phone', 'experience', 'skills', 'education']
            )
            
            print("✅ Bilingual extraction successful")
            print(f"   Name: '{result.get('name', 'N/A')}'")
            print(f"   Email: '{result.get('email', 'N/A')}'")
            print(f"   Experience: '{result.get('experience', 'N/A')[:50]}{'...' if len(result.get('experience', '')) > 50 else ''}'")
            print(f"   Skills: '{result.get('skills', 'N/A')[:50]}{'...' if len(result.get('skills', '')) > 50 else ''}'")
            
        else:
            print("⚠️  No CV files found for testing")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_app_imports():
    """Test that app.py can import all required modules"""
    print("\n🔧 TESTING APP.PY IMPORTS")
    print("=" * 60)
    
    try:
        # Test individual imports that app.py uses
        from hybrid_german_cv_parser import HybridGermanCVParser
        print("✅ HybridGermanCVParser import successful")
        
        from bilingual_cv_extractor import BilingualCVExtractor
        print("✅ BilingualCVExtractor import successful")
        
        from bilingual_matcher import BilingualCVMatcher
        print("✅ BilingualCVMatcher import successful")
        
        from language_detector import LanguageDetector
        print("✅ LanguageDetector import successful")
        
        from translation_service import TranslationService
        print("✅ TranslationService import successful")
        
        # Test initialization as done in app.py
        bilingual_extractor = HybridGermanCVParser()
        print("✅ Hybrid parser initialization successful")
        
        # Test the exact method call used in app.py line 478-480
        if hasattr(bilingual_extractor, 'extract_cv_data_bilingual'):
            print("✅ extract_cv_data_bilingual method exists")
        else:
            print("❌ extract_cv_data_bilingual method missing")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all required modules are available")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_parser_performance():
    """Test parser performance with real CV"""
    print("\n⚡ TESTING PARSER PERFORMANCE")
    print("=" * 60)
    
    try:
        from hybrid_german_cv_parser import HybridGermanCVParser
        import time
        import glob
        
        parser = HybridGermanCVParser()
        
        # Find CV files
        cv_files = []
        uploads_dir = "uploads"
        if os.path.exists(uploads_dir):
            cv_files = glob.glob(os.path.join(uploads_dir, "*.pdf"))
        
        if not cv_files:
            print("⚠️  No CV files found for performance testing")
            return True
        
        # Test with first 3 CVs
        for i, cv_file in enumerate(cv_files[:3], 1):
            filename = os.path.basename(cv_file)
            print(f"📄 {i}. Testing: {filename}")
            
            start_time = time.time()
            result = parser.extract_cv_data_bilingual(
                cv_file, ['name', 'email', 'phone', 'experience', 'skills', 'education']
            )
            end_time = time.time()
            
            extraction_time = end_time - start_time
            
            if 'error' in result:
                print(f"   ❌ Error: {result['error']}")
            else:
                print(f"   ✅ Success in {extraction_time:.3f}s")
                print(f"      Name: '{result.get('name', 'N/A')}'")
                print(f"      Fields extracted: {len([k for k, v in result.items() if v])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run all integration tests"""
    print("🚀 HYBRID PARSER APPLICATION INTEGRATION TEST")
    print("=" * 80)
    
    all_passed = True
    
    # Test hybrid parser integration
    if not test_hybrid_parser_integration():
        all_passed = False
    
    # Test app imports
    if not test_app_imports():
        all_passed = False
    
    # Test parser performance
    if not test_parser_performance():
        all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 ALL INTEGRATION TESTS PASSED!")
        print("✅ The hybrid parser is properly integrated into the application")
        print("✅ App.py should now use the hybrid parser for CV extraction")
        print("\n💡 To verify in the running app:")
        print("   1. Upload a German CV")
        print("   2. Check the extracted data quality")
        print("   3. Export CV data to Excel to see improved extraction")
    else:
        print("❌ SOME INTEGRATION TESTS FAILED!")
        print("⚠️  The hybrid parser may not be properly integrated")
        print("\n🔧 Check the error messages above and fix any issues")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
