#!/usr/bin/env python3
"""
German CV Specific Testing
Focus on German language CV parsing and matching accuracy
"""

import os
from matcher import CVMatcher
from cv_extractor import CVDataExtractor

def test_german_skill_detection():
    """Test German skill detection and synonym matching"""
    print("🇩🇪 GERMAN SKILL DETECTION TEST")
    print("=" * 40)
    
    matcher = CVMatcher()
    
    # German CV with various German technical terms
    german_cv = """
    Anna Müller - Softwareentwicklerin
    
    Berufserfahrung:
    • 5 Jahre Berufserfahrung in der Softwareentwicklung
    • Entwicklung von Webanwendungen mit Java und Spring Boot
    • Arbeit mit PostgreSQL-Datenbanken und relationalen Datenbanken
    • Entwicklung von REST-Services und REST-APIs
    • Nutzung von Git und Jenkins für CI/CD-Prozesse
    • Arbeit in einem agilen Scrum-Team
    • Kenntnisse in Docker und Kubernetes
    
    Ausbildung:
    • Master of Science Informatik
    • Abgeschlossenes Studium der Informatik
    
    Technische Kenntnisse:
    • Programmiersprachen: Java, Python
    • Frameworks: Spring Boot, Spring Framework
    • Datenbanken: PostgreSQL, MySQL
    • Tools: Git, Jenkins, Docker
    • Methoden: Scrum, agile Methoden
    """
    
    # German job description
    german_job = """
    Java Entwickler (m/w/d)
    
    Anforderungen:
    • Mindestens 3 Jahre Berufserfahrung
    • Java-Programmierung
    • Spring Boot Framework
    • PostgreSQL Datenbank-Kenntnisse
    • REST-API Entwicklung
    • Git und Jenkins
    • Scrum-Methodik
    • Informatik-Studium
    """
    
    # Test skill matching
    skill_score = matcher.calculate_skill_match(german_job, german_cv)
    keyword_score = matcher.calculate_keyword_match(german_job, german_cv)
    tf_idf_score = matcher.calculate_tf_idf_similarity(german_job, german_cv)
    overall_score = (skill_score * 0.5) + (keyword_score * 0.3) + (tf_idf_score * 0.2)
    
    print(f"German CV vs German Job:")
    print(f"   Overall Score: {overall_score*100:.1f}%")
    print(f"   Skill Score: {skill_score*100:.1f}%")
    print(f"   Keyword Score: {keyword_score*100:.1f}%")
    print(f"   Content Score: {tf_idf_score*100:.1f}%")
    
    # Test with English equivalent
    english_job = """
    Java Developer Position
    
    Requirements:
    • 3+ years experience
    • Java programming
    • Spring Boot framework
    • PostgreSQL database
    • REST API development
    • Git and Jenkins
    • Scrum methodology
    • Computer Science degree
    """
    
    skill_score_en = matcher.calculate_skill_match(english_job, german_cv)
    keyword_score_en = matcher.calculate_keyword_match(english_job, german_cv)
    tf_idf_score_en = matcher.calculate_tf_idf_similarity(english_job, german_cv)
    overall_score_en = (skill_score_en * 0.5) + (keyword_score_en * 0.3) + (tf_idf_score_en * 0.2)
    
    print(f"\nGerman CV vs English Job:")
    print(f"   Overall Score: {overall_score_en*100:.1f}%")
    print(f"   Skill Score: {skill_score_en*100:.1f}%")
    print(f"   Keyword Score: {keyword_score_en*100:.1f}%")
    print(f"   Content Score: {tf_idf_score_en*100:.1f}%")
    
    # Analysis
    if overall_score >= overall_score_en:
        print(f"\n✅ German CV scores better with German job ({overall_score*100:.1f}% vs {overall_score_en*100:.1f}%)")
    else:
        print(f"\n⚠️  German CV scores better with English job ({overall_score_en*100:.1f}% vs {overall_score*100:.1f}%)")

def test_german_experience_parsing():
    """Test German experience parsing"""
    print(f"\n📅 GERMAN EXPERIENCE PARSING TEST")
    print("-" * 35)
    
    matcher = CVMatcher()
    
    # Test various German experience formats
    experience_formats = [
        "5 Jahre Berufserfahrung in der Softwareentwicklung",
        "Mindestens 3 Jahre Erfahrung mit Java",
        "2018-heute: Senior Java Entwickler",
        "2020-2023: Softwareentwickler",
        "Berufserfahrung: 4 Jahre",
        "6+ Jahre Erfahrung in der Programmierung"
    ]
    
    job_requiring_experience = """
    Senior Java Entwickler
    Mindestens 3 Jahre Berufserfahrung erforderlich
    """
    
    print("Testing experience detection:")
    for exp_format in experience_formats:
        cv_content = f"Max Mustermann\nSoftwareentwickler\n{exp_format}\nJava, Spring Boot"
        skill_score = matcher.calculate_skill_match(job_requiring_experience, cv_content)
        print(f"   '{exp_format}' -> Skill Score: {skill_score*100:.1f}%")

def test_german_education_parsing():
    """Test German education parsing"""
    print(f"\n🎓 GERMAN EDUCATION PARSING TEST")
    print("-" * 35)
    
    matcher = CVMatcher()
    
    # Test various German education formats
    education_formats = [
        "Bachelor of Science Informatik",
        "Master Informatik",
        "Diplom-Informatiker",
        "Studium der Informatik",
        "Abgeschlossenes Studium der Informatik",
        "B.Sc. Informatik",
        "M.Sc. Informatik",
        "Informatikstudium",
        "Fachinformatiker für Anwendungsentwicklung"
    ]
    
    job_requiring_education = """
    Java Entwickler
    Abgeschlossenes Studium der Informatik oder vergleichbare Qualifikation
    """
    
    print("Testing education detection:")
    for edu_format in education_formats:
        cv_content = f"Anna Schmidt\nSoftwareentwicklerin\n{edu_format}\nJava, Spring Boot"
        skill_score = matcher.calculate_skill_match(job_requiring_education, cv_content)
        print(f"   '{edu_format}' -> Skill Score: {skill_score*100:.1f}%")

def test_mixed_language_cv():
    """Test CV with mixed German and English content"""
    print(f"\n🌐 MIXED LANGUAGE CV TEST")
    print("-" * 30)
    
    matcher = CVMatcher()
    
    # Mixed language CV
    mixed_cv = """
    Maria Schmidt - Software Developer / Softwareentwicklerin
    
    Professional Experience / Berufserfahrung:
    • Senior Java Developer bei TechCorp (2020-heute)
    • Entwicklung von web applications mit Java und Spring Boot
    • Working with PostgreSQL databases und REST APIs
    • Agile development in Scrum teams
    • Git, Jenkins, CI/CD pipelines
    
    Education / Ausbildung:
    • Master of Science in Computer Science / Informatik
    • Bachelor Informatik
    
    Skills / Kenntnisse:
    • Programming languages: Java, Python, JavaScript
    • Frameworks: Spring Boot, React
    • Databases: PostgreSQL, MySQL
    • Tools: Git, Docker, Kubernetes
    """
    
    # Test with German job
    german_job = """
    Senior Java Entwickler (m/w/d)
    Mindestens 3 Jahre Berufserfahrung
    Java, Spring Boot, PostgreSQL
    Scrum, Git, Jenkins
    Informatik-Studium
    """
    
    # Test with English job
    english_job = """
    Senior Java Developer
    3+ years experience
    Java, Spring Boot, PostgreSQL
    Scrum, Git, Jenkins
    Computer Science degree
    """
    
    # Calculate scores
    german_score = matcher.calculate_skill_match(german_job, mixed_cv)
    english_score = matcher.calculate_skill_match(english_job, mixed_cv)
    
    print(f"Mixed language CV scores:")
    print(f"   German job: {german_score*100:.1f}%")
    print(f"   English job: {english_score*100:.1f}%")
    
    if abs(german_score - english_score) < 0.1:
        print(f"   ✅ Similar scores for both languages (good multilingual support)")
    else:
        print(f"   ⚠️  Significant difference in scores")

def test_edge_cases():
    """Test edge cases and potential issues"""
    print(f"\n🔍 EDGE CASES TEST")
    print("-" * 20)
    
    matcher = CVMatcher()
    
    # Test empty CV
    empty_cv = ""
    job = "Java Developer with Spring Boot"
    score = matcher.calculate_skill_match(job, empty_cv)
    print(f"Empty CV: {score*100:.1f}%")
    
    # Test CV with only personal info
    personal_only_cv = """
    Max Mustermann
    Geburtsdatum: 01.01.1990
    Adresse: Berlin, Deutschland
    Telefon: +49 30 12345678
    E-Mail: <EMAIL>
    """
    score = matcher.calculate_skill_match(job, personal_only_cv)
    print(f"Personal info only: {score*100:.1f}%")
    
    # Test very short CV
    short_cv = "Java Developer"
    score = matcher.calculate_skill_match(job, short_cv)
    print(f"Very short CV: {score*100:.1f}%")
    
    # Test CV with special characters
    special_chars_cv = """
    Müller, Jürgen - Softwareentwickler
    Straße: Müllerstraße 123
    Fähigkeiten: Java, Spring Boot, PostgreSQL
    Größe: 1,80m
    """
    score = matcher.calculate_skill_match(job, special_chars_cv)
    print(f"Special characters: {score*100:.1f}%")

def main():
    """Run German-specific CV tests"""
    test_german_skill_detection()
    test_german_experience_parsing()
    test_german_education_parsing()
    test_mixed_language_cv()
    test_edge_cases()
    
    print(f"\n✅ German CV specific testing completed!")
    print(f"🎯 The system shows good German language support!")

if __name__ == "__main__":
    main()
