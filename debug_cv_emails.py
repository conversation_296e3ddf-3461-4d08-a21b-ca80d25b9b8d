#!/usr/bin/env python3
"""
🔍 Debug CV Email Extraction
============================
Check which CVs have extractable email addresses
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import the database and email extraction function
from hr_database_working import HRDatabase
from app_german import extract_email_from_cv_content

def debug_cv_emails():
    """Debug email extraction for all CVs"""
    print("🔍 Debugging CV Email Extraction")
    print("=" * 50)
    
    # Initialize database
    hr_db = HRDatabase()
    
    # Get all jobs
    jobs = hr_db.get_all_jobs()
    
    total_cvs = 0
    cvs_with_emails = 0
    cvs_without_emails = 0
    
    for job in jobs:
        print(f"\n📋 Job: {job.title}")
        print("-" * 30)
        
        # Get CVs for this job
        cvs = hr_db.get_cvs_for_job(job.title)
        
        if not cvs:
            print("   No CVs found for this job")
            continue
        
        for cv in cvs:
            total_cvs += 1
            print(f"\n   📄 CV: {cv.filename}")
            print(f"   👤 Candidate: {getattr(cv, 'candidate_name', 'Unknown')}")
            
            # Extract email
            extracted_email = extract_email_from_cv_content(cv.content)
            
            if extracted_email:
                cvs_with_emails += 1
                print(f"   ✅ Email found: {extracted_email}")
            else:
                cvs_without_emails += 1
                print(f"   ❌ No email found")
                
                # Show first 200 characters of content for debugging
                content_preview = cv.content[:200] if cv.content else "No content"
                print(f"   📝 Content preview: {content_preview}...")
    
    # Summary
    print(f"\n📊 Summary")
    print("=" * 20)
    print(f"Total CVs: {total_cvs}")
    print(f"CVs with emails: {cvs_with_emails}")
    print(f"CVs without emails: {cvs_without_emails}")
    
    if cvs_without_emails > 0:
        print(f"\n⚠️  {cvs_without_emails} CVs don't have extractable email addresses")
        print("   This is why emails are only sent to some CVs")
        print("\n💡 Solutions:")
        print("   1. Manually add email addresses to CV database")
        print("   2. Improve email extraction patterns")
        print("   3. Allow manual email input for CVs without emails")

def test_specific_cv_email(cv_filename):
    """Test email extraction for a specific CV"""
    print(f"🎯 Testing email extraction for: {cv_filename}")
    print("-" * 40)
    
    hr_db = HRDatabase()
    
    # Find the CV
    jobs = hr_db.get_all_jobs()
    target_cv = None
    
    for job in jobs:
        cvs = hr_db.get_cvs_for_job(job.title)
        for cv in cvs:
            if cv_filename.lower() in cv.filename.lower():
                target_cv = cv
                break
        if target_cv:
            break
    
    if not target_cv:
        print(f"❌ CV '{cv_filename}' not found")
        return
    
    print(f"✅ Found CV: {target_cv.filename}")
    print(f"👤 Candidate: {getattr(target_cv, 'candidate_name', 'Unknown')}")
    
    # Test email extraction
    extracted_email = extract_email_from_cv_content(target_cv.content)
    
    if extracted_email:
        print(f"✅ Email extracted: {extracted_email}")
    else:
        print("❌ No email found")
        print("\n📝 CV Content (first 500 chars):")
        print("-" * 30)
        print(target_cv.content[:500] if target_cv.content else "No content")
        print("-" * 30)

def add_manual_email_to_cv():
    """Add manual email to a CV that doesn't have one"""
    print("📧 Manual Email Addition Tool")
    print("-" * 30)
    
    hr_db = HRDatabase()
    
    # Show all CVs without emails
    jobs = hr_db.get_all_jobs()
    cvs_without_emails = []
    
    for job in jobs:
        cvs = hr_db.get_cvs_for_job(job.title)
        for cv in cvs:
            extracted_email = extract_email_from_cv_content(cv.content)
            if not extracted_email:
                cvs_without_emails.append({
                    'cv': cv,
                    'job': job.title
                })
    
    if not cvs_without_emails:
        print("✅ All CVs have extractable email addresses!")
        return
    
    print(f"Found {len(cvs_without_emails)} CVs without emails:")
    for i, item in enumerate(cvs_without_emails, 1):
        cv = item['cv']
        print(f"{i}. {cv.filename} ({getattr(cv, 'candidate_name', 'Unknown')}) - Job: {item['job']}")
    
    print("\n💡 To add emails manually, you would need to:")
    print("1. Update the CV content to include email addresses")
    print("2. Or modify the database to store emails separately")
    print("3. Or create a manual email mapping system")

def main():
    """Main function"""
    print("🔍 CV Email Debug Tool")
    print("=" * 30)
    print("1. Debug all CVs")
    print("2. Test specific CV")
    print("3. Show CVs without emails")
    
    choice = input("\nEnter choice (1-3): ").strip()
    
    if choice == "1":
        debug_cv_emails()
    elif choice == "2":
        cv_name = input("Enter CV filename (partial match): ").strip()
        test_specific_cv_email(cv_name)
    elif choice == "3":
        add_manual_email_to_cv()
    else:
        print("Invalid choice")

if __name__ == "__main__":
    main()
