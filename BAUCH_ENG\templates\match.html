{% extends 'base.html' %}

{% block title %}Match CVs - HR Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-check-circle me-2"></i>Match CVs to Job
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('match_results') }}">
                    <div class="mb-3">
                        <label for="job" class="form-label">Select Job</label>
                        <select class="form-select" id="job" name="job" required>
                            <option value="" selected disabled>Choose a job...</option>
                            {% for job in jobs %}
                                <option value="{{ job.title }}" {% if request.args.get('job') == job.title %}selected{% endif %}>
                                    {{ job.title }}
                                </option>
                            {% endfor %}
                        </select>
                        <div class="form-text">Select the job you want to match CVs against.</div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-info text-white">
                            <i class="fas fa-play-circle me-1"></i> Run Matching
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
