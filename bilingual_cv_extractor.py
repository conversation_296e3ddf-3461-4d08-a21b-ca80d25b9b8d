"""
Bilingual CV Data Extractor
Enhanced CV extractor with German and English language support
"""

import re
import os
from typing import Dict, List, Optional, Tuple
import fitz  # PyMuPDF
from docx import Document
from language_detector import LanguageDetector
from german_skills import GermanSkills, GERMAN_STOP_WORDS
from cv_extractor import CVDataExtractor


class BilingualCVExtractor(CVDataExtractor):
    def __init__(self):
        super().__init__()
        self.language_detector = LanguageDetector()
        self.german_skills = GermanSkills()
        
        # German name patterns
        self.german_name_patterns = [
            r'\b([A-ZÄÖÜ][a-zäöüß]+(?:\s+[A-ZÄÖÜ][a-zäöüß]+)+)\b',
            r'Name:\s*([A-ZÄÖÜ][a-zäöüß]+(?:\s+[A-ZÄÖÜ][a-zäöüß]+)+)',
            r'Vor-?\s*und\s*Nachname:\s*([A-Z<PERSON><PERSON>Ü][a-zä<PERSON>üß]+(?:\s+[A-<PERSON><PERSON><PERSON><PERSON>][a-z<PERSON><PERSON>üß]+)+)',
            r'Bewerbung\s+von\s+([A-ZÄÖÜ][a-zäöüß]+(?:\s+[A-ZÄÖÜ][a-zäöüß]+)+)',
            r'Lebenslauf\s+von\s+([A-ZÄÖÜ][a-zäöüß]+(?:\s+[A-ZÄÖÜ][a-zäöüß]+)+)'
        ]
        
        # German email patterns (same as English)
        self.email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        
        # German phone patterns
        self.german_phone_patterns = [
            r'\+49[\s\-]?\d{2,4}[\s\-]?\d{3,8}',  # German international format
            r'0\d{2,4}[\s\-/]?\d{3,8}',  # German national format
            r'\(\d{2,4}\)\s?\d{3,8}',  # With area code in parentheses
            r'\d{2,4}[\s\-/]\d{3,8}'  # Simple format
        ]
        
        # German experience patterns
        self.german_experience_patterns = [
            r'(\d+)\+?\s*Jahre?\s*(?:Berufs)?(?:erfahrung|tätigkeit)',
            r'(\d+)-(\d+)\s*Jahre?\s*(?:Berufs)?(?:erfahrung|tätigkeit)',
            r'(?:Berufs)?(?:erfahrung|tätigkeit)\s*:?\s*(\d+)\+?\s*Jahre?',
            r'(\d+)\+?\s*Jahre?\s*(?:in|im|als)\s*\w+',
            r'seit\s*(\d+)\s*Jahre?n?\s*(?:tätig|beschäftigt)',
            r'über\s*(\d+)\s*Jahre?\s*(?:Erfahrung|tätig)'
        ]
        
        # German education keywords
        self.german_education_keywords = [
            'universität', 'hochschule', 'fachhochschule', 'tu', 'fh',
            'bachelor', 'master', 'diplom', 'magister', 'promotion', 'doktor',
            'studium', 'studiengang', 'abschluss', 'ausbildung', 'lehre',
            'abitur', 'fachabitur', 'realschulabschluss', 'hauptschulabschluss',
            'weiterbildung', 'fortbildung', 'zertifikat', 'schulung'
        ]
    
    def extract_name_bilingual(self, text: str, filename: str = "") -> str:
        """Extract name from CV text supporting both German and English"""
        # Detect language first
        language, confidence = self.language_detector.detect_language(text)
        
        if language == 'german' or confidence < 0.7:
            # Try German patterns first
            for pattern in self.german_name_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    name = matches[0].strip()
                    # Filter out common German words
                    exclude_words = ['Sehr Geehrte', 'Sehr Geehrter', 'Mit Freundlichen', 'Freundlichen Grüßen']
                    if not any(exclude in name for exclude in exclude_words):
                        return name
        
        # Fallback to English patterns
        return super().extract_name(text, filename)
    
    def extract_phone_bilingual(self, text: str) -> str:
        """Extract phone number supporting German formats"""
        # Try German patterns first
        for pattern in self.german_phone_patterns:
            matches = re.findall(pattern, text)
            if matches:
                return matches[0]
        
        # Fallback to English patterns
        return super().extract_phone(text)
    
    def extract_experience_bilingual(self, text: str) -> str:
        """Extract work experience supporting German patterns"""
        # Detect language
        language, confidence = self.language_detector.detect_language(text)
        
        if language == 'german' or confidence < 0.7:
            # Try German patterns
            text_lower = text.lower()
            for pattern in self.german_experience_patterns:
                matches = re.findall(pattern, text_lower)
                if matches:
                    if isinstance(matches[0], tuple):
                        return f"{matches[0][0]}-{matches[0][1]} Jahre"
                    else:
                        return f"{matches[0]} Jahre"
            
            # Check for German experience level keywords
            if any(keyword in text_lower for keyword in ['senior', 'leitend', 'führung', 'manager', 'direktor']):
                return "Senior Level (5+ Jahre)"
            elif any(keyword in text_lower for keyword in ['erfahren', 'fortgeschritten', 'spezialist']):
                return "Mittleres Level (2-5 Jahre)"
            elif any(keyword in text_lower for keyword in ['junior', 'einsteiger', 'berufsanfänger', 'absolvent']):
                return "Junior Level (0-2 Jahre)"
        
        # Fallback to English extraction
        return super().extract_experience(text)
    
    def extract_skills_bilingual(self, text: str) -> str:
        """Extract skills supporting both German and English"""
        # Detect language
        language, confidence = self.language_detector.detect_language(text)
        
        found_skills = []
        
        # Extract German skills if German text or mixed
        if language == 'german' or language == 'mixed' or confidence < 0.7:
            german_skills_found = self.german_skills.find_skills_in_text(text)
            found_skills.extend(german_skills_found['all'])
        
        # Extract English skills
        if language == 'english' or language == 'mixed' or confidence < 0.7:
            text_lower = text.lower()
            for skill in self.skill_keywords:
                if skill.lower() in text_lower:
                    found_skills.append(skill.title())
        
        # Remove duplicates and limit
        unique_skills = list(dict.fromkeys(found_skills))  # Preserve order while removing duplicates
        
        if unique_skills:
            return ", ".join(unique_skills[:15])  # Increased limit for bilingual
        
        return "Fähigkeiten nicht spezifiziert" if language == 'german' else "Skills not specified"
    
    def extract_education_bilingual(self, text: str) -> str:
        """Extract education information supporting German terms"""
        # Detect language
        language, confidence = self.language_detector.detect_language(text)
        
        text_lower = text.lower()
        education_info = []
        
        # Use appropriate keywords based on language
        if language == 'german' or confidence < 0.7:
            keywords = self.german_education_keywords + self.education_keywords
        else:
            keywords = self.education_keywords + self.german_education_keywords
        
        # Look for education institutions
        lines = text.split('\n')
        for line in lines:
            line_lower = line.lower()
            if any(keyword in line_lower for keyword in keywords):
                clean_line = line.strip()
                if clean_line and len(clean_line) > 5:
                    education_info.append(clean_line)
        
        if education_info:
            return "; ".join(education_info[:3])
        
        return "Ausbildung nicht spezifiziert" if language == 'german' else "Education not specified"
    
    def extract_cv_data_bilingual(self, file_path: str, fields: List[str]) -> Dict[str, str]:
        """Extract specified fields from CV with bilingual support"""
        # Extract text
        text = self.extract_text_from_file(file_path)
        if not text:
            return {"error": "Could not extract text from file"}
        
        # Detect language and get info
        language_info = self.language_detector.get_language_info(text)
        
        filename = os.path.basename(file_path)
        data = {
            'language_detected': language_info['primary_language'],
            'language_confidence': language_info['confidence'],
            'is_bilingual': language_info['is_bilingual'],
            'processing_recommendation': language_info['recommendation']
        }
        
        # Extract fields using bilingual methods
        if 'name' in fields:
            data['name'] = self.extract_name_bilingual(text, filename)
        if 'email' in fields:
            data['email'] = self.extract_email(text)  # Email extraction is language-agnostic
        if 'phone' in fields:
            data['phone'] = self.extract_phone_bilingual(text)
        if 'experience' in fields:
            data['experience'] = self.extract_experience_bilingual(text)
        if 'skills' in fields:
            data['skills'] = self.extract_skills_bilingual(text)
        if 'education' in fields:
            data['education'] = self.extract_education_bilingual(text)
        
        return data
    
    def get_language_specific_summary(self, text: str) -> Dict[str, any]:
        """Get a summary of the CV with language-specific insights"""
        language_info = self.language_detector.get_language_info(text)
        
        # Extract skills by language
        german_skills = self.german_skills.find_skills_in_text(text)
        english_skills = []
        text_lower = text.lower()
        for skill in self.skill_keywords:
            if skill.lower() in text_lower:
                english_skills.append(skill)
        
        return {
            'language_info': language_info,
            'german_skills': {
                'technical': german_skills['technical'][:10],
                'soft': german_skills['soft'][:10],
                'count': len(german_skills['all'])
            },
            'english_skills': {
                'skills': english_skills[:10],
                'count': len(english_skills)
            },
            'total_skills_found': len(german_skills['all']) + len(english_skills),
            'bilingual_score': self._calculate_bilingual_score(text),
            'recommendations': self._get_processing_recommendations(language_info, german_skills, english_skills)
        }
    
    def _calculate_bilingual_score(self, text: str) -> float:
        """Calculate how bilingual the CV is (0-1 scale)"""
        german_skills = self.german_skills.find_skills_in_text(text)
        english_skills = []
        text_lower = text.lower()
        for skill in self.skill_keywords:
            if skill.lower() in text_lower:
                english_skills.append(skill)
        
        total_skills = len(german_skills['all']) + len(english_skills)
        if total_skills == 0:
            return 0.0
        
        # Calculate balance between German and English content
        german_ratio = len(german_skills['all']) / total_skills
        english_ratio = len(english_skills) / total_skills
        
        # Bilingual score is higher when both languages are present
        bilingual_score = 1 - abs(german_ratio - english_ratio)
        return min(bilingual_score, 1.0)
    
    def _get_processing_recommendations(self, language_info: Dict, german_skills: Dict, english_skills: List) -> List[str]:
        """Get recommendations for processing this CV"""
        recommendations = []
        
        if language_info['is_bilingual']:
            recommendations.append("Use bilingual matching algorithms for best results")
        
        if len(german_skills['all']) > len(english_skills):
            recommendations.append("Prioritize German job descriptions for matching")
        elif len(english_skills) > len(german_skills['all']):
            recommendations.append("Prioritize English job descriptions for matching")
        
        if language_info['confidence'] < 0.6:
            recommendations.append("Manual review recommended due to low language detection confidence")
        
        if len(german_skills['technical']) > 5:
            recommendations.append("Strong technical profile - suitable for technical positions")
        
        if len(german_skills['soft']) > 5:
            recommendations.append("Strong soft skills profile - suitable for leadership positions")
        
        return recommendations


# Utility functions for easy integration
def extract_bilingual_cv_data(file_path: str, fields: List[str]) -> Dict[str, str]:
    """Quick function to extract CV data with bilingual support"""
    extractor = BilingualCVExtractor()
    return extractor.extract_cv_data_bilingual(file_path, fields)


def get_cv_language_summary(file_path: str) -> Dict[str, any]:
    """Get language summary of a CV"""
    extractor = BilingualCVExtractor()
    text = extractor.extract_text_from_file(file_path)
    if text:
        return extractor.get_language_specific_summary(text)
    return {"error": "Could not extract text from file"}
