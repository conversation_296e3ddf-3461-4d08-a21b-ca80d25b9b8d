#!/usr/bin/env python3
"""
Debug the experience validation logic
"""

import re

def debug_experience_validation():
    """Debug experience validation"""
    print("=== DEBUGGING EXPERIENCE VALIDATION ===")
    
    daniel_cv = """
    Daniel Meixner
    Qualitätsfachkraft / QS-Messtechniker
    
    Berufserfahrung:
    2020-2024: Qualitätssicherung bei Firma ABC
    - Messtechnik und Qualitätskontrolle
    - CNC-Messmaschinen Programmierung
    - Koordinatenmesstechnik (KMG)
    - Qualitätsfachkraft
    
    Kenntnisse:
    - Qualitätssicherung
    - Messtechnik
    - CNC-Messprogramm
    - Koordinatenmesstechnik
    """
    
    horst_cv = """
    Horst Lippert
    
    Berufserfahrung:
    1999-2001: CNC-Kurs bei Bildungseinrichtung
    - Grundlagen CNC-Programmierung
    - Fanuc Steuerung
    
    2001-2020: Verschiedene Tätigkeiten
    - Allgemeine Fertigungsarbeiten
    - Maschinenbedienung
    
    Kenntnisse:
    - CNC-Programmierung (Grundkenntnisse)
    - Fanuc
    - Maschinenbedienung
    """
    
    print("🔍 DANIEL CV ANALYSIS:")
    print("-" * 40)
    analyze_cv_experience(daniel_cv, "Daniel")
    
    print("\n🔍 HORST CV ANALYSIS:")
    print("-" * 40)
    analyze_cv_experience(horst_cv, "Horst")

def analyze_cv_experience(cv_text, name):
    """Analyze CV experience step by step"""
    
    # Step 1: Extract experience sections
    experience_section_patterns = [
        r'berufserfahrung[:\s]*(.*?)(?=\n\s*[a-z]+[:\s]|$)',
        r'work experience[:\s]*(.*?)(?=\n\s*[a-z]+[:\s]|$)',
    ]
    
    experience_text = ""
    for pattern in experience_section_patterns:
        matches = re.findall(pattern, cv_text, re.IGNORECASE | re.DOTALL)
        if matches:
            experience_text += " ".join(matches)
            print(f"Found experience section: {matches[0][:100]}...")
    
    # Step 2: Look for year-based experience if no section found
    if not experience_text:
        year_based_pattern = r'(\d{4}[-–]\d{4}|\d{4}[-–]heute|\d{4}[-–]present)[:\s]*([^\n]+(?:\n[^\d\n][^\n]*)*)'
        year_matches = re.findall(year_based_pattern, cv_text, re.IGNORECASE)
        experience_text = " ".join([match[1] for match in year_matches])
        print(f"Found year-based experience: {len(year_matches)} entries")
        for i, (years, desc) in enumerate(year_matches):
            print(f"  {i+1}. {years}: {desc[:50]}...")
    
    # Step 3: Find CNC experience with context
    cnc_experience_pattern = r'(\d{4})[-–](\d{4}|heute|present)[:\s]*([^.\n]*(?:cnc|fanuc|heidenhain|fräs|dreh|zerspanung|programmier)[^.\n]*)'
    cnc_matches = re.findall(cnc_experience_pattern, experience_text.lower(), re.IGNORECASE)
    
    print(f"CNC experience matches found: {len(cnc_matches)}")
    for i, (start, end, context) in enumerate(cnc_matches):
        print(f"  {i+1}. {start}-{end}: {context[:80]}...")
        
        # Analyze context quality
        context_lower = context.lower()
        if any(term in context_lower for term in ['programmierung', 'programming', 'steuerung', 'rüsten', 'einrichten']):
            print(f"     → HIGH QUALITY (programming/setup)")
        elif any(term in context_lower for term in ['bedienung', 'operation', 'maschinen']):
            print(f"     → MEDIUM QUALITY (operation)")
        elif any(term in context_lower for term in ['cnc', 'fanuc', 'heidenhain']):
            print(f"     → LOW QUALITY (just mentions)")
    
    # Step 4: Check for education indicators
    education_indicators = [
        'zerspanungsmechaniker', 'industriemechaniker', 'feinwerkmechaniker',
        'maschinenbau', 'fertigungstechnik', 'mechanical engineering',
        'cnc-kurs', 'cnc kurs', 'cnc-schulung', 'cnc schulung'
    ]
    
    found_education = []
    for indicator in education_indicators:
        if indicator in cv_text.lower():
            found_education.append(indicator)
    
    print(f"Education indicators found: {found_education}")
    
    # Step 5: Check for keyword stuffing
    cnc_mentions = len(re.findall(r'\bcnc\b', cv_text.lower()))
    print(f"Total CNC mentions: {cnc_mentions}")
    print(f"Substantial contexts: {len(cnc_matches)}")
    
    if cnc_mentions > 5 and len(cnc_matches) < cnc_mentions / 3:
        print("⚠️  KEYWORD STUFFING DETECTED")
    else:
        print("✅ No keyword stuffing")

if __name__ == "__main__":
    debug_experience_validation()
