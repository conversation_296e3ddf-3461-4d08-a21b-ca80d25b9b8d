from sqlalchemy import create_engine, Column, Integer, String, Text, ForeignKey, inspect, text, Boolean, Date
from sqlalchemy.orm import sessionmaker, declarative_base, relationship
from sqlalchemy.exc import IntegrityError
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date
import os

# Define the declarative base
Base = declarative_base()


class Job(Base):
    __tablename__ = 'hr_jobs'

    id = Column(Integer, primary_key=True)
    title = Column(String(100), unique=True, nullable=False)
    description = Column(Text, nullable=False)
    platform = Column(String(100), nullable=True)
    job_url = Column(String(500), nullable=True)  # URL to the job posting
    start_date = Column(Date, nullable=True)
    end_date = Column(Date, nullable=True)
    status = Column(String(50), default='Active')
    cvs = relationship("CV", backref="job", cascade="all, delete-orphan")
    responsible_people = relationship("Responsible<PERSON><PERSON>", backref="job", cascade="all, delete-orphan")

    @property
    def days_remaining(self):
        if self.end_date:
            today = date.today()
            delta = self.end_date - today
            return delta.days
        return None

    @property
    def main_responsible_person(self):
        for person in self.responsible_people:
            if person.is_main_responsible:
                return person.name
        return "Not Assigned"


class CV(Base):
    __tablename__ = 'hr_cvs'

    id = Column(Integer, primary_key=True)
    filename = Column(String(255), nullable=False)
    content = Column(Text, nullable=False)
    job_id = Column(Integer, ForeignKey('hr_jobs.id'), nullable=False)
    candidate_name = Column(String(100), nullable=True)


class User(Base):
    __tablename__ = 'hr_users'

    id = Column(Integer, primary_key=True)
    username = Column(String(80), unique=True, nullable=False)
    email = Column(String(120), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)


class ResponsiblePerson(Base):
    __tablename__ = 'hr_responsible_people'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    email = Column(String(120), nullable=False)
    job_id = Column(Integer, ForeignKey('hr_jobs.id'), nullable=False)
    is_main_responsible = Column(Boolean, default=False)


class HRDatabase:
    def __init__(self, db_url=None):
        if db_url is None:
            # Use absolute path to ensure same database regardless of working directory
            script_dir = os.path.dirname(os.path.abspath(__file__))
            db_path = os.path.join(script_dir, 'hr_database.db')
            db_url = f'sqlite:///{db_path}'
        
        self.engine = create_engine(db_url)

        # Check if the database exists and if the candidate_name column exists
        # If not, add it to the table
        inspector = inspect(self.engine)
        if 'hr_cvs' in inspector.get_table_names():
            columns = [col['name'] for col in inspector.get_columns('hr_cvs')]
            if 'candidate_name' not in columns:
                with self.engine.begin() as conn:
                    conn.execute(text("ALTER TABLE hr_cvs ADD COLUMN candidate_name VARCHAR(100)"))
                    print("Added candidate_name column to hr_cvs table")

        Base.metadata.create_all(self.engine)
        self.Session = sessionmaker(bind=self.engine)

    def add_job(self, title, description, responsible_people=None, platform=None, job_url=None, start_date=None, end_date=None, status='Active'):
        session = self.Session()
        try:
            # Check if job already exists
            existing = session.query(Job).filter(Job.title == title).first()
            if existing:
                raise ValueError(f"Job '{title}' already exists")

            job = Job(
                title=title,
                description=description,
                platform=platform,
                job_url=job_url,
                start_date=start_date,
                end_date=end_date,
                status=status
            )
            session.add(job)
            session.flush()  # Get the job ID

            # Add responsible people if provided
            if responsible_people:
                for person_data in responsible_people:
                    person = ResponsiblePerson(
                        name=person_data['name'],
                        email=person_data['email'],
                        job_id=job.id,
                        is_main_responsible=person_data.get('is_main', False)
                    )
                    session.add(person)

            session.commit()
            return job
        except IntegrityError as e:
            session.rollback()
            raise ValueError(f"Job '{title}' already exists") from e
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def get_job_by_title(self, title):
        session = self.Session()
        try:
            return session.query(Job).filter(Job.title == title).first()
        finally:
            session.close()

    def get_all_jobs(self):
        session = self.Session()
        try:
            return session.query(Job).all()
        finally:
            session.close()

    def add_cv(self, filename, content, job_title, candidate_name=None):
        session = self.Session()
        try:
            job = self.get_job_by_title(job_title)
            if not job:
                raise ValueError(f"Job '{job_title}' not found")

            cv = CV(filename=filename, content=content, job_id=job.id, candidate_name=candidate_name)
            session.add(cv)
            session.commit()
            return cv
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def get_cvs_for_job(self, job_title):
        session = self.Session()
        try:
            job = self.get_job_by_title(job_title)
            if not job:
                raise ValueError(f"Job '{job_title}' not found")

            return session.query(CV).filter(CV.job_id == job.id).all()
        finally:
            session.close()

    def delete_job(self, title):
        session = self.Session()
        try:
            job = session.query(Job).filter(Job.title == title).first()
            if not job:
                raise ValueError(f"Job '{title}' not found")

            session.delete(job)
            session.commit()
            return True
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def get_cv_count_for_job(self, job_title):
        session = self.Session()
        try:
            job = session.query(Job).filter(Job.title == job_title).first()
            if not job:
                return 0
            return session.query(CV).filter(CV.job_id == job.id).count()
        finally:
            session.close()

    def remove_cv(self, filename, job_title):
        """Remove a CV from the database"""
        session = self.Session()
        try:
            job = session.query(Job).filter(Job.title == job_title).first()
            if not job:
                raise ValueError(f"Job '{job_title}' not found")

            cv = session.query(CV).filter(CV.filename == filename, CV.job_id == job.id).first()
            if not cv:
                raise ValueError(f"CV '{filename}' not found for job '{job_title}'")

            session.delete(cv)
            session.commit()
            return True
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    # User authentication methods
    def create_user(self, username, email, password):
        session = self.Session()
        try:
            # Check if user already exists
            existing = session.query(User).filter(
                (User.username == username) | (User.email == email)
            ).first()
            if existing:
                raise ValueError("Username or email already exists")

            user = User(username=username, email=email)
            user.set_password(password)
            session.add(user)
            session.commit()
            return user
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def authenticate_user(self, username, password):
        session = self.Session()
        try:
            user = session.query(User).filter(User.username == username).first()
            if user and user.check_password(password) and user.is_active:
                return user
            return None
        finally:
            session.close()

    def get_user_by_id(self, user_id):
        session = self.Session()
        try:
            return session.query(User).filter(User.id == user_id).first()
        finally:
            session.close()

    # Responsible people methods
    def get_responsible_people_for_job(self, job_id):
        session = self.Session()
        try:
            return session.query(ResponsiblePerson).filter(ResponsiblePerson.job_id == job_id).all()
        finally:
            session.close()

    def get_main_responsible_for_job(self, job_id):
        session = self.Session()
        try:
            return session.query(ResponsiblePerson).filter(
                ResponsiblePerson.job_id == job_id,
                ResponsiblePerson.is_main_responsible == True
            ).first()
        finally:
            session.close()
