LEBENSLAUF

<PERSON>in

Kontakt:
E-Mail: <EMAIL>
Telefon: +49 30 ********
Adresse: Berlin, Deutschland

BERUFSERFAHRUNG

Senior Java Entwicklerin | TechCorp GmbH | 2020 - heute
• Entwicklung von Webanwendungen mit Java und Spring Boot
• Arbeit mit PostgreSQL-Datenbanken und relationalen Datenbanken
• Entwicklung von REST-Services und REST-APIs
• Nutzung von Git und Jenkins für CI/CD-Prozesse
• Arbeit in einem agilen Scrum-Team
• Führung von Junior-Entwicklern
• 4 Jahre Berufserfahrung in der Softwareentwicklung

Java Entwicklerin | StartupTech | 2018 - 2020
• Entwicklung von Backend-Services mit Java
• Implementierung von Microservices-Architekturen
• Arbeit mit Docker und Kubernetes
• Verwendung von Maven und Gradle
• Agile Entwicklung nach Scrum-Methodik

AUSBILDUNG

Master of Science Informatik | Technische Universität Berlin | 2016 - 2018
• Schwerpunkt: Softwareentwicklung und Datenbanken
• Abschlussnote: 1,3

Bachelor of Science Informatik | Universität München | 2013 - 2016
• Grundlagen der Informatik und Programmierung
• Abschlussnote: 1,5

TECHNISCHE KENNTNISSE

Programmiersprachen:
• Java (Expertenlevel) - 5+ Jahre Erfahrung
• Python (Fortgeschritten)
• JavaScript (Grundkenntnisse)

Frameworks und Technologien:
• Spring Boot, Spring Framework
• Hibernate, JPA
• REST-API Entwicklung
• Microservices

Datenbanken:
• PostgreSQL (Expertenlevel)
• MySQL
• MongoDB (Grundkenntnisse)

Tools und Methoden:
• Git, GitHub, GitLab
• Jenkins, CI/CD
• Docker, Kubernetes
• Maven, Gradle
• IntelliJ IDEA, Eclipse
• Scrum, Agile Methoden
• JIRA, Confluence

SPRACHKENNTNISSE
• Deutsch: Muttersprache
• Englisch: Verhandlungssicher (C1)

PROJEKTE

E-Commerce Platform | 2021 - 2022
• Entwicklung einer vollständigen E-Commerce-Lösung
• Verwendung von Java, Spring Boot, PostgreSQL
• Implementation von REST-APIs für Frontend-Integration
• Deployment mit Docker und Kubernetes

Banking Application | 2019 - 2020
• Entwicklung einer sicheren Banking-Anwendung
• Verwendung von Java, Spring Security
• Integration mit externen Payment-APIs
• Agile Entwicklung im Scrum-Team
