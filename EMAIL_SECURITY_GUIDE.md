# 🔐 Email Security Configuration Guide
## BAUCH HR Management System - Listmonk Integration

### 📋 Overview
This guide explains how to securely configure email services for your HR management system using Listmonk.

---

## 🚨 CRITICAL SECURITY STEPS

### 1. **Change Default Credentials IMMEDIATELY**
```bash
# Default credentials that MUS<PERSON> be changed:
Listmonk Admin: admin/listmonk
Database: listmonk/listmonk
```

### 2. **Where to Configure Email Credentials**

#### **Option A: Environment Variables (Recommended)**
Create a `.env` file in your project root:

```bash
# Run the setup script
python setup_secure_email.py
```

#### **Option B: Manual Configuration**
Edit these files with your email credentials:

**File: `.env`**
```env
# Your actual email account credentials
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true

# The "From" address in emails
DEFAULT_SENDER_EMAIL=<EMAIL>
DEFAULT_SENDER_NAME=BAUCH HR Team
```

**File: `email_system/config.toml`**
```toml
[smtp]
host = "smtp.gmail.com"
port = 587
username = "<EMAIL>"
password = "your-app-password"
tls_enabled = true
```

---

## 📧 Email Provider Setup

### **Gmail Configuration**
1. **Enable 2-Factor Authentication**
2. **Generate App Password:**
   - Go to Google Account → Security → App passwords
   - Select "Mail" and generate password
   - Use this app password, NOT your regular password

```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-16-char-app-password
```

### **Outlook/Office 365 Configuration**
```env
SMTP_HOST=smtp-mail.outlook.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-password
```

### **Custom SMTP Configuration**
```env
SMTP_HOST=mail.yourcompany.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-smtp-password
```

---

## 🛡️ Security Best Practices

### **1. Network Security**
- Listmonk runs on `localhost:9000` (not accessible from internet)
- Use reverse proxy (nginx) for external access
- Enable firewall to block port 9000 externally

### **2. Database Security**
```bash
# Change PostgreSQL credentials
sudo -u postgres psql
CREATE USER listmonk_secure WITH PASSWORD 'strong-password';
CREATE DATABASE listmonk OWNER listmonk_secure;
```

### **3. File Permissions**
```bash
# Secure configuration files
chmod 600 .env
chmod 600 email_system/config.toml
```

### **4. SSL/TLS Configuration**
- Always use TLS for SMTP (`tls_enabled = true`)
- Use HTTPS for web interface in production
- Enable SSL for database connections

---

## 🔧 Installation & Setup

### **Step 1: Install Dependencies**
```bash
pip install python-dotenv requests
```

### **Step 2: Run Security Setup**
```bash
python setup_secure_email.py
```

### **Step 3: Configure Listmonk**
```bash
cd email_system
./listmonk --install  # First time setup
./listmonk            # Start server
```

### **Step 4: Access Web Interface**
- URL: http://localhost:9000
- Login with your secure credentials
- Configure SMTP settings in the web interface

---

## 🚦 Testing Email Configuration

### **Test 1: Basic Connection**
```python
from listmonk_integration import ListmonkEmailService

# Test connection
email_service = ListmonkEmailService()
# Should show: "✅ Connected to Listmonk server successfully"
```

### **Test 2: Send Test Email**
```python
# Send test email through Listmonk web interface
# Go to http://localhost:9000 → Campaigns → Create test campaign
```

---

## 🔍 Monitoring & Logging

### **Check Email Logs**
```bash
# Application logs
tail -f logs/hr_system.log

# Listmonk logs
cd email_system
./listmonk --help  # Check log options
```

### **Monitor Email Delivery**
- Use Listmonk web interface: http://localhost:9000
- Check campaign statistics
- Monitor bounce rates and delivery status

---

## ⚠️ Common Security Issues

### **❌ DON'T DO THIS:**
```python
# Hardcoded credentials (INSECURE)
email_service = ListmonkEmailService(
    username="admin",
    password="listmonk"  # Default password!
)
```

### **✅ DO THIS:**
```python
# Environment variables (SECURE)
email_service = ListmonkEmailService()  # Uses .env file
```

---

## 🆘 Troubleshooting

### **Issue: "Authentication failed"**
- Check SMTP credentials in `.env` file
- Verify app password for Gmail
- Test SMTP settings manually

### **Issue: "Connection refused"**
- Ensure Listmonk is running: `cd email_system && ./listmonk`
- Check if port 9000 is available
- Verify firewall settings

### **Issue: "Database connection failed"**
- Check PostgreSQL is running
- Verify database credentials
- Ensure database exists

---

## 📞 Support

If you encounter issues:
1. Check the logs: `logs/hr_system.log`
2. Verify all credentials are correct
3. Test SMTP settings independently
4. Check Listmonk documentation: https://listmonk.app/docs/

---

## 🔒 Security Checklist

- [ ] Changed default Listmonk admin password
- [ ] Changed default database password
- [ ] Created secure `.env` file
- [ ] Configured proper SMTP credentials
- [ ] Enabled TLS for email sending
- [ ] Restricted network access to port 9000
- [ ] Set up proper file permissions
- [ ] Enabled logging and monitoring
- [ ] Tested email sending functionality
- [ ] Documented credentials securely

**Remember: Never commit `.env` file to version control!**
