# BAUCH HR Management System - Deployment Guide

## 🚀 Production Deployment Options

This guide covers multiple deployment methods for the BAUCH HR Management System with enhanced security and bilingual capabilities.

## 📋 Prerequisites

- Linux server (Ubuntu 20.04+ recommended)
- Python 3.8+
- At least 2GB RAM
- 10GB+ disk space
- Domain name (optional but recommended)

## 🔧 Method 1: Traditional Server Deployment

### 1. Automated Deployment Script

The easiest way to deploy:

```bash
# Clone the repository
git clone <your-repo-url>
cd BAUCH_GERMAN

# Run the deployment script
chmod +x deploy.sh
./deploy.sh
```

This script will:
- Install system dependencies
- Setup Python virtual environment
- Configure Gunicorn WSGI server
- Setup Nginx reverse proxy
- Create systemd service
- Configure firewall
- Optionally setup SSL with Let's Encrypt

### 2. Manual Deployment Steps

If you prefer manual control:

#### Step 1: System Dependencies
```bash
sudo apt update
sudo apt install -y python3 python3-pip python3-venv nginx supervisor git curl build-essential python3-dev libffi-dev libssl-dev
```

#### Step 2: Application Setup
```bash
# Create application directory
sudo mkdir -p /var/www/bauch-hr
sudo chown $USER:$USER /var/www/bauch-hr

# Copy application files
cp -r . /var/www/bauch-hr/
cd /var/www/bauch-hr

# Setup virtual environment
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
pip install gunicorn

# Download language models
python -m spacy download en_core_web_sm
python -m spacy download de_core_news_sm
```

#### Step 3: Database Setup
```bash
# Create admin user
python create_admin_user.py
```

#### Step 4: Gunicorn Configuration
Create `/var/www/bauch-hr/gunicorn.conf.py`:
```python
bind = "127.0.0.1:8000"
workers = 2
worker_class = "sync"
timeout = 30
keepalive = 2
user = "your-username"
group = "your-username"
```

#### Step 5: Systemd Service
Create `/etc/systemd/system/bauch-hr.service`:
```ini
[Unit]
Description=BAUCH HR Management System
After=network.target

[Service]
Type=exec
User=your-username
Group=your-username
WorkingDirectory=/var/www/bauch-hr
Environment=PATH=/var/www/bauch-hr/venv/bin
Environment=FLASK_ENV=production
ExecStart=/var/www/bauch-hr/venv/bin/gunicorn --config /var/www/bauch-hr/gunicorn.conf.py wsgi:application
Restart=always

[Install]
WantedBy=multi-user.target
```

#### Step 6: Nginx Configuration
Create `/etc/nginx/sites-available/bauch-hr`:
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    client_max_body_size 16M;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /static {
        alias /var/www/bauch-hr/static;
        expires 1y;
    }
}
```

#### Step 7: Enable and Start Services
```bash
sudo systemctl daemon-reload
sudo systemctl enable bauch-hr
sudo systemctl start bauch-hr

sudo ln -s /etc/nginx/sites-available/bauch-hr /etc/nginx/sites-enabled/
sudo systemctl restart nginx
```

## 🐳 Method 2: Docker Deployment

### Quick Start with Docker Compose

```bash
# Clone repository
git clone <your-repo-url>
cd BAUCH_GERMAN

# Set environment variables
export SECRET_KEY=$(openssl rand -hex 32)

# Build and start
docker-compose up -d

# Create admin user
docker-compose exec web python create_admin_user.py
```

### Manual Docker Build

```bash
# Build image
docker build -t bauch-hr .

# Run container
docker run -d \
  --name bauch-hr \
  -p 8000:8000 \
  -v $(pwd)/uploads:/app/uploads \
  -v $(pwd)/hr_database.db:/app/hr_database.db \
  -e SECRET_KEY="your-secret-key" \
  -e FLASK_ENV=production \
  bauch-hr
```

## 🔒 Security Configuration

### 1. Environment Variables

Set these environment variables for production:

```bash
export SECRET_KEY="your-very-secure-secret-key"
export FLASK_ENV="production"
export DATABASE_URL="sqlite:///path/to/database.db"
export UPLOAD_FOLDER="/secure/path/to/uploads"
```

### 2. SSL/HTTPS Setup

#### Using Let's Encrypt (Recommended)
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

#### Manual SSL Certificate
```bash
# Place your SSL certificates
sudo mkdir -p /etc/nginx/ssl
sudo cp your-cert.pem /etc/nginx/ssl/
sudo cp your-key.pem /etc/nginx/ssl/
sudo chmod 600 /etc/nginx/ssl/*
```

### 3. Firewall Configuration
```bash
sudo ufw allow 22/tcp   # SSH
sudo ufw allow 80/tcp   # HTTP
sudo ufw allow 443/tcp  # HTTPS
sudo ufw enable
```

### 4. Database Security
- Use strong passwords for admin accounts
- Regular database backups
- Restrict database file permissions

## 📊 Monitoring and Maintenance

### Service Management
```bash
# Check application status
sudo systemctl status bauch-hr

# View logs
sudo journalctl -u bauch-hr -f

# Restart application
sudo systemctl restart bauch-hr

# Restart web server
sudo systemctl restart nginx
```

### Log Files
- Application logs: `/var/www/bauch-hr/logs/`
- Nginx logs: `/var/log/nginx/`
- System logs: `journalctl -u bauch-hr`

### Backup Strategy
```bash
# Database backup
cp /var/www/bauch-hr/hr_database.db /backup/location/

# Uploads backup
tar -czf uploads_backup.tar.gz /var/www/bauch-hr/uploads/

# Configuration backup
tar -czf config_backup.tar.gz /etc/nginx/sites-available/ /etc/systemd/system/bauch-hr.service
```

## 🔧 Performance Optimization

### 1. Gunicorn Workers
Adjust workers based on CPU cores:
```python
# In gunicorn.conf.py
workers = (2 * cpu_cores) + 1
```

### 2. Nginx Caching
Add to Nginx configuration:
```nginx
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 3. Database Optimization
- Regular VACUUM for SQLite
- Consider PostgreSQL for high-traffic deployments

## 🌐 Multi-Language Features

The system includes:
- Automatic language detection (German/English)
- Bilingual CV processing
- Cross-language job matching
- UI language switching
- German-specific skill recognition

## 🔍 Troubleshooting

### Common Issues

1. **Application won't start**
   ```bash
   sudo journalctl -u bauch-hr -n 50
   ```

2. **Permission errors**
   ```bash
   sudo chown -R your-user:your-user /var/www/bauch-hr
   ```

3. **Database errors**
   ```bash
   cd /var/www/bauch-hr
   source venv/bin/activate
   python create_admin_user.py
   ```

4. **Language models missing**
   ```bash
   source venv/bin/activate
   python -m spacy download en_core_web_sm
   python -m spacy download de_core_news_sm
   ```

### Health Checks
```bash
# Check if application is responding
curl http://localhost:8000/

# Check Nginx status
sudo nginx -t
sudo systemctl status nginx

# Check disk space
df -h

# Check memory usage
free -h
```

## 📞 Support

For deployment issues:
1. Check the logs first
2. Verify all dependencies are installed
3. Ensure proper file permissions
4. Check firewall settings
5. Verify SSL certificates (if using HTTPS)

## 🎯 Production Checklist

- [ ] Set strong SECRET_KEY
- [ ] Configure proper database permissions
- [ ] Setup SSL/HTTPS
- [ ] Configure firewall
- [ ] Setup log rotation
- [ ] Configure backups
- [ ] Test language switching
- [ ] Test CV upload and matching
- [ ] Verify password change functionality
- [ ] Test account lockout features
- [ ] Monitor resource usage

The BAUCH HR Management System is now ready for production use with enhanced security, bilingual capabilities, and professional deployment options!
