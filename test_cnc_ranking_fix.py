#!/usr/bin/env python3
"""
Test the improved CNC ranking to see if <PERSON> gets properly ranked
"""

from hr_database_working import HRDatabase
from advanced_llm_matcher import <PERSON>hancedCVMatch<PERSON>

def test_cnc_ranking():
    """Test CNC job ranking with enhanced approach"""
    print("=== TESTING ENHANCED CNC RANKING ===")
    
    try:
        db = HRDatabase()
        enhanced_matcher = EnhancedCVMatcher()

        # Get CNC job and CVs
        cnc_job = db.get_job_by_title('CNC Fräser')
        if not cnc_job:
            print("CNC job not found!")
            return

        cvs = db.get_cvs_for_job('C<PERSON> Fräser')
        print(f"Found {len(cvs)} CVs for CNC job")
        print(f"Job domain detected: {enhanced_matcher.advanced_matcher.detect_job_domain(cnc_job.description)}")
        print()
        
        # Test enhanced matching on each CV
        results = []
        for cv in cvs:
            try:
                result = enhanced_matcher.advanced_matcher.calculate_advanced_match_score(cv.content, cnc_job.description)
                results.append({
                    'name': cv.candidate_name or 'Unknown',
                    'score': result['final_score'],
                    'breakdown': result['breakdown'],
                    'domain': result['domain'],
                    'explanation': result['explanation']
                })
                
            except Exception as e:
                print(f"Error processing {cv.candidate_name}: {e}")
                # Fallback scoring
                results.append({
                    'name': cv.candidate_name or 'Unknown',
                    'score': 50.0,  # Default score
                    'breakdown': {'skills': 50, 'experience': 50, 'education': 50},
                    'domain': 'unknown',
                    'explanation': f'Error: {e}'
                })
        
        # Sort by score
        results.sort(key=lambda x: x['score'], reverse=True)
        
        print("=== ENHANCED RANKING RESULTS ===")
        for i, result in enumerate(results, 1):
            print(f"{i}. {result['name']}")
            print(f"   Overall Score: {result['score']:.1f}%")
            print(f"   Skills: {result['breakdown']['skills']:.1f}% | Experience: {result['breakdown']['experience']:.1f}% | Education: {result['breakdown']['education']:.1f}%")
            print(f"   Domain: {result['domain']}")
            print(f"   Explanation: {result['explanation'][:100]}...")
            print()
        
        # Check if Daniel is ranked higher
        daniel_rank = next((i for i, r in enumerate(results, 1) if 'Daniel' in r['name']), None)
        reichelt_rank = next((i for i, r in enumerate(results, 1) if 'Reichelt' in r['name']), None)
        pascal_rank = next((i for i, r in enumerate(results, 1) if 'Pascal' in r['name']), None)
        
        print("=== RANKING ANALYSIS ===")
        if daniel_rank:
            print(f"Daniel Meixner (CNC experience): Rank #{daniel_rank}")
        if reichelt_rank:
            print(f"Reichelt Frank (less relevant): Rank #{reichelt_rank}")
        if pascal_rank:
            print(f"Pascal Baum (technical degree): Rank #{pascal_rank}")
        
        print()
        if daniel_rank and reichelt_rank:
            if daniel_rank < reichelt_rank:
                print("✅ SUCCESS: Daniel is ranked higher than Reichelt!")
                print("   This suggests the enhanced algorithm properly weights CNC experience.")
            else:
                print("❌ ISSUE: Daniel is still ranked lower than Reichelt.")
                print("   The algorithm may need further tuning.")
        
        # Show detailed comparison for top candidates
        print("\n=== DETAILED COMPARISON ===")
        for result in results[:3]:
            print(f"\n{result['name']}:")
            print(f"  Final Score: {result['score']:.1f}%")
            print(f"  Skills Score: {result['breakdown']['skills']:.1f}%")
            print(f"  Experience Score: {result['breakdown']['experience']:.1f}%")
            print(f"  Education Score: {result['breakdown']['education']:.1f}%")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_cnc_ranking()
