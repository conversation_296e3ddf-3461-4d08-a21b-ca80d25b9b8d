#!/usr/bin/env python3
"""
Test Excel Extraction Functionality
Tests the actual Excel extraction feature from the web application
"""

import os
import glob
import tempfile
import sys
from typing import Dict, List

# Add the current directory to Python path to import app modules
sys.path.append('.')

from cv_extractor import CVDataExtractor
from hr_database import HRDatabase

class ExcelExtractionFunctionalityTest:
    def __init__(self):
        self.extractor = CVDataExtractor()
        self.db = HRDatabase()
        self.all_fields = ['name', 'email', 'phone', 'experience', 'skills', 'education']

    def test_excel_extraction_simulation(self):
        """Simulate the Excel extraction functionality"""
        print("📊 EXCEL EXTRACTION FUNCTIONALITY TEST")
        print("=" * 50)
        
        # Get all jobs from database
        try:
            jobs = self.db.get_all_jobs()
            print(f"📁 Found {len(jobs)} jobs in database")
            
            if not jobs:
                print("❌ No jobs found in database")
                return
            
            # Test extraction for each job
            for job in jobs:
                print(f"\n💼 Testing Excel extraction for job: {job.title}")
                print("-" * 40)
                
                try:
                    # Get CVs for this job
                    cvs = self.db.get_cvs_for_job(job.title)
                    print(f"   📄 Found {len(cvs)} CVs for this job")
                    
                    if not cvs:
                        print("   ⚠️  No CVs found for this job")
                        continue
                    
                    # Test extraction for each CV
                    extraction_results = []
                    successful_extractions = 0
                    
                    for cv in cvs:
                        print(f"\n   📄 Testing CV: {cv.filename}")
                        
                        # Try to find the actual file
                        cv_path = self.find_cv_file(cv.filename)
                        
                        if not cv_path:
                            print(f"      ❌ CV file not found on disk")
                            continue
                        
                        try:
                            # Extract all fields (simulating Excel extraction)
                            extracted_data = self.extractor.extract_cv_data(cv_path, self.all_fields)
                            
                            # Validate extraction
                            field_success = 0
                            for field in self.all_fields:
                                value = extracted_data.get(field, '')
                                is_valid = self.validate_extracted_field(field, value)
                                
                                status = "✅" if is_valid else "❌"
                                print(f"      {status} {field}: '{value[:50]}{'...' if len(value) > 50 else ''}'")
                                
                                if is_valid:
                                    field_success += 1
                            
                            success_rate = (field_success / len(self.all_fields)) * 100
                            print(f"      📊 Extraction Success: {success_rate:.1f}% ({field_success}/{len(self.all_fields)})")
                            
                            extraction_results.append({
                                'cv_filename': cv.filename,
                                'candidate_name': getattr(cv, 'candidate_name', 'Unknown'),
                                'extracted_data': extracted_data,
                                'success_rate': success_rate
                            })
                            
                            if success_rate >= 50:  # Consider 50%+ as successful
                                successful_extractions += 1
                                
                        except Exception as e:
                            print(f"      ❌ Error extracting from {cv.filename}: {e}")
                    
                    # Summary for this job
                    if extraction_results:
                        avg_success = sum(r['success_rate'] for r in extraction_results) / len(extraction_results)
                        print(f"\n   📊 Job Summary:")
                        print(f"      Total CVs processed: {len(extraction_results)}")
                        print(f"      Successful extractions (≥50%): {successful_extractions}")
                        print(f"      Average success rate: {avg_success:.1f}%")
                        
                        # Test if this would work for Excel export
                        self.test_excel_export_simulation(job.title, extraction_results)
                    
                except Exception as e:
                    print(f"   ❌ Error processing job {job.title}: {e}")
                    
        except Exception as e:
            print(f"❌ Error accessing database: {e}")

    def find_cv_file(self, filename: str) -> str:
        """Find the actual CV file on disk"""
        upload_folder = 'uploads'
        
        # Try exact filename first
        exact_path = os.path.join(upload_folder, filename)
        if os.path.exists(exact_path):
            return exact_path
        
        # Try without candidate name prefix
        if '_' in filename:
            parts = filename.split('_', 1)
            if len(parts) > 1:
                original_filename = parts[1]
                original_path = os.path.join(upload_folder, original_filename)
                if os.path.exists(original_path):
                    return original_path
        
        # Try to find any file that matches
        if os.path.exists(upload_folder):
            for file in os.listdir(upload_folder):
                if filename.endswith(file) or file.endswith(filename.split('_')[-1]):
                    return os.path.join(upload_folder, file)
        
        return None

    def validate_extracted_field(self, field: str, value: str) -> bool:
        """Validate if extracted field is meaningful"""
        if not value or value in ['not found', 'not specified', '']:
            return False
        
        value_lower = value.lower()
        
        if field == 'name':
            # Should have at least 2 words and not be generic text
            words = value.split()
            return (len(words) >= 2 and 
                   not any(generic in value_lower for generic in ['email', 'phone', 'address', 'contact']))
        
        elif field == 'email':
            return '@' in value and '.' in value
        
        elif field == 'phone':
            digits = ''.join(filter(str.isdigit, value))
            return len(digits) >= 6
        
        elif field == 'experience':
            experience_indicators = ['year', 'jahre', 'experience', 'erfahrung', 'senior', 'junior']
            return any(indicator in value_lower for indicator in experience_indicators)
        
        elif field == 'skills':
            skill_indicators = ['java', 'python', 'javascript', 'sql', 'git', 'spring', 'react', 'docker', 'windows', 'linux']
            return any(skill in value_lower for skill in skill_indicators)
        
        elif field == 'education':
            education_indicators = ['degree', 'university', 'college', 'bachelor', 'master', 'informatik', 'computer', 'fachinformatiker']
            return any(indicator in value_lower for indicator in education_indicators)
        
        return True

    def test_excel_export_simulation(self, job_title: str, extraction_results: List[Dict]):
        """Simulate Excel export functionality"""
        print(f"\n   📊 Excel Export Simulation:")
        
        # Check if we have enough data for meaningful Excel export
        valid_extractions = [r for r in extraction_results if r['success_rate'] >= 50]
        
        if not valid_extractions:
            print(f"      ❌ No valid extractions for Excel export")
            return
        
        print(f"      ✅ {len(valid_extractions)} CVs ready for Excel export")
        
        # Simulate creating Excel data structure
        excel_data = []
        for result in valid_extractions:
            row_data = {
                'Candidate Name': result['candidate_name'],
                'CV Filename': result['cv_filename'],
                'Success Rate': f"{result['success_rate']:.1f}%"
            }
            
            # Add extracted fields
            for field in self.all_fields:
                field_value = result['extracted_data'].get(field, '')
                # Truncate long values for Excel
                if len(field_value) > 100:
                    field_value = field_value[:97] + "..."
                row_data[field.title()] = field_value
            
            excel_data.append(row_data)
        
        # Simulate Excel file creation
        print(f"      📋 Excel data structure created:")
        print(f"         Rows: {len(excel_data)}")
        print(f"         Columns: {len(excel_data[0]) if excel_data else 0}")
        
        # Show sample data
        if excel_data:
            print(f"      📄 Sample row:")
            sample_row = excel_data[0]
            for key, value in list(sample_row.items())[:3]:  # Show first 3 fields
                print(f"         {key}: {value}")

    def test_language_specific_extraction(self):
        """Test extraction accuracy for German vs English CVs"""
        print(f"\n🌍 LANGUAGE-SPECIFIC EXTRACTION TEST")
        print("=" * 50)
        
        cv_files = glob.glob("uploads/*.pdf") + glob.glob("uploads/*.docx")
        
        german_cvs = []
        english_cvs = []
        
        # Categorize CVs by language
        for cv_file in cv_files:
            filename = os.path.basename(cv_file).lower()
            
            # Determine language based on filename and content
            if any(indicator in filename for indicator in ['maria', 'max', 'german', 'cv_', 'mueller', 'schmidt']):
                german_cvs.append(cv_file)
            elif any(indicator in filename for indicator in ['emma', 'taha', 'brooks', 'mughal']):
                english_cvs.append(cv_file)
            else:
                # Check content to determine language
                try:
                    content = self.extractor.extract_text_from_file(cv_file)
                    if content:
                        content_lower = content.lower()
                        german_indicators = ['berufserfahrung', 'ausbildung', 'kenntnisse', 'lebenslauf', 'telefon']
                        english_indicators = ['experience', 'education', 'skills', 'resume', 'phone']
                        
                        german_count = sum(1 for indicator in german_indicators if indicator in content_lower)
                        english_count = sum(1 for indicator in english_indicators if indicator in content_lower)
                        
                        if german_count > english_count:
                            german_cvs.append(cv_file)
                        else:
                            english_cvs.append(cv_file)
                except:
                    pass
        
        print(f"📄 German CVs: {len(german_cvs)}")
        print(f"📄 English CVs: {len(english_cvs)}")
        
        # Test German CVs
        if german_cvs:
            print(f"\n🇩🇪 Testing German CV extraction:")
            german_success_rates = []
            
            for cv_file in german_cvs:
                filename = os.path.basename(cv_file)
                try:
                    extracted_data = self.extractor.extract_cv_data(cv_file, self.all_fields)
                    
                    field_success = 0
                    for field in self.all_fields:
                        value = extracted_data.get(field, '')
                        if self.validate_extracted_field(field, value):
                            field_success += 1
                    
                    success_rate = (field_success / len(self.all_fields)) * 100
                    german_success_rates.append(success_rate)
                    
                    print(f"   {filename}: {success_rate:.1f}%")
                    
                except Exception as e:
                    print(f"   {filename}: Error - {e}")
            
            if german_success_rates:
                avg_german = sum(german_success_rates) / len(german_success_rates)
                print(f"   📊 Average German CV success: {avg_german:.1f}%")
        
        # Test English CVs
        if english_cvs:
            print(f"\n🇺🇸 Testing English CV extraction:")
            english_success_rates = []
            
            for cv_file in english_cvs:
                filename = os.path.basename(cv_file)
                try:
                    extracted_data = self.extractor.extract_cv_data(cv_file, self.all_fields)
                    
                    field_success = 0
                    for field in self.all_fields:
                        value = extracted_data.get(field, '')
                        if self.validate_extracted_field(field, value):
                            field_success += 1
                    
                    success_rate = (field_success / len(self.all_fields)) * 100
                    english_success_rates.append(success_rate)
                    
                    print(f"   {filename}: {success_rate:.1f}%")
                    
                except Exception as e:
                    print(f"   {filename}: Error - {e}")
            
            if english_success_rates:
                avg_english = sum(english_success_rates) / len(english_success_rates)
                print(f"   📊 Average English CV success: {avg_english:.1f}%")

def main():
    """Run Excel extraction functionality tests"""
    print("🚀 EXCEL EXTRACTION FUNCTIONALITY TEST SUITE")
    print("=" * 70)
    
    test_suite = ExcelExtractionFunctionalityTest()
    
    # Test Excel extraction simulation
    test_suite.test_excel_extraction_simulation()
    
    # Test language-specific extraction
    test_suite.test_language_specific_extraction()
    
    print(f"\n✅ Excel extraction functionality testing completed!")

if __name__ == "__main__":
    main()
