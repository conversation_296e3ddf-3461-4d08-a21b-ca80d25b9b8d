import re
from typing import List, Tuple
from collections import Counter
import math


class CVMatcher:
    def __init__(self):
        # Common stop words to ignore
        self.stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
            'above', 'below', 'between', 'among', 'is', 'are', 'was', 'were', 'be', 'been',
            'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'
        }

    def preprocess_text(self, text: str) -> List[str]:
        """Preprocess text by cleaning and tokenizing"""
        # Convert to lowercase
        text = text.lower()
        
        # Remove special characters and keep only alphanumeric and spaces
        text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text)
        
        # Split into words
        words = text.split()
        
        # Remove stop words and short words
        words = [word for word in words if word not in self.stop_words and len(word) > 2]
        
        return words

    def calculate_tf_idf_similarity(self, job_description: str, cv_content: str) -> float:
        """Calculate TF-IDF based similarity between job description and CV"""
        # Preprocess texts
        job_words = self.preprocess_text(job_description)
        cv_words = self.preprocess_text(cv_content)
        
        # Create vocabulary
        all_words = set(job_words + cv_words)
        
        if not all_words:
            return 0.0
        
        # Calculate term frequencies
        job_tf = Counter(job_words)
        cv_tf = Counter(cv_words)
        
        # Calculate similarity using cosine similarity
        dot_product = 0
        job_magnitude = 0
        cv_magnitude = 0
        
        for word in all_words:
            job_freq = job_tf.get(word, 0)
            cv_freq = cv_tf.get(word, 0)
            
            dot_product += job_freq * cv_freq
            job_magnitude += job_freq ** 2
            cv_magnitude += cv_freq ** 2
        
        if job_magnitude == 0 or cv_magnitude == 0:
            return 0.0
        
        similarity = dot_product / (math.sqrt(job_magnitude) * math.sqrt(cv_magnitude))
        return similarity

    def calculate_keyword_match(self, job_description: str, cv_content: str) -> float:
        """Calculate keyword-based matching score"""
        job_words = set(self.preprocess_text(job_description))
        cv_words = set(self.preprocess_text(cv_content))
        
        if not job_words:
            return 0.0
        
        # Calculate Jaccard similarity
        intersection = job_words.intersection(cv_words)
        union = job_words.union(cv_words)
        
        if not union:
            return 0.0
        
        return len(intersection) / len(union)

    def calculate_skill_match(self, job_description: str, cv_content: str) -> float:
        """Calculate skill-specific matching score"""
        # Define important skill keywords
        technical_skills = [
            'python', 'java', 'javascript', 'react', 'angular', 'vue', 'node',
            'sql', 'mysql', 'postgresql', 'mongodb', 'docker', 'kubernetes',
            'aws', 'azure', 'git', 'linux', 'windows', 'agile', 'scrum'
        ]
        
        soft_skills = [
            'leadership', 'communication', 'teamwork', 'management', 'analysis',
            'problem solving', 'critical thinking', 'project management'
        ]
        
        all_skills = technical_skills + soft_skills
        
        job_lower = job_description.lower()
        cv_lower = cv_content.lower()
        
        job_skills = [skill for skill in all_skills if skill in job_lower]
        cv_skills = [skill for skill in all_skills if skill in cv_lower]
        
        if not job_skills:
            return 0.0
        
        matched_skills = set(job_skills).intersection(set(cv_skills))
        return len(matched_skills) / len(job_skills)

    def calculate_match_score(self, cv_path: str, job_description: str) -> float:
        """Calculate overall match score between CV and job description"""
        try:
            # Extract text from CV
            from cv_extractor import CVDataExtractor
            extractor = CVDataExtractor()
            cv_content = extractor.extract_text_from_file(cv_path)
            
            if not cv_content:
                return 0.0
            
            # Calculate different similarity metrics
            tf_idf_score = self.calculate_tf_idf_similarity(job_description, cv_content)
            keyword_score = self.calculate_keyword_match(job_description, cv_content)
            skill_score = self.calculate_skill_match(job_description, cv_content)
            
            # Weighted combination of scores
            # TF-IDF: 40%, Keywords: 30%, Skills: 30%
            overall_score = (tf_idf_score * 0.4) + (keyword_score * 0.3) + (skill_score * 0.3)
            
            # Convert to percentage
            return overall_score * 100
            
        except Exception as e:
            print(f"Error calculating match score: {e}")
            return 0.0

    def match(self, job_description: str, cv_contents: List[str]) -> List[Tuple[float, str]]:
        """Match multiple CVs against a job description and return sorted results"""
        results = []
        
        for i, cv_content in enumerate(cv_contents):
            # Calculate different similarity metrics
            tf_idf_score = self.calculate_tf_idf_similarity(job_description, cv_content)
            keyword_score = self.calculate_keyword_match(job_description, cv_content)
            skill_score = self.calculate_skill_match(job_description, cv_content)
            
            # Weighted combination of scores
            overall_score = (tf_idf_score * 0.4) + (keyword_score * 0.3) + (skill_score * 0.3)
            
            results.append((overall_score, cv_content))
        
        # Sort by score in descending order
        results.sort(key=lambda x: x[0], reverse=True)
        
        return results

    def get_match_explanation(self, job_description: str, cv_content: str) -> dict:
        """Get detailed explanation of match score"""
        tf_idf_score = self.calculate_tf_idf_similarity(job_description, cv_content)
        keyword_score = self.calculate_keyword_match(job_description, cv_content)
        skill_score = self.calculate_skill_match(job_description, cv_content)
        
        overall_score = (tf_idf_score * 0.4) + (keyword_score * 0.3) + (skill_score * 0.3)
        
        # Find common keywords
        job_words = set(self.preprocess_text(job_description))
        cv_words = set(self.preprocess_text(cv_content))
        common_keywords = job_words.intersection(cv_words)
        
        return {
            'overall_score': overall_score * 100,
            'tf_idf_score': tf_idf_score * 100,
            'keyword_score': keyword_score * 100,
            'skill_score': skill_score * 100,
            'common_keywords': list(common_keywords)[:10],  # Top 10 common keywords
            'explanation': f"Overall match: {overall_score*100:.1f}% "
                          f"(Content similarity: {tf_idf_score*100:.1f}%, "
                          f"Keyword match: {keyword_score*100:.1f}%, "
                          f"Skill match: {skill_score*100:.1f}%)"
        }
