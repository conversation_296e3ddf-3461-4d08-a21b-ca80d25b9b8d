"""
Fast Startup Script for BAUCH HR Application
Shows progress during startup to help with debugging
"""

import sys
import time
import os

def show_progress(message):
    """Show startup progress"""
    print(f"🔄 {message}...")
    sys.stdout.flush()

def main():
    """Fast startup with progress indicators"""
    print("🚀 BAUCH HR Application - Fast Startup")
    print("=" * 50)
    
    show_progress("Checking Python environment")
    time.sleep(0.1)
    
    show_progress("Loading core Flask modules")
    try:
        from flask import Flask
        print("✅ Flask loaded")
    except ImportError as e:
        print(f"❌ Flask import failed: {e}")
        return False
    
    show_progress("Loading database modules")
    try:
        from hr_database_working import HRDatabase
        print("✅ Database modules loaded")
    except ImportError as e:
        print(f"❌ Database import failed: {e}")
        return False
    
    show_progress("Loading CV processing modules")
    try:
        from matcher import CVMatcher
        from cv_extractor import CVDataExtractor
        print("✅ CV processing modules loaded")
    except ImportError as e:
        print(f"❌ CV processing import failed: {e}")
        return False
    
    show_progress("Loading email service")
    try:
        from email_service import EmailService
        print("✅ Email service loaded")
    except ImportError as e:
        print(f"❌ Email service import failed: {e}")
        return False
    
    show_progress("Starting main application")
    print("⏳ This may take 30-60 seconds due to heavy libraries...")
    
    # Import and run the main application
    try:
        import app_german
        print("✅ Application started successfully!")
        print("🌐 Access at: http://127.0.0.1:5000")
        print("🔧 Debugger PIN will be shown above")
        return True
    except Exception as e:
        print(f"❌ Application startup failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💡 Troubleshooting Tips:")
        print("1. Make sure all dependencies are installed")
        print("2. Check that all required files exist")
        print("3. Try running: pip install -r requirements.txt")
        print("4. Check for any missing modules")
        sys.exit(1)
