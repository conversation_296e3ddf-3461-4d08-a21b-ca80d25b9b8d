#!/usr/bin/env python3
"""
Analyze CV content to understand ranking issues
"""

from hr_database_working import HRDatabase

def analyze_cvs():
    """Analyze CV content"""
    print("=== ANALYZING CV CONTENT ===")
    
    try:
        db = HRDatabase()
        cvs = db.get_cvs_for_job('CNC Fräser')
        
        for cv in cvs:
            print(f"\n📄 {cv.candidate_name}:")
            print("-" * 40)
            
            # Show first 500 characters
            content_preview = cv.content[:500].replace('\n', ' ')
            print(f"Content preview: {content_preview}...")
            
            # Check for key terms
            content_lower = cv.content.lower()
            
            # Check for CNC terms
            cnc_terms = ['cnc', 'fanuc', 'heidenhain', 'fräsen', 'drehen', 'programmieren']
            found_cnc = [term for term in cnc_terms if term in content_lower]
            print(f"CNC terms found: {found_cnc}")
            
            # Check for years
            import re
            years = re.findall(r'\b(19|20)\d{2}\b', content_lower)
            print(f"Years mentioned: {years}")
            
            # Check for recent years
            recent = '2023' in content_lower or '2024' in content_lower
            print(f"Has recent experience: {recent}")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_cvs()
