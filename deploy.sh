#!/bin/bash

# BAUCH HR Management System - Deployment Script
# This script helps deploy the application to a production server

set -e  # Exit on any error

echo "🚀 BAUCH HR Management System - Deployment Script"
echo "=================================================="

# Configuration
APP_NAME="bauch-hr"
APP_DIR="/var/www/$APP_NAME"
SERVICE_NAME="$APP_NAME"
NGINX_SITE="$APP_NAME"
PYTHON_VERSION="3.8"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "This script should not be run as root for security reasons"
        exit 1
    fi
}

# Install system dependencies
install_system_deps() {
    log_info "Installing system dependencies..."
    
    sudo apt update
    sudo apt install -y \
        python3 \
        python3-pip \
        python3-venv \
        nginx \
        supervisor \
        git \
        curl \
        build-essential \
        python3-dev \
        libffi-dev \
        libssl-dev
    
    log_success "System dependencies installed"
}

# Setup application directory
setup_app_directory() {
    log_info "Setting up application directory..."
    
    sudo mkdir -p $APP_DIR
    sudo chown $USER:$USER $APP_DIR
    
    # Copy application files
    cp -r . $APP_DIR/
    cd $APP_DIR
    
    log_success "Application directory setup complete"
}

# Setup Python virtual environment
setup_python_env() {
    log_info "Setting up Python virtual environment..."
    
    cd $APP_DIR
    python3 -m venv venv
    source venv/bin/activate
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install Python dependencies
    pip install -r requirements.txt
    
    # Install additional production dependencies
    pip install gunicorn supervisor
    
    # Download spaCy models
    python -m spacy download en_core_web_sm
    python -m spacy download de_core_news_sm
    
    log_success "Python environment setup complete"
}

# Setup database
setup_database() {
    log_info "Setting up database..."
    
    cd $APP_DIR
    source venv/bin/activate
    
    # Create database and admin user
    python create_admin_user.py
    
    log_success "Database setup complete"
}

# Create Gunicorn configuration
create_gunicorn_config() {
    log_info "Creating Gunicorn configuration..."
    
    cat > $APP_DIR/gunicorn.conf.py << EOF
# Gunicorn configuration for BAUCH HR Management System

bind = "127.0.0.1:8000"
workers = 2
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
user = "$USER"
group = "$USER"
tmp_upload_dir = None
secure_scheme_headers = {
    'X-FORWARDED-PROTOCOL': 'ssl',
    'X-FORWARDED-PROTO': 'https',
    'X-FORWARDED-SSL': 'on'
}
forwarded_allow_ips = '*'
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'
accesslog = "$APP_DIR/logs/gunicorn_access.log"
errorlog = "$APP_DIR/logs/gunicorn_error.log"
loglevel = "info"
capture_output = True
enable_stdio_inheritance = True
EOF
    
    # Create logs directory
    mkdir -p $APP_DIR/logs
    
    log_success "Gunicorn configuration created"
}

# Create systemd service
create_systemd_service() {
    log_info "Creating systemd service..."
    
    sudo tee /etc/systemd/system/$SERVICE_NAME.service > /dev/null << EOF
[Unit]
Description=BAUCH HR Management System
After=network.target

[Service]
Type=exec
User=$USER
Group=$USER
WorkingDirectory=$APP_DIR
Environment=PATH=$APP_DIR/venv/bin
Environment=FLASK_ENV=production
Environment=SECRET_KEY=$(openssl rand -hex 32)
ExecStart=$APP_DIR/venv/bin/gunicorn --config $APP_DIR/gunicorn.conf.py wsgi:application
ExecReload=/bin/kill -s HUP \$MAINPID
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF
    
    sudo systemctl daemon-reload
    sudo systemctl enable $SERVICE_NAME
    
    log_success "Systemd service created"
}

# Create Nginx configuration
create_nginx_config() {
    log_info "Creating Nginx configuration..."
    
    sudo tee /etc/nginx/sites-available/$NGINX_SITE > /dev/null << EOF
server {
    listen 80;
    server_name _;  # Replace with your domain
    
    client_max_body_size 16M;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_redirect off;
        proxy_buffering off;
    }
    
    location /static {
        alias $APP_DIR/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location /uploads {
        alias $APP_DIR/uploads;
        expires 1h;
        add_header Cache-Control "private";
    }
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
}
EOF
    
    # Enable the site
    sudo ln -sf /etc/nginx/sites-available/$NGINX_SITE /etc/nginx/sites-enabled/
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # Test Nginx configuration
    sudo nginx -t
    
    log_success "Nginx configuration created"
}

# Setup SSL with Let's Encrypt (optional)
setup_ssl() {
    read -p "Do you want to setup SSL with Let's Encrypt? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "Setting up SSL with Let's Encrypt..."
        
        sudo apt install -y certbot python3-certbot-nginx
        
        read -p "Enter your domain name: " DOMAIN_NAME
        
        sudo certbot --nginx -d $DOMAIN_NAME
        
        log_success "SSL setup complete"
    fi
}

# Start services
start_services() {
    log_info "Starting services..."
    
    sudo systemctl start $SERVICE_NAME
    sudo systemctl restart nginx
    
    log_success "Services started"
}

# Setup firewall
setup_firewall() {
    log_info "Setting up firewall..."
    
    sudo ufw allow 22/tcp
    sudo ufw allow 80/tcp
    sudo ufw allow 443/tcp
    sudo ufw --force enable
    
    log_success "Firewall configured"
}

# Main deployment function
deploy() {
    log_info "Starting deployment process..."
    
    check_root
    install_system_deps
    setup_app_directory
    setup_python_env
    setup_database
    create_gunicorn_config
    create_systemd_service
    create_nginx_config
    setup_ssl
    start_services
    setup_firewall
    
    log_success "Deployment completed successfully!"
    echo
    echo "🎉 BAUCH HR Management System is now deployed!"
    echo "📝 Next steps:"
    echo "   1. Update the server_name in /etc/nginx/sites-available/$NGINX_SITE"
    echo "   2. Configure your domain DNS to point to this server"
    echo "   3. Access your application at http://your-domain.com"
    echo "   4. Login with the admin credentials you created"
    echo
    echo "📊 Service management commands:"
    echo "   sudo systemctl status $SERVICE_NAME    # Check service status"
    echo "   sudo systemctl restart $SERVICE_NAME   # Restart application"
    echo "   sudo systemctl restart nginx          # Restart web server"
    echo "   sudo journalctl -u $SERVICE_NAME -f   # View application logs"
}

# Run deployment
deploy
