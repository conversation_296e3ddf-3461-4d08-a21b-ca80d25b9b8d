# Listmonk Configuration - Secure Template
# Copy this to config.toml and update with your actual values

[app]
# Interface and port where the app will run its webserver
# Use localhost for security - only local connections allowed
address = "localhost:9000"

# Admin credentials - CHANGE THESE IMMEDIATELY
admin_username = "taha"
admin_password = "taha135"

# Database configuration
[db]
host = "localhost"
port = 5432
user = "postgres"
password = ""
database = "listmonk"
ssl_mode = "disable"  # Disable SSL for local development
max_open = 25
max_idle = 25
max_lifetime = "300s"

# Optional space separated Postgres DSN params
params = "sslmode=disable"

# SMTP configuration - THIS IS WHERE YOU CONFIGURE YOUR EMAIL CREDENTIALS
[smtp]
# Your email provider's SMTP server
host = "smtp.gmail.com"  # Change to your SMTP server
port = 587
auth_protocol = "plain"

# YOUR EMAIL CREDENTIALS - These are the actual email account credentials
username = "<EMAIL>"  # Your email address
password = "ibitlgbuhqfluibs"     # Your email password or app password

# SMTP settings
hello_hostname = ""
max_conns = 10
max_msg_retries = 2
idle_timeout = "15s"
wait_timeout = "5s"
tls_enabled = true
tls_skip_verify = false

# Email headers
[smtp.headers]
# Set the "From" name that appears in emails
"Reply-To" = ["<EMAIL>"]

# File upload settings
[upload]
provider = "filesystem"
filesystem_upload_path = "uploads"
filesystem_upload_uri = "/uploads"

# Privacy and compliance settings
[privacy]
individual_tracking = false
unsubscribe_header = true
allow_blocklist = true
allow_export = true
allow_wipe = true
exportable = ["profile", "subscriptions", "campaign_views", "link_clicks"]

# Security settings
[security]
enable_captcha = false
captcha_key = ""
captcha_secret = ""

# Bounce processing
[bounce]
enabled = true
webhooks_enabled = false

# Performance settings
[performance]
concurrency = 10
message_rate = 10  # messages per second
batch_size = 1000
max_send_errors = 1999

# Logging
[log]
level = "info"
