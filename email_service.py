"""
Email Service for HR Management System
Handles sending bulk emails to applicants
"""

import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from typing import List, Dict, Optional
from datetime import datetime
import logging
from threading import Thread

class EmailService:
    def __init__(self, mail_server=None, mail_port=None, mail_username=None, 
                 mail_password=None, mail_use_tls=True, default_sender=None):
        """Initialize email service with configuration"""
        self.mail_server = mail_server or os.environ.get('MAIL_SERVER')
        self.mail_port = mail_port or int(os.environ.get('MAIL_PORT', 587))
        self.mail_username = mail_username or os.environ.get('MAIL_USERNAME')
        self.mail_password = mail_password or os.environ.get('MAIL_PASSWORD')
        self.mail_use_tls = mail_use_tls
        self.default_sender = default_sender or os.environ.get('MAIL_DEFAULT_SENDER')
        self.logger = logging.getLogger(__name__)
    
    def send_email(self, recipient: str, subject: str, body_html: str, 
                   body_text: str = None, sender: str = None) -> bool:
        """Send a single email"""
        if not self.mail_server or not self.mail_username or not self.mail_password:
            self.logger.error("Email configuration incomplete")
            return False
            
        sender = sender or self.default_sender
        if not sender:
            self.logger.error("No sender email specified")
            return False
            
        try:
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = sender
            msg['To'] = recipient
            
            # Add text part if provided, otherwise create from HTML
            if body_text:
                msg.attach(MIMEText(body_text, 'plain'))
            else:
                # Simple HTML to text conversion
                text = body_html.replace('<br>', '\n').replace('<p>', '\n').replace('</p>', '\n')
                text = ' '.join(text.split())  # Normalize whitespace
                msg.attach(MIMEText(text, 'plain'))
                
            # Add HTML part
            msg.attach(MIMEText(body_html, 'html'))
            
            # Connect to server and send
            with smtplib.SMTP(self.mail_server, self.mail_port) as server:
                if self.mail_use_tls:
                    server.starttls()
                server.login(self.mail_username, self.mail_password)
                server.send_message(msg)
                
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send email to {recipient}: {str(e)}")
            return False
    
    def send_bulk_emails(self, recipients: List[Dict], subject: str, 
                         template: str, template_vars: Dict = None,
                         async_send: bool = True) -> Dict:
        """
        Send emails to multiple recipients
        
        Args:
            recipients: List of dicts with 'email' and other template variables
            subject: Email subject
            template: HTML template string with {variable} placeholders
            template_vars: Global template variables for all emails
            async_send: Whether to send emails asynchronously
            
        Returns:
            Dict with success and failure counts
        """
        if not recipients:
            return {'success': 0, 'failed': 0}
            
        template_vars = template_vars or {}
        results = {'success': 0, 'failed': 0, 'recipients': []}
        
        def send_all_emails():
            for recipient in recipients:
                if 'email' not in recipient:
                    results['failed'] += 1
                    continue
                    
                # Combine global and recipient-specific template variables
                email_vars = template_vars.copy()
                email_vars.update(recipient)
                
                # Format template with variables
                try:
                    body_html = template.format(**email_vars)
                    success = self.send_email(
                        recipient=recipient['email'],
                        subject=subject,
                        body_html=body_html
                    )
                    
                    if success:
                        results['success'] += 1
                        results['recipients'].append({
                            'email': recipient['email'],
                            'status': 'sent'
                        })
                    else:
                        results['failed'] += 1
                        results['recipients'].append({
                            'email': recipient['email'],
                            'status': 'failed'
                        })
                except Exception as e:
                    self.logger.error(f"Error preparing email for {recipient['email']}: {str(e)}")
                    results['failed'] += 1
                    results['recipients'].append({
                        'email': recipient['email'],
                        'status': 'failed',
                        'error': str(e)
                    })
        
        if async_send:
            # Start a background thread to send emails
            thread = Thread(target=send_all_emails)
            thread.daemon = True
            thread.start()
            return {'status': 'sending', 'total': len(recipients)}
        else:
            # Send synchronously and return results
            send_all_emails()
            return results
    
    def get_default_templates(self, language: str = 'en') -> Dict[str, str]:
        """Get default email templates for common scenarios"""
        templates = {
            'application_received': {
                'en': """
                <html>
                <body>
                    <h2>Application Received</h2>
                    <p>Dear {name},</p>
                    <p>Thank you for applying to the <b>{job_title}</b> position at our company.</p>
                    <p>We have received your application and will review it shortly.</p>
                    <p>Best regards,<br>HR Team</p>
                </body>
                </html>
                """,
                'de': """
                <html>
                <body>
                    <h2>Bewerbung Eingegangen</h2>
                    <p>Sehr geehrte(r) {name},</p>
                    <p>vielen Dank für Ihre Bewerbung für die Position <b>{job_title}</b> in unserem Unternehmen.</p>
                    <p>Wir haben Ihre Bewerbung erhalten und werden sie in Kürze prüfen.</p>
                    <p>Mit freundlichen Grüßen,<br>HR-Team</p>
                </body>
                </html>
                """
            },
            'interview_invitation': {
                'en': """
                <html>
                <body>
                    <h2>Interview Invitation</h2>
                    <p>Dear {name},</p>
                    <p>We are pleased to invite you to an interview for the <b>{job_title}</b> position.</p>
                    <p>Date: {interview_date}<br>Time: {interview_time}<br>Location: {interview_location}</p>
                    <p>Please confirm your attendance by replying to this email.</p>
                    <p>Best regards,<br>HR Team</p>
                </body>
                </html>
                """,
                'de': """
                <html>
                <body>
                    <h2>Einladung zum Vorstellungsgespräch</h2>
                    <p>Sehr geehrte(r) {name},</p>
                    <p>wir freuen uns, Sie zu einem Vorstellungsgespräch für die Position <b>{job_title}</b> einzuladen.</p>
                    <p>Datum: {interview_date}<br>Zeit: {interview_time}<br>Ort: {interview_location}</p>
                    <p>Bitte bestätigen Sie Ihre Teilnahme durch eine Antwort auf diese E-Mail.</p>
                    <p>Mit freundlichen Grüßen,<br>HR-Team</p>
                </body>
                </html>
                """
            },
            'status_update': {
                'en': """
                <html>
                <body>
                    <h2>Application Status Update</h2>
                    <p>Dear {name},</p>
                    <p>We would like to inform you that your application for the <b>{job_title}</b> position has been {status}.</p>
                    <p>{additional_info}</p>
                    <p>Best regards,<br>HR Team</p>
                </body>
                </html>
                """,
                'de': """
                <html>
                <body>
                    <h2>Aktualisierung des Bewerbungsstatus</h2>
                    <p>Sehr geehrte(r) {name},</p>
                    <p>wir möchten Sie darüber informieren, dass Ihre Bewerbung für die Position <b>{job_title}</b> {status} wurde.</p>
                    <p>{additional_info}</p>
                    <p>Mit freundlichen Grüßen,<br>HR-Team</p>
                </body>
                </html>
                """
            }
        }
        
        return {k: templates[k].get(language, templates[k]['en']) for k in templates}