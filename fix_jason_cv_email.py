#!/usr/bin/env python3
"""
🔧 Fix Jason CV Email
====================
Add a real email address to <PERSON>'s CV for testing
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import required modules
from hr_database_working import HRDatabase

def fix_jason_cv_email():
    """Add email to <PERSON>'s CV"""
    print("🔧 Fixing Jason CV Email Address")
    print("=" * 40)
    
    # Initialize database
    hr_db = HRDatabase()
    
    # Find Jason's CV
    jobs = hr_db.get_all_jobs()
    jason_cv = None
    
    for job in jobs:
        cvs = hr_db.get_cvs_for_job(job.title)
        for cv in cvs:
            if 'jason' in cv.filename.lower():
                jason_cv = cv
                break
        if jason_cv:
            break
    
    if not jason_cv:
        print("❌ <PERSON>'s CV not found")
        return
    
    print(f"✅ Found Jason's CV: {jason_cv.filename}")
    print(f"👤 Candidate: {getattr(jason_cv, 'candidate_name', 'Unknown')}")
    
    # Show current content
    print(f"\n📝 Current CV content (first 300 chars):")
    print("-" * 30)
    print(jason_cv.content[:300] if jason_cv.content else "No content")
    print("-" * 30)
    
    # Fix the email in the content
    if jason_cv.content:
        # Replace the empty email field with a real email
        updated_content = jason_cv.content.replace(
            "E-Mail: \n", 
            "E-Mail: <EMAIL>\n"
        )
        
        # Also try other variations
        updated_content = updated_content.replace(
            "E-Mail:", 
            "E-Mail: <EMAIL>"
        )
        
        # If still no email, add it after the name
        if "<EMAIL>" not in updated_content:
            updated_content = updated_content.replace(
                "Name: Max Mustermann",
                "Name: Max Mustermann\nE-Mail: <EMAIL>"
            )
        
        print(f"\n📝 Updated CV content (first 300 chars):")
        print("-" * 30)
        print(updated_content[:300])
        print("-" * 30)
        
        # Update the CV in database
        try:
            session = hr_db.Session()
            cv_record = session.query(hr_db.CV).filter(hr_db.CV.id == jason_cv.id).first()
            if cv_record:
                cv_record.content = updated_content
                session.commit()
                print("✅ Jason's CV updated with email address!")
                print("📧 Email added: <EMAIL>")
            else:
                print("❌ Could not find CV record in database")
            session.close()
        except Exception as e:
            print(f"❌ Error updating CV: {e}")
    else:
        print("❌ CV has no content to update")

def test_jason_email_extraction():
    """Test email extraction for Jason's CV after fix"""
    print("\n🧪 Testing Email Extraction for Jason's CV")
    print("-" * 40)
    
    from app_german import extract_email_from_cv_content
    
    # Get updated CV
    hr_db = HRDatabase()
    jobs = hr_db.get_all_jobs()
    jason_cv = None
    
    for job in jobs:
        cvs = hr_db.get_cvs_for_job(job.title)
        for cv in cvs:
            if 'jason' in cv.filename.lower():
                jason_cv = cv
                break
        if jason_cv:
            break
    
    if jason_cv:
        extracted_email = extract_email_from_cv_content(jason_cv.content)
        if extracted_email:
            print(f"✅ Email extraction successful: {extracted_email}")
        else:
            print("❌ Email extraction still failed")
            print("📝 CV content:")
            print(jason_cv.content[:500] if jason_cv.content else "No content")
    else:
        print("❌ Jason's CV not found")

def main():
    """Main function"""
    fix_jason_cv_email()
    test_jason_email_extraction()
    
    print("\n🎯 Next Steps:")
    print("1. Now all 7 CVs should have extractable email addresses")
    print("2. Test bulk email again to verify all CVs receive emails")
    print("3. Use the HR app email interface to send to all CVs")

if __name__ == "__main__":
    main()
