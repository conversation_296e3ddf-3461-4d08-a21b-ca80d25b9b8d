#!/usr/bin/env python3
"""
Universal Domain-Specific CV Matcher
Based on conservative, domain-focused evaluation principles
Works across all job domains with adaptive weighting
"""

import re
from typing import List, Dict, Tuple, Any
from dataclasses import dataclass
from datetime import datetime

@dataclass
class JobRequirements:
    """Extracted job requirements"""
    domain: str
    required_qualifications: List[str]
    domain_knowledge: List[str]
    specific_skills: List[str]
    production_type: List[str]
    leadership_required: bool
    min_experience_years: int
    core_competencies: List[str]  # NEW: Most critical skills for the role
    nice_to_have: List[str]       # NEW: Bonus skills

@dataclass
class CandidateProfile:
    """Candidate evaluation profile"""
    name: str
    relevant_experience_years: int
    recent_experience: List[str]  # Last 5 years
    domain_skills: List[str]
    certifications: List[str]
    education_level: str
    gaps_mismatches: List[str]
    irrelevant_experience: List[str]
    core_competency_matches: List[str]  # NEW: Matches to core job requirements

class UniversalDomainMatcher:
    """
    Universal domain-specific matcher that prioritizes exact job relevance
    Adapts to any domain while maintaining conservative evaluation principles
    """

    def __init__(self):
        # Universal domain knowledge patterns - expandable for any domain
        self.domain_patterns = {
            # Manufacturing/CNC domain
            'manufacturing': {
                'cnc_programming': [
                    'cnc programmierung', 'cnc-programmierung', 'cnc programmierer',
                    'fanuc', 'heidenhain', 'siemens', 'sinumerik', 'mazak', 'okuma'
                ],
                'machining': [
                    'zerspanung', 'zerspanungsmechaniker', 'drehen', 'fräsen',
                    'bearbeitung', 'spanende bearbeitung', 'maschinenbedienung',
                    'werkzeugmaschinen', 'drehmaschine', 'fräsmaschine'
                ],
                'quality_control': [
                    'qualitätskontrolle', 'qualitätssicherung', 'qs', 'messtechnik',
                    'koordinatenmesstechnik', 'kmg', 'mitutoyo', 'quindos', 'zeiss',
                    'erstmuster', 'vda', 'ppap', 'spc', 'msa'
                ],
                'production': [
                    'serienfertigung', 'serienproduktion', 'kleinserien',
                    'prozessoptimierung', 'fertigungsoptimierung', 'lean',
                    'kaizen', 'kontinuierliche verbesserung', 'kvp'
                ],
                'vocational_training': [
                    'zerspanungsmechaniker', 'industriemechaniker', 'feinwerkmechaniker',
                    'werkzeugmechaniker', 'ausbildung', 'ihk', 'berufsausbildung'
                ]
            },

            # Software development domain
            'software': {
                'programming': [
                    'java', 'python', 'javascript', 'c++', 'c#', 'typescript',
                    'programmierung', 'softwareentwicklung', 'coding'
                ],
                'frameworks': [
                    'spring', 'spring boot', 'react', 'angular', 'vue', 'django',
                    'flask', 'express', 'node.js', '.net'
                ],
                'databases': [
                    'sql', 'postgresql', 'mysql', 'mongodb', 'oracle',
                    'datenbank', 'database', 'nosql'
                ],
                'devops': [
                    'docker', 'kubernetes', 'jenkins', 'git', 'ci/cd',
                    'aws', 'azure', 'cloud', 'microservices'
                ],
                'methodologies': [
                    'agile', 'scrum', 'kanban', 'test-driven development',
                    'tdd', 'bdd', 'continuous integration'
                ]
            },

            # Finance domain
            'finance': {
                'accounting': [
                    'buchhaltung', 'bilanzierung', 'jahresabschluss', 'hgb',
                    'ifrs', 'gaap', 'steuerrecht', 'controlling'
                ],
                'analysis': [
                    'finanzanalyse', 'financial analysis', 'reporting',
                    'budgetierung', 'forecasting', 'variance analysis'
                ],
                'compliance': [
                    'compliance', 'risk management', 'audit', 'sox',
                    'mifid', 'basel', 'regulatory'
                ],
                'systems': [
                    'sap', 'oracle financials', 'excel', 'power bi',
                    'tableau', 'sql', 'python'
                ]
            },

            # Healthcare domain
            'healthcare': {
                'clinical': [
                    'patient care', 'clinical', 'medical', 'diagnosis',
                    'treatment', 'therapy', 'nursing'
                ],
                'medical_tech': [
                    'medical devices', 'healthcare technology', 'emr',
                    'electronic health records', 'telemedicine'
                ],
                'compliance': [
                    'hipaa', 'gdpr', 'medical compliance', 'quality assurance',
                    'clinical trials', 'fda'
                ]
            },

            # Sales/Marketing domain
            'sales': {
                'sales_skills': [
                    'sales', 'vertrieb', 'verkauf', 'customer relationship',
                    'crm', 'lead generation', 'closing'
                ],
                'marketing': [
                    'marketing', 'digital marketing', 'seo', 'sem',
                    'social media', 'content marketing', 'brand management'
                ],
                'tools': [
                    'salesforce', 'hubspot', 'google analytics',
                    'adobe creative suite', 'mailchimp'
                ]
            }
        }

        # Domain-specific weighting matrices (based on your CNC example)
        self.domain_weights = {
            'manufacturing': {
                'direct_experience': 0.60,  # 60% - Core competency in domain
                'quality_process': 0.25,    # 25% - Quality & process optimization
                'education': 0.10,          # 10% - Vocational training
                'leadership': 0.05          # 5% - Leadership potential
            },
            'software': {
                'technical_skills': 0.50,   # 50% - Programming & technical skills
                'experience': 0.30,         # 30% - Relevant experience
                'education': 0.15,          # 15% - Formal education
                'methodologies': 0.05       # 5% - Agile/DevOps practices
            },
            'finance': {
                'domain_knowledge': 0.45,   # 45% - Financial expertise
                'experience': 0.35,         # 35% - Relevant experience
                'education': 0.15,          # 15% - Finance education/certification
                'tools': 0.05              # 5% - Software tools
            },
            'healthcare': {
                'clinical_experience': 0.50, # 50% - Direct patient/clinical experience
                'compliance': 0.25,         # 25% - Regulatory compliance
                'education': 0.20,          # 20% - Medical education/certification
                'technology': 0.05          # 5% - Healthcare technology
            },
            'sales': {
                'sales_experience': 0.50,   # 50% - Direct sales experience
                'results': 0.30,            # 30% - Proven results/achievements
                'industry_knowledge': 0.15, # 15% - Industry-specific knowledge
                'tools': 0.05              # 5% - CRM/sales tools
            },
            'general': {
                'experience': 0.40,         # 40% - General experience
                'skills': 0.35,            # 35% - Transferable skills
                'education': 0.20,         # 20% - Education
                'adaptability': 0.05       # 5% - Learning ability
            }
        }

    def extract_job_requirements(self, job_description: str) -> JobRequirements:
        """Extract structured requirements from job description using universal approach"""
        job_lower = job_description.lower()

        # Detect domain using comprehensive approach
        domain = self._detect_domain_universal(job_lower)

        # Extract requirements using universal method
        return self._extract_universal_requirements(job_description, job_lower, domain)

    def _detect_domain_universal(self, job_description: str) -> str:
        """Universal domain detection that can identify any domain"""
        domain_scores = {}

        # Score each domain based on keyword presence
        for domain_name, patterns in self.domain_patterns.items():
            score = 0
            for category, keywords in patterns.items():
                for keyword in keywords:
                    if keyword in job_description:
                        # Weight core competencies higher
                        if category in ['cnc_programming', 'programming', 'accounting', 'clinical']:
                            score += 3
                        else:
                            score += 1
            domain_scores[domain_name] = score

        # Return domain with highest score, or 'general' if no clear match
        if not domain_scores or max(domain_scores.values()) == 0:
            return 'general'

        return max(domain_scores, key=domain_scores.get)

    def _extract_universal_requirements(self, job_desc: str, job_lower: str, domain: str) -> JobRequirements:
        """Universal requirements extraction that works for any domain"""

        # Extract core competencies (most critical skills)
        core_competencies = self._extract_core_competencies(job_lower, domain)

        # Extract required qualifications
        qualifications = self._extract_qualifications(job_lower, domain)

        # Extract domain knowledge requirements
        domain_knowledge = self._extract_domain_knowledge(job_lower, domain)

        # Extract specific skills/tools
        specific_skills = self._extract_specific_skills(job_lower, domain)

        # Extract production/work type
        work_type = self._extract_work_type(job_lower, domain)

        # Check for leadership requirements
        leadership_required = self._check_leadership_requirements(job_lower)

        # Extract experience requirements
        min_experience = self._extract_experience_requirements(job_lower)

        # Extract nice-to-have skills
        nice_to_have = self._extract_nice_to_have(job_lower, domain)

        return JobRequirements(
            domain=domain,
            required_qualifications=qualifications,
            domain_knowledge=domain_knowledge,
            specific_skills=specific_skills,
            production_type=work_type,
            leadership_required=leadership_required,
            min_experience_years=min_experience,
            core_competencies=core_competencies,
            nice_to_have=nice_to_have
        )

    def _extract_core_competencies(self, job_lower: str, domain: str) -> List[str]:
        """Extract the most critical skills for the role"""
        core_competencies = []

        if domain in self.domain_patterns:
            patterns = self.domain_patterns[domain]

            # Prioritize certain categories as core competencies
            core_categories = {
                'manufacturing': ['cnc_programming', 'machining', 'quality_control'],
                'software': ['programming', 'frameworks'],
                'finance': ['accounting', 'analysis'],
                'healthcare': ['clinical', 'medical_tech'],
                'sales': ['sales_skills', 'marketing']
            }

            priority_categories = core_categories.get(domain, list(patterns.keys())[:2])

            for category in priority_categories:
                if category in patterns:
                    for skill in patterns[category]:
                        if skill in job_lower:
                            core_competencies.append(skill)

        return core_competencies

    def _extract_qualifications(self, job_lower: str, domain: str) -> List[str]:
        """Extract required qualifications"""
        qualifications = []

        # Education requirements
        education_patterns = [
            'bachelor', 'master', 'diploma', 'degree', 'studium',
            'ausbildung', 'lehre', 'berufsausbildung', 'ihk'
        ]

        for pattern in education_patterns:
            if pattern in job_lower:
                qualifications.append(f'education_{pattern}')

        # Experience requirements
        if 'berufserfahrung' in job_lower or 'experience' in job_lower:
            qualifications.append('professional_experience')

        # Domain-specific qualifications
        if domain == 'manufacturing':
            manufacturing_quals = ['zerspanungsmechaniker', 'industriemechaniker', 'feinwerkmechaniker']
            for qual in manufacturing_quals:
                if qual in job_lower:
                    qualifications.append(f'vocational_{qual}')

        return qualifications

    def _extract_domain_knowledge(self, job_lower: str, domain: str) -> List[str]:
        """Extract domain knowledge requirements"""
        domain_knowledge = []

        if domain in self.domain_patterns:
            for category, patterns in self.domain_patterns[domain].items():
                if any(pattern in job_lower for pattern in patterns):
                    domain_knowledge.append(category)

        return domain_knowledge

    def _extract_specific_skills(self, job_lower: str, domain: str) -> List[str]:
        """Extract specific skills/tools mentioned"""
        specific_skills = []

        # Common skill indicators across domains
        skill_indicators = [
            'fanuc', 'heidenhain', 'siemens',  # Manufacturing
            'java', 'python', 'react', 'spring',  # Software
            'sap', 'excel', 'oracle',  # Finance/General
            'salesforce', 'hubspot'  # Sales
        ]

        for skill in skill_indicators:
            if skill in job_lower:
                specific_skills.append(skill)

        return specific_skills

    def _extract_work_type(self, job_lower: str, domain: str) -> List[str]:
        """Extract work/production type"""
        work_type = []

        type_patterns = {
            'manufacturing': ['serienfertigung', 'kleinserien', 'prototyping'],
            'software': ['web development', 'mobile', 'backend', 'frontend'],
            'finance': ['reporting', 'analysis', 'audit'],
            'sales': ['b2b', 'b2c', 'inside sales', 'field sales']
        }

        if domain in type_patterns:
            for pattern in type_patterns[domain]:
                if pattern in job_lower:
                    work_type.append(pattern)

        return work_type

    def _check_leadership_requirements(self, job_lower: str) -> bool:
        """Check if leadership is required"""
        leadership_indicators = [
            'leitung', 'führung', 'team', 'schichtleitung', 'management',
            'lead', 'supervisor', 'manager', 'director'
        ]
        return any(indicator in job_lower for indicator in leadership_indicators)

    def _extract_experience_requirements(self, job_lower: str) -> int:
        """Extract minimum experience requirement"""
        exp_patterns = [
            r'(\d+)\+?\s*jahre?\s*(?:berufserfahrung|erfahrung)',
            r'mindestens\s+(\d+)\s+jahre?',
            r'(\d+)\+?\s*years?\s*(?:of\s*)?(?:experience|exp)'
        ]

        for pattern in exp_patterns:
            match = re.search(pattern, job_lower)
            if match:
                return int(match.group(1))

        return 2  # Default minimum

    def _extract_nice_to_have(self, job_lower: str, domain: str) -> List[str]:
        """Extract nice-to-have skills"""
        nice_to_have = []

        # Look for bonus skills mentioned after "nice to have", "von vorteil", etc.
        bonus_indicators = [
            'nice to have', 'von vorteil', 'wünschenswert', 'plus',
            'bonus', 'additional', 'preferred'
        ]

        for indicator in bonus_indicators:
            if indicator in job_lower:
                # Extract text after the indicator
                start_idx = job_lower.find(indicator)
                if start_idx != -1:
                    text_after = job_lower[start_idx:start_idx+200]  # Next 200 chars
                    # Extract skills from this text
                    if domain in self.domain_patterns:
                        for category, patterns in self.domain_patterns[domain].items():
                            for pattern in patterns:
                                if pattern in text_after:
                                    nice_to_have.append(pattern)

        return nice_to_have

    def _extract_manufacturing_requirements(self, job_desc: str, job_lower: str) -> JobRequirements:
        """Extract manufacturing-specific requirements"""
        
        # Required qualifications
        qualifications = []
        if any(term in job_lower for term in ['zerspanungsmechaniker', 'industriemechaniker']):
            qualifications.append('vocational_training_machining')
        if 'berufserfahrung' in job_lower:
            qualifications.append('professional_experience')
        
        # Domain knowledge
        domain_knowledge = []
        for category, patterns in self.manufacturing_patterns.items():
            if any(pattern in job_lower for pattern in patterns):
                domain_knowledge.append(category)
        
        # Specific skills (machines/software)
        specific_skills = []
        skill_patterns = ['fanuc', 'heidenhain', 'siemens', 'mitutoyo', 'quindos', 'zeiss']
        for skill in skill_patterns:
            if skill in job_lower:
                specific_skills.append(skill)
        
        # Production type
        production_type = []
        if 'serienfertigung' in job_lower or 'serienproduktion' in job_lower:
            production_type.append('series_production')
        if 'kleinserien' in job_lower:
            production_type.append('small_batch')
        
        # Leadership
        leadership_required = any(term in job_lower for term in 
                                ['leitung', 'führung', 'team', 'schichtleitung'])
        
        # Experience years
        exp_match = re.search(r'(\d+)\+?\s*jahre?\s*(?:berufserfahrung|erfahrung)', job_lower)
        min_experience = int(exp_match.group(1)) if exp_match else 2
        
        return JobRequirements(
            domain='manufacturing',
            required_qualifications=qualifications,
            domain_knowledge=domain_knowledge,
            specific_skills=specific_skills,
            production_type=production_type,
            leadership_required=leadership_required,
            min_experience_years=min_experience
        )
    
    def _extract_software_requirements(self, job_desc: str, job_lower: str) -> JobRequirements:
        """Extract software-specific requirements"""
        # Similar structure for software jobs
        return JobRequirements(
            domain='software',
            required_qualifications=['computer_science_degree'],
            domain_knowledge=['programming'],
            specific_skills=[],
            production_type=[],
            leadership_required=False,
            min_experience_years=2
        )
    
    def _extract_generic_requirements(self, job_desc: str, job_lower: str) -> JobRequirements:
        """Extract generic requirements"""
        return JobRequirements(
            domain='general',
            required_qualifications=[],
            domain_knowledge=[],
            specific_skills=[],
            production_type=[],
            leadership_required=False,
            min_experience_years=1
        )
    
    def evaluate_candidate(self, cv_content: str, job_requirements: JobRequirements) -> CandidateProfile:
        """Universal candidate evaluation against job requirements"""
        cv_lower = cv_content.lower()

        # Extract name
        name_match = re.search(r'^([A-Z][a-z]+ [A-Z][a-z]+)', cv_content, re.MULTILINE)
        name = name_match.group(1) if name_match else "Unknown"

        # Calculate relevant experience years (conservative approach)
        relevant_years = self._calculate_relevant_experience_universal(cv_lower, job_requirements)

        # Extract recent experience (last 5 years)
        recent_experience = self._extract_recent_experience_universal(cv_lower, job_requirements)

        # Extract domain skills
        domain_skills = self._extract_domain_skills_universal(cv_lower, job_requirements)

        # Extract certifications
        certifications = self._extract_certifications_universal(cv_lower, job_requirements)

        # Determine education level
        education_level = self._determine_education_level(cv_lower)

        # Identify gaps and mismatches
        gaps_mismatches = self._identify_gaps_universal(cv_lower, job_requirements)

        # Identify irrelevant experience
        irrelevant_exp = self._identify_irrelevant_experience_universal(cv_lower, job_requirements)

        # NEW: Extract core competency matches
        core_matches = self._extract_core_competency_matches(cv_lower, job_requirements)

        return CandidateProfile(
            name=name,
            relevant_experience_years=relevant_years,
            recent_experience=recent_experience,
            domain_skills=domain_skills,
            certifications=certifications,
            education_level=education_level,
            gaps_mismatches=gaps_mismatches,
            irrelevant_experience=irrelevant_exp,
            core_competency_matches=core_matches
        )

    def _extract_core_competency_matches(self, cv_lower: str, job_req: JobRequirements) -> List[str]:
        """Extract matches to core job competencies"""
        matches = []
        for competency in job_req.core_competencies:
            if competency in cv_lower:
                matches.append(competency)
        return matches

    def _calculate_relevant_experience_universal(self, cv_content: str, job_req: JobRequirements) -> int:
        """Universal method to calculate relevant experience for any domain"""
        max_years = 0
        current_year = datetime.now().year

        # Method 1: Look for explicit experience statements
        explicit_patterns = [
            r'(\d+)\s*jahre?\s*(?:berufserfahrung|erfahrung)',
            r'berufserfahrung.*?(\d+)\s*jahre?',
            r'erfahrung.*?(\d+)\s*jahre?',
            r'(\d+)\+?\s*years?\s*(?:of\s*)?(?:experience|exp)'
        ]

        for pattern in explicit_patterns:
            matches = re.findall(pattern, cv_content, re.IGNORECASE)
            for match in matches:
                if isinstance(match, str) and match.isdigit():
                    years = int(match)
                    max_years = max(max_years, years)

        # Method 2: Calculate from job date ranges (more reliable and conservative)
        date_patterns = [
            r'(\d{4})\s*-\s*(\d{4})',  # 2020-2023
            r'(\d{4})\s*-\s*heute',    # 2020-heute
            r'(\d{4})\s*-\s*present',  # 2020-present
            r'seit\s*(\d{4})',         # seit 2020
        ]

        relevant_job_periods = []

        # Look for date ranges near domain-relevant keywords
        lines = cv_content.split('\n')
        for i, line in enumerate(lines):
            line_lower = line.lower()

            # Check if this line or nearby lines contain relevant keywords
            context_lines = lines[max(0, i-2):min(len(lines), i+3)]  # 2 lines before, 2 after
            context = ' '.join(context_lines).lower()

            # Get domain-specific keywords for relevance check
            relevant_keywords = self._get_domain_keywords(job_req.domain)

            if any(keyword in context for keyword in relevant_keywords):
                for pattern in date_patterns:
                    matches = re.findall(pattern, line, re.IGNORECASE)
                    for match in matches:
                        if isinstance(match, tuple) and len(match) == 2:
                            start_year = int(match[0])
                            end_year = int(match[1]) if match[1].isdigit() else current_year

                            # Sanity check: reasonable year range
                            if (1990 <= start_year <= current_year and
                                1990 <= end_year <= current_year and
                                start_year <= end_year and
                                (end_year - start_year) <= 50):  # Max 50 years experience
                                relevant_job_periods.append((start_year, end_year))
                        elif isinstance(match, str) and match.isdigit():
                            start_year = int(match)
                            if 1990 <= start_year <= current_year:
                                relevant_job_periods.append((start_year, current_year))

        # Calculate total relevant experience (avoid overlaps)
        if relevant_job_periods:
            relevant_job_periods.sort()
            merged_periods = [relevant_job_periods[0]]

            for start, end in relevant_job_periods[1:]:
                last_start, last_end = merged_periods[-1]
                if start <= last_end + 1:  # Allow 1 year gap
                    merged_periods[-1] = (last_start, max(last_end, end))
                else:
                    merged_periods.append((start, end))

            total_relevant_experience = sum(min(end - start, 15) for start, end in merged_periods)  # Cap at 15 years per job
            max_years = max(max_years, total_relevant_experience)

        # Method 3: Look for domain-specific experience indicators
        domain_experience = 0
        if any(term in cv_content.lower() for term in relevant_keywords):
            # If domain terms are present, give some credit even if exact years aren't found
            if max_years == 0:
                domain_experience = 2  # Assume minimum relevant experience

        return max(max_years, domain_experience)

    def _get_domain_keywords(self, domain: str) -> List[str]:
        """Get relevant keywords for a domain"""
        if domain in self.domain_patterns:
            keywords = []
            for category, patterns in self.domain_patterns[domain].items():
                keywords.extend(patterns)
            return keywords
        return []

    def _extract_recent_experience_universal(self, cv_content: str, job_req: JobRequirements) -> List[str]:
        """Universal method to extract recent relevant experience"""
        recent_exp = []
        current_year = datetime.now().year

        # Look for recent job positions (last 5 years)
        year_patterns = [
            r'(\d{4})-heute\s*([^.]+)',
            r'(\d{4})-(\d{4})\s*([^.]+)',
            r'(\d{4})-present\s*([^.]+)'
        ]

        for pattern in year_patterns:
            matches = re.findall(pattern, cv_content, re.IGNORECASE)
            for match in matches:
                if len(match) == 2:  # heute/present pattern
                    start_year = int(match[0])
                    if current_year - start_year <= 5:
                        recent_exp.append(match[1].strip())
                elif len(match) == 3:  # year range pattern
                    end_year = int(match[1])
                    if current_year - end_year <= 5:
                        recent_exp.append(match[2].strip())

        return recent_exp[:3]  # Limit to top 3

    def _extract_domain_skills_universal(self, cv_content: str, job_req: JobRequirements) -> List[str]:
        """Universal method to extract domain-specific skills"""
        skills = []

        if job_req.domain in self.domain_patterns:
            for category, patterns in self.domain_patterns[job_req.domain].items():
                for pattern in patterns:
                    if pattern in cv_content:
                        skills.append(pattern)

        return list(set(skills))  # Remove duplicates

    def _extract_certifications_universal(self, cv_content: str, job_req: JobRequirements) -> List[str]:
        """Universal method to extract relevant certifications"""
        cert_patterns = [
            r'(?:zertifikat|zertifizierung|ausbildung|certification|certified)\s*([^.]+)',
            r'(?:abschluss|qualifikation|degree|diploma)\s*([^.]+)'
        ]

        certifications = []
        for pattern in cert_patterns:
            matches = re.findall(pattern, cv_content, re.IGNORECASE)
            certifications.extend([match.strip() for match in matches])

        return certifications[:5]  # Limit to top 5

    def _identify_gaps_universal(self, cv_content: str, job_req: JobRequirements) -> List[str]:
        """Universal method to identify gaps or mismatches"""
        gaps = []

        # Check for missing core competencies
        for competency in job_req.core_competencies:
            if competency not in cv_content:
                gaps.append(f"Missing core competency: {competency}")

        # Check for missing specific skills
        for skill in job_req.specific_skills:
            if skill not in cv_content:
                gaps.append(f"Missing specific skill: {skill}")

        return gaps

    def _identify_irrelevant_experience_universal(self, cv_content: str, job_req: JobRequirements) -> List[str]:
        """Universal method to identify irrelevant experience"""
        irrelevant = []

        # Define irrelevant patterns by domain
        irrelevant_patterns = {
            'manufacturing': ['marketing', 'sales', 'vertrieb', 'büro', 'verwaltung', 'gastronomie'],
            'software': ['gastronomie', 'retail', 'construction', 'manual labor'],
            'finance': ['manufacturing', 'construction', 'gastronomie', 'retail'],
            'healthcare': ['manufacturing', 'software', 'construction', 'gastronomie'],
            'sales': ['manufacturing', 'healthcare', 'construction']
        }

        patterns = irrelevant_patterns.get(job_req.domain, [])
        for pattern in patterns:
            if pattern in cv_content:
                irrelevant.append(pattern)

        return irrelevant

    def _calculate_relevant_experience(self, cv_content: str, job_req: JobRequirements) -> int:
        """Calculate years of directly relevant experience (conservative)"""
        if job_req.domain != 'manufacturing':
            return 0  # Simplified for now

        max_years = 0
        current_year = datetime.now().year

        # Method 1: Look for explicit experience statements
        explicit_patterns = [
            r'(\d+)\s*jahre?\s*(?:berufserfahrung|erfahrung)',
            r'berufserfahrung.*?(\d+)\s*jahre?',
            r'erfahrung.*?(\d+)\s*jahre?'
        ]

        for pattern in explicit_patterns:
            matches = re.findall(pattern, cv_content, re.IGNORECASE)
            for match in matches:
                if isinstance(match, str) and match.isdigit():
                    years = int(match)
                    max_years = max(max_years, years)

        # Method 2: Calculate from job date ranges (more reliable and conservative)
        date_patterns = [
            r'(\d{4})\s*-\s*(\d{4})',  # 2020-2023
            r'(\d{4})\s*-\s*heute',    # 2020-heute
            r'(\d{4})\s*-\s*present',  # 2020-present
            r'seit\s*(\d{4})',         # seit 2020
        ]

        relevant_job_periods = []

        # Look for date ranges near manufacturing/quality keywords
        lines = cv_content.split('\n')
        for i, line in enumerate(lines):
            line_lower = line.lower()

            # Check if this line or nearby lines contain relevant keywords
            context_lines = lines[max(0, i-2):min(len(lines), i+3)]  # 2 lines before, 2 after
            context = ' '.join(context_lines).lower()

            relevant_keywords = ['cnc', 'zerspanung', 'bearbeitung', 'fertigung', 'qualität',
                               'messtechnik', 'qs', 'produktion', 'maschinen']

            if any(keyword in context for keyword in relevant_keywords):
                for pattern in date_patterns:
                    matches = re.findall(pattern, line, re.IGNORECASE)
                    for match in matches:
                        if isinstance(match, tuple) and len(match) == 2:
                            start_year = int(match[0])
                            end_year = int(match[1]) if match[1].isdigit() else current_year

                            # Sanity check: reasonable year range
                            if (1990 <= start_year <= current_year and
                                1990 <= end_year <= current_year and
                                start_year <= end_year and
                                (end_year - start_year) <= 50):  # Max 50 years experience
                                relevant_job_periods.append((start_year, end_year))
                        elif isinstance(match, str) and match.isdigit():
                            start_year = int(match)
                            if 1990 <= start_year <= current_year:
                                relevant_job_periods.append((start_year, current_year))

        # Calculate total relevant experience (avoid overlaps)
        if relevant_job_periods:
            relevant_job_periods.sort()
            merged_periods = [relevant_job_periods[0]]

            for start, end in relevant_job_periods[1:]:
                last_start, last_end = merged_periods[-1]
                if start <= last_end + 1:  # Allow 1 year gap
                    merged_periods[-1] = (last_start, max(last_end, end))
                else:
                    merged_periods.append((start, end))

            total_relevant_experience = sum(min(end - start, 15) for start, end in merged_periods)  # Cap at 15 years per job
            max_years = max(max_years, total_relevant_experience)

        # Method 3: Look for manufacturing-specific experience indicators
        manufacturing_experience = 0
        if any(term in cv_content.lower() for term in ['cnc', 'zerspanung', 'bearbeitung', 'fertigung', 'qualität']):
            # If manufacturing terms are present, give some credit even if exact years aren't found
            if max_years == 0:
                manufacturing_experience = 2  # Assume minimum relevant experience

        return max(max_years, manufacturing_experience)
    
    def _extract_recent_experience(self, cv_content: str, job_req: JobRequirements) -> List[str]:
        """Extract recent relevant experience"""
        recent_exp = []
        current_year = datetime.now().year
        
        # Look for recent job positions (last 5 years)
        year_patterns = [
            r'(\d{4})-heute\s*([^.]+)',
            r'(\d{4})-(\d{4})\s*([^.]+)'
        ]
        
        for pattern in year_patterns:
            matches = re.findall(pattern, cv_content, re.IGNORECASE)
            for match in matches:
                if len(match) == 2:  # heute pattern
                    start_year = int(match[0])
                    if current_year - start_year <= 5:
                        recent_exp.append(match[1].strip())
                elif len(match) == 3:  # year range pattern
                    end_year = int(match[1])
                    if current_year - end_year <= 5:
                        recent_exp.append(match[2].strip())
        
        return recent_exp[:3]  # Limit to top 3
    
    def _extract_domain_skills(self, cv_content: str, job_req: JobRequirements) -> List[str]:
        """Extract domain-specific skills"""
        skills = []
        
        if job_req.domain == 'manufacturing':
            for category, patterns in self.manufacturing_patterns.items():
                for pattern in patterns:
                    if pattern in cv_content:
                        skills.append(pattern)
        
        return list(set(skills))  # Remove duplicates
    
    def _extract_certifications(self, cv_content: str, job_req: JobRequirements) -> List[str]:
        """Extract relevant certifications"""
        cert_patterns = [
            r'(?:zertifikat|zertifizierung|ausbildung)\s*([^.]+)',
            r'(?:abschluss|qualifikation)\s*([^.]+)'
        ]
        
        certifications = []
        for pattern in cert_patterns:
            matches = re.findall(pattern, cv_content, re.IGNORECASE)
            certifications.extend([match.strip() for match in matches])
        
        return certifications[:3]  # Limit to top 3
    
    def _determine_education_level(self, cv_content: str) -> str:
        """Determine education level"""
        if any(term in cv_content for term in ['promotion', 'phd', 'doktor']):
            return 'phd'
        elif any(term in cv_content for term in ['master', 'diplom']):
            return 'master'
        elif any(term in cv_content for term in ['bachelor', 'studium']):
            return 'bachelor'
        elif any(term in cv_content for term in ['ausbildung', 'lehre']):
            return 'vocational'
        else:
            return 'unknown'
    
    def _identify_gaps(self, cv_content: str, job_req: JobRequirements) -> List[str]:
        """Identify gaps or mismatches"""
        gaps = []
        
        # Check for missing required skills
        for skill in job_req.specific_skills:
            if skill not in cv_content:
                gaps.append(f"Missing: {skill}")
        
        return gaps
    
    def _identify_irrelevant_experience(self, cv_content: str, job_req: JobRequirements) -> List[str]:
        """Identify irrelevant experience"""
        irrelevant = []
        
        if job_req.domain == 'manufacturing':
            irrelevant_patterns = ['marketing', 'sales', 'vertrieb', 'büro', 'verwaltung']
            for pattern in irrelevant_patterns:
                if pattern in cv_content:
                    irrelevant.append(pattern)
        
        return irrelevant
    
    def calculate_conservative_score(self, candidate: CandidateProfile,
                                   job_req: JobRequirements, cv_content: str = "") -> Dict[str, Any]:
        """Universal conservative scoring that adapts to any domain"""

        # Get domain-specific weights
        weights = self.domain_weights.get(job_req.domain, self.domain_weights['general'])

        # 1. Experience scoring (conservative but not zero-based)
        experience_score = self._calculate_experience_score(candidate, job_req)

        # 2. Core competency scoring (most important - domain-specific)
        core_competency_score = self._calculate_core_competency_score(candidate, job_req, cv_content)

        # 3. Domain skills scoring
        domain_skills_score = self._calculate_domain_skills_score(candidate, job_req, cv_content)

        # 4. Education scoring (domain-specific)
        education_score = self._calculate_education_score(candidate, job_req)

        # 5. Leadership/soft skills scoring
        leadership_score = self._calculate_leadership_score(candidate, job_req, cv_content)

        # 6. Calculate penalties
        gap_penalty, irrelevant_penalty = self._calculate_penalties(candidate, job_req)

        # 7. Apply domain-specific weighting
        final_score = self._apply_domain_weighting(
            experience_score, core_competency_score, domain_skills_score,
            education_score, leadership_score, weights, job_req.domain
        )

        # Apply penalties but ensure minimum score
        final_score = max(0.05, final_score - gap_penalty - irrelevant_penalty)

        return {
            'final_score': final_score * 100,
            'experience_score': experience_score * 100,
            'core_competency_score': core_competency_score * 100,
            'domain_skills_score': domain_skills_score * 100,
            'education_score': education_score * 100,
            'leadership_score': leadership_score * 100,
            'penalties': (gap_penalty + irrelevant_penalty) * 100,
            'domain': job_req.domain,
            'weights_used': weights,
            'core_competency_matches': candidate.core_competency_matches
        }

    def _calculate_experience_score(self, candidate: CandidateProfile, job_req: JobRequirements) -> float:
        """Calculate experience score"""
        if candidate.relevant_experience_years >= job_req.min_experience_years:
            return min(0.9, 0.6 + (candidate.relevant_experience_years - job_req.min_experience_years) * 0.05)
        elif candidate.relevant_experience_years > 0:
            return 0.3 + (candidate.relevant_experience_years / job_req.min_experience_years) * 0.3
        else:
            return 0.1  # Minimum score for any candidate

    def _calculate_core_competency_score(self, candidate: CandidateProfile,
                                       job_req: JobRequirements, cv_content: str) -> float:
        """Calculate core competency score - most critical for job success"""
        if not job_req.core_competencies:
            return 0.5  # Neutral if no core competencies defined

        full_cv_lower = cv_content.lower() if cv_content else ' '.join(candidate.domain_skills).lower()

        # Count matches to core competencies
        core_matches = 0
        total_core = len(job_req.core_competencies)

        for competency in job_req.core_competencies:
            if competency in full_cv_lower:
                core_matches += 1

        # Base score from core competency coverage
        base_score = core_matches / total_core if total_core > 0 else 0

        # Apply domain-specific bonuses
        bonus_score = self._apply_domain_specific_bonuses(
            candidate, job_req, full_cv_lower, core_matches, total_core
        )

        return min(0.9, base_score + bonus_score)

    def _apply_domain_specific_bonuses(self, candidate: CandidateProfile,
                                     job_req: JobRequirements, cv_content: str,
                                     core_matches: int, total_core: int) -> float:
        """Apply domain-specific bonuses based on job type"""
        bonus = 0

        if job_req.domain == 'manufacturing':
            # CNC programming gets highest bonus (like your example)
            cnc_programming_patterns = [
                'programmierung von cnc', 'cnc programmierung', 'cnc-programmierung',
                'cnc-messprogramm', 'cnc messprogramm', 'erstellen von cnc'
            ]
            if any(pattern in cv_content for pattern in cnc_programming_patterns):
                bonus += 0.4  # Major bonus for CNC programming
            elif 'cnc' in cv_content:
                bonus += 0.2  # Moderate bonus for general CNC

            # Series production experience
            if any(term in cv_content for term in ['serienfertigung', 'serienproduktion']):
                bonus += 0.15

            # Quality control experience
            if any(term in cv_content for term in ['qualitätskontrolle', 'qs', 'messtechnik']):
                bonus += 0.1

        elif job_req.domain == 'software':
            # Programming languages bonus
            prog_languages = ['java', 'python', 'javascript', 'c++', 'c#']
            lang_matches = sum(1 for lang in prog_languages if lang in cv_content)
            bonus += min(0.3, lang_matches * 0.1)

            # Framework experience
            frameworks = ['spring', 'react', 'angular', 'django']
            framework_matches = sum(1 for fw in frameworks if fw in cv_content)
            bonus += min(0.2, framework_matches * 0.05)

        elif job_req.domain == 'finance':
            # Financial analysis experience
            if any(term in cv_content for term in ['finanzanalyse', 'financial analysis']):
                bonus += 0.3

            # Compliance experience
            if any(term in cv_content for term in ['compliance', 'audit', 'risk']):
                bonus += 0.2

        return bonus

    def _calculate_domain_skills_score(self, candidate: CandidateProfile,
                                     job_req: JobRequirements, cv_content: str) -> float:
        """Calculate domain skills score"""
        if not candidate.domain_skills:
            return 0.1

        # Count relevant domain skills
        relevant_skills = 0
        total_domain_skills = len(job_req.domain_knowledge) if job_req.domain_knowledge else 1

        for skill in candidate.domain_skills:
            if any(domain_skill in skill.lower() for domain_skill in job_req.domain_knowledge):
                relevant_skills += 1

        base_score = min(0.8, relevant_skills / total_domain_skills)

        # Bonus for specific tools/technologies
        specific_bonus = 0
        for skill in job_req.specific_skills:
            if skill in cv_content.lower():
                specific_bonus += 0.05

        return min(0.9, base_score + specific_bonus)

    def _calculate_education_score(self, candidate: CandidateProfile, job_req: JobRequirements) -> float:
        """Calculate education score based on domain requirements"""
        education_levels = {
            'unknown': 0.2,
            'high_school': 0.3,
            'vocational': 0.6,
            'bachelor': 0.8,
            'master': 0.9,
            'phd': 1.0
        }

        base_score = education_levels.get(candidate.education_level, 0.2)

        # Domain-specific adjustments
        if job_req.domain == 'manufacturing':
            # Vocational training is highly valued in manufacturing
            if candidate.education_level == 'vocational':
                return 0.9  # Almost perfect for manufacturing
            elif candidate.education_level in ['bachelor', 'master']:
                return 0.7  # Good but not ideal
        elif job_req.domain == 'software':
            # Formal education more important in software
            if candidate.education_level in ['bachelor', 'master', 'phd']:
                return base_score
            else:
                return base_score * 0.7  # Penalty for lack of formal education

        return base_score

    def _calculate_leadership_score(self, candidate: CandidateProfile,
                                  job_req: JobRequirements, cv_content: str) -> float:
        """Calculate leadership/soft skills score"""
        if not job_req.leadership_required:
            return 0.5  # Neutral if not required

        leadership_indicators = [
            'leitung', 'führung', 'team', 'management', 'lead',
            'supervisor', 'coordinator', 'project management'
        ]

        leadership_mentions = sum(1 for indicator in leadership_indicators
                                if indicator in cv_content.lower())

        return min(0.8, leadership_mentions * 0.2)

    def _calculate_penalties(self, candidate: CandidateProfile, job_req: JobRequirements) -> Tuple[float, float]:
        """Calculate penalties for gaps and irrelevant experience"""
        gap_penalty = len(candidate.gaps_mismatches) * 0.03  # Reduced penalty
        irrelevant_penalty = len(candidate.irrelevant_experience) * 0.02  # Reduced penalty

        return gap_penalty, irrelevant_penalty

    def _apply_domain_weighting(self, experience_score: float, core_competency_score: float,
                              domain_skills_score: float, education_score: float,
                              leadership_score: float, weights: Dict[str, float],
                              domain: str) -> float:
        """Apply domain-specific weighting to scores"""

        if domain == 'manufacturing':
            # Manufacturing: Direct experience 60%, Quality/Process 25%, Education 10%, Leadership 5%
            return (
                core_competency_score * weights['direct_experience'] +
                domain_skills_score * weights['quality_process'] +
                education_score * weights['education'] +
                leadership_score * weights['leadership']
            )
        elif domain == 'software':
            # Software: Technical skills 50%, Experience 30%, Education 15%, Methodologies 5%
            return (
                core_competency_score * weights['technical_skills'] +
                experience_score * weights['experience'] +
                education_score * weights['education'] +
                domain_skills_score * weights['methodologies']
            )
        elif domain == 'finance':
            # Finance: Domain knowledge 45%, Experience 35%, Education 15%, Tools 5%
            return (
                core_competency_score * weights['domain_knowledge'] +
                experience_score * weights['experience'] +
                education_score * weights['education'] +
                domain_skills_score * weights['tools']
            )
        else:
            # General: Experience 40%, Skills 35%, Education 20%, Adaptability 5%
            return (
                experience_score * weights['experience'] +
                core_competency_score * weights['skills'] +
                education_score * weights['education'] +
                leadership_score * weights['adaptability']
            )

# Compatibility alias for existing code
DomainSpecificMatcher = UniversalDomainMatcher
