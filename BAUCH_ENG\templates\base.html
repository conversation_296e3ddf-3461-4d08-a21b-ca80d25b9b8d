<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}BAUCH HR Management System{% endblock %}</title>
    <!-- Google Fonts - Poppins -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('home') }}">
                <img src="{{ url_for('static', filename='img/BAUCH_Group_Logo_4c_transparent.png') }}" alt="BAUCH Group" class="navbar-brand-logo">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('home') }}">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('jobs') }}">
                            <i class="fas fa-briefcase me-1"></i>Jobs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('cvs') }}">
                            <i class="fas fa-file-alt me-1"></i>CVs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('match') }}">
                            <i class="fas fa-check-circle me-1"></i>Match
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('match') }}">
                            <i class="fas fa-search me-1"></i>Match CVs
                        </a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <!-- Logo Container -->
                    <div class="navbar-logos me-3">
                        <img src="{{ url_for('static', filename='img/Bauch-engineering-transparent.png') }}" alt="Bauch Engineering" class="navbar-logo">
                        <img src="{{ url_for('static', filename='img/Bauch_CNC_Logo_transparent.png') }}" alt="Bauch CNC" class="navbar-logo">
                        <img src="{{ url_for('static', filename='img/SCHERMER_Logo_Bauch_Group_4c.png') }}" alt="Schermer Bauch Group" class="navbar-logo">
                    </div>
                    <button class="btn btn-outline-dark btn-sm me-2" id="theme-toggle">
                        <i class="fas fa-moon" id="dark-icon"></i>
                        <i class="fas fa-sun d-none" id="light-icon"></i>
                    </button>
                    <a href="{{ url_for('logout') }}" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-sign-out-alt me-1"></i>Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    <div class="container mt-3">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <!-- Main Content -->
    <div class="container mt-4">
        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="text-center text-muted py-4 mt-5">
        <div class="container">
            <p>BAUCH HR Management System &copy; {{ now.year }}</p>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
