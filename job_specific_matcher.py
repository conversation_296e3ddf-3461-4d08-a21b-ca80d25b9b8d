#!/usr/bin/env python3
"""
Job-Specific CV Matcher
Creates custom skill lists for each job and scores candidates only on those skills
Never re-uses domain taxonomy - builds fresh evaluation for each job
"""

import re
from typing import List, Dict, Tuple, Any
from dataclasses import dataclass
from datetime import datetime

@dataclass
class JobSpecificRequirements:
    """Job-specific requirements extracted from job ad"""
    skill_set: List[str]  # Max 15 skills in order of importance
    min_experience_years: int
    required_education_level: str
    critical_certifications: List[str]  # Named tools/certs for bonus points

@dataclass
class CandidateEvaluation:
    """Candidate evaluation results"""
    candidate_name: str
    experience_points: int
    education_points: int
    bonus_points: int
    total_score: int
    skill_breakdown: Dict[str, int]
    education_detected: str
    top_relevant_experience: List[str]
    irrelevant_experience_ignored: List[str]

class JobSpecificMatcher:
    """
    Strict job-specific matcher that builds custom skill lists for each job
    Follows German education qualification ladder
    """
    
    def __init__(self):
        # German education qualification ladder
        self.education_levels = {
            'doktor': {'keywords': ['dr.', 'dr-ing', 'dr. rer. nat', 'promotion', 'phd'], 'points': 10},
            'master': {'keywords': ['m.sc.', 'dipl.-ing', 'diplom', 'master'], 'points': 8},
            'bachelor': {'keywords': ['b.eng.', 'b.sc.', 'bachelor'], 'points': 6},
            'techniker': {'keywords': ['staatlich geprüfter techniker', 'techniker', 'meister', 'industriemeister'], 'points': 5},
            'facharbeiter': {'keywords': ['ausbildung', 'lehre', 'geselle', 'facharbeiter', 'ihk', 'hwk'], 'points': 3},
            'none': {'keywords': [], 'points': 0}
        }
    
    def extract_job_requirements(self, job_description: str) -> JobSpecificRequirements:
        """Extract custom skill list from specific job ad (max 15 skills)"""
        job_lower = job_description.lower()
        
        # Build custom skill list based on this specific job
        skill_set = self._build_custom_skill_list(job_description, job_lower)
        
        # Extract experience requirement
        min_experience = self._extract_experience_requirement(job_lower)
        
        # Extract education requirement
        education_level = self._extract_education_requirement(job_lower)
        
        # Extract critical certifications/tools
        critical_certs = self._extract_critical_certifications(job_lower)
        
        return JobSpecificRequirements(
            skill_set=skill_set,
            min_experience_years=min_experience,
            required_education_level=education_level,
            critical_certifications=critical_certs
        )
    
    def _build_custom_skill_list(self, job_desc: str, job_lower: str) -> List[str]:
        """Build custom skill list from job description (max 15, ordered by importance)"""
        skills = []
        
        # Extract explicit skills mentioned in job description
        # Look for technical skills, tools, processes, qualifications
        
        # CNC/Manufacturing skills (if mentioned)
        if 'cnc' in job_lower:
            if 'programmier' in job_lower:
                skills.append('CNC-Programmierung')
            if 'fanuc' in job_lower or 'heidenhain' in job_lower:
                skills.append('Fanuc/Heidenhain Steuerungen')
            if 'rüsten' in job_lower or 'einfahren' in job_lower:
                skills.append('Rüsten und Einfahren CNC-Maschinen')
        
        # Production/Manufacturing processes
        if 'serienfertigung' in job_lower or 'serienproduktion' in job_lower:
            skills.append('Serienfertigung (Klein-/Mittelserien)')
        
        if 'erstbemusterung' in job_lower or 'erstmuster' in job_lower:
            skills.append('Erstbemusterung')
        
        if 'qualitätskontrolle' in job_lower or 'qs-maßnahmen' in job_lower or 'qualitätssicherung' in job_lower:
            skills.append('QS-Maßnahmen/Qualitätskontrolle')
        
        if 'prozessoptimierung' in job_lower:
            skills.append('Prozessoptimierung')
        
        if 'maschinenbetreuung' in job_lower:
            skills.append('Maschinenbetreuung')
        
        # Education/Training requirements
        if 'zerspanungsmechaniker' in job_lower or 'industriemechaniker' in job_lower or 'feinwerkmechaniker' in job_lower:
            skills.append('Zerspanungsmechaniker/Industriemechaniker/Feinwerkmechaniker Ausbildung')
        
        if 'mechanische bearbeitung' in job_lower or 'zerspanung' in job_lower:
            skills.append('Mechanische Bearbeitung/Zerspanung')
        
        # Standards and methodologies
        if 'vda' in job_lower:
            skills.append('VDA-Kenntnisse')
        
        # Soft skills (if explicitly mentioned)
        if 'störung' in job_lower:
            skills.append('Störungsbehebung')
        
        if 'selbständig' in job_lower or 'eigenverantwortlich' in job_lower:
            skills.append('Selbständiges Arbeiten')
        
        if 'qualitätsbewusstsein' in job_lower:
            skills.append('Qualitätsbewusstsein')
        
        if 'schichtleitung' in job_lower or 'führung' in job_lower or 'verantwortung' in job_lower:
            skills.append('Schichtleitung/Führungsverantwortung')
        
        # Software development skills (if mentioned)
        if 'java' in job_lower:
            skills.append('Java Programmierung')
        if 'python' in job_lower:
            skills.append('Python Programmierung')
        if 'spring' in job_lower:
            skills.append('Spring Framework')
        if 'database' in job_lower or 'datenbank' in job_lower:
            skills.append('Datenbank-Kenntnisse')
        
        # Finance skills (if mentioned)
        if 'financial analysis' in job_lower or 'finanzanalyse' in job_lower:
            skills.append('Finanzanalyse')
        if 'sap' in job_lower:
            skills.append('SAP-Kenntnisse')
        if 'reporting' in job_lower:
            skills.append('Reporting')
        
        # Limit to 15 skills maximum
        return skills[:15]
    
    def _extract_experience_requirement(self, job_lower: str) -> int:
        """Extract minimum experience requirement"""
        exp_patterns = [
            r'(\d+)\+?\s*jahre?\s*(?:berufserfahrung|erfahrung)',
            r'mindestens\s+(\d+)\s+jahre?',
            r'(\d+)\+?\s*years?\s*(?:of\s*)?(?:experience|exp)'
        ]
        
        for pattern in exp_patterns:
            match = re.search(pattern, job_lower)
            if match:
                return int(match.group(1))
        
        return 2  # Default minimum
    
    def _extract_education_requirement(self, job_lower: str) -> str:
        """Extract education requirement"""
        if any(keyword in job_lower for keyword in ['bachelor', 'master', 'studium']):
            return 'bachelor'
        elif any(keyword in job_lower for keyword in ['ausbildung', 'lehre', 'facharbeiter']):
            return 'facharbeiter'
        elif any(keyword in job_lower for keyword in ['techniker', 'meister']):
            return 'techniker'
        else:
            return 'facharbeiter'  # Default for manufacturing jobs
    
    def _extract_critical_certifications(self, job_lower: str) -> List[str]:
        """Extract critical certifications/tools for bonus points"""
        certs = []
        
        # Look for specific named tools/certifications
        cert_patterns = [
            'fanuc', 'heidenhain', 'siemens', 'mitutoyo', 'zeiss', 'quindos',
            'vda', 'dgq', 'iso', 'qs', 'cam', 'cad'
        ]
        
        for cert in cert_patterns:
            if cert in job_lower:
                certs.append(cert)
        
        return certs[:5]  # Max 5 bonus certifications
    
    def evaluate_candidate(self, cv_content: str, job_requirements: JobSpecificRequirements) -> CandidateEvaluation:
        """Evaluate candidate against job-specific requirements"""
        cv_lower = cv_content.lower()
        
        # Extract candidate name
        name = self._extract_candidate_name(cv_content)
        
        # Score experience for each skill (0-3 points each)
        skill_breakdown = {}
        total_experience_points = 0
        
        for skill in job_requirements.skill_set:
            points = self._score_skill_experience(cv_lower, skill)
            skill_breakdown[skill] = points
            total_experience_points += points
        
        # Score education
        education_points, education_detected = self._score_education(cv_lower, job_requirements.required_education_level)
        
        # Score bonus certifications
        bonus_points = self._score_bonus_certifications(cv_lower, job_requirements.critical_certifications)
        
        # Extract relevant and irrelevant experience
        relevant_exp = self._extract_relevant_experience(cv_content, job_requirements.skill_set)
        irrelevant_exp = self._extract_irrelevant_experience(cv_content, job_requirements.skill_set)
        
        total_score = total_experience_points + education_points + bonus_points
        
        return CandidateEvaluation(
            candidate_name=name,
            experience_points=total_experience_points,
            education_points=education_points,
            bonus_points=bonus_points,
            total_score=total_score,
            skill_breakdown=skill_breakdown,
            education_detected=education_detected,
            top_relevant_experience=relevant_exp,
            irrelevant_experience_ignored=irrelevant_exp
        )
    
    def _extract_candidate_name(self, cv_content: str) -> str:
        """Extract candidate name from CV"""
        lines = cv_content.split('\n')
        for line in lines[:5]:  # Check first 5 lines
            line = line.strip()
            if line and len(line.split()) == 2:  # Likely "First Last" format
                words = line.split()
                if all(word[0].isupper() for word in words):  # Both words capitalized
                    return line
        return "Unknown Candidate"
    
    def _score_skill_experience(self, cv_lower: str, skill: str) -> int:
        """Score experience for a specific skill (0-3 points)"""
        skill_lower = skill.lower()

        # Define skill keywords for matching with enhanced patterns
        skill_keywords = {
            'cnc-programmierung': ['cnc', 'programmierung', 'cnc-programmierung', 'cnc programmierung', 'cnc-messprogramm', 'messmaschinen'],
            'fanuc/heidenhain steuerungen': ['fanuc', 'heidenhain'],
            'rüsten und einfahren cnc-maschinen': ['rüsten', 'einfahren', 'einrichten', 'maschinenbedienung'],
            'serienfertigung (klein-/mittelserien)': ['serienfertigung', 'serienproduktion', 'serie', 'serienbegleitend', 'kleinserien', 'mittelserien'],
            'erstbemusterung': ['erstbemusterung', 'erstmuster', 'bemusterung', 'erstmusterprüf'],
            'qs-maßnahmen/qualitätskontrolle': ['qualitätskontrolle', 'qualitätssicherung', 'qs', 'qualität', 'prüfprotokoll'],
            'prozessoptimierung': ['prozessoptimierung', 'optimierung', 'kvp', 'verbesserung', 'prozessaudit'],
            'maschinenbetreuung': ['maschinenbetreuung', 'maschinenbedienung', 'maschinen', 'betreuung'],
            'zerspanungsmechaniker/industriemechaniker/feinwerkmechaniker ausbildung': ['zerspanungsmechaniker', 'industriemechaniker', 'feinwerkmechaniker', 'fertigungsmechaniker', 'ausbildung'],
            'mechanische bearbeitung/zerspanung': ['zerspanung', 'bearbeitung', 'drehen', 'fräsen', 'nc-drehen', 'nc-fräsen'],
            'vda-kenntnisse': ['vda', 'vda-berichte', 'vda-standard'],
            'störungsbehebung': ['störung', 'fehler', 'problem', 'behebung'],
            'selbständiges arbeiten': ['selbständig', 'eigenverantwortlich', 'selbstständig', 'eigenständig'],
            'qualitätsbewusstsein': ['qualitätsbewusstsein', 'präzision', 'genauigkeit', 'gewissenhaft'],
            'schichtleitung/führungsverantwortung': ['schichtleitung', 'führung', 'leitung', 'team', 'verantwortung', 'perspektive']
        }

        # Get keywords for this skill
        keywords = skill_keywords.get(skill_lower, [skill_lower.split()[0]])

        # Check for keyword presence and context
        matches = 0
        recent_experience = False
        strong_evidence = False

        for keyword in keywords:
            if keyword in cv_lower:
                matches += 1

                # Check if it's recent experience (contains years 2015+)
                keyword_context = self._get_keyword_context(cv_lower, keyword, 150)
                if any(year in keyword_context for year in ['2015', '2016', '2017', '2018', '2019', '2020', '2021', '2022', '2023', '2024', '2025', 'heute', 'present']):
                    recent_experience = True

                # Check for strong evidence (detailed descriptions)
                if any(strong_word in keyword_context for strong_word in ['erfahrung', 'spezialist', 'verantwortlich', 'durchführung', 'erstellung']):
                    strong_evidence = True

        # Enhanced scoring for better differentiation
        if matches >= 2 and recent_experience and strong_evidence:
            return 3  # Clearly demonstrated with strong evidence
        elif matches >= 1 and recent_experience and strong_evidence:
            return 3  # Strong recent experience
        elif matches >= 2 and recent_experience:
            return 3  # Multiple matches, recent
        elif matches >= 1 and recent_experience:
            return 2  # Some recent experience
        elif matches >= 2:
            return 2  # Multiple matches but older
        elif matches >= 1:
            return 1  # Only basic mention
        else:
            return 0  # Not evidenced
    
    def _get_keyword_context(self, text: str, keyword: str, context_chars: int) -> str:
        """Get context around a keyword"""
        index = text.find(keyword)
        if index == -1:
            return ""
        
        start = max(0, index - context_chars)
        end = min(len(text), index + len(keyword) + context_chars)
        return text[start:end]
    
    def _score_education(self, cv_lower: str, required_level: str) -> Tuple[int, str]:
        """Score education based on German qualification ladder"""
        detected_education = "Kein einschlägiger Abschluss"
        points = 0
        
        # Check each education level (highest first)
        for level, config in self.education_levels.items():
            for keyword in config['keywords']:
                if keyword in cv_lower:
                    # Check if it's relevant to the job domain
                    context = self._get_keyword_context(cv_lower, keyword, 50)
                    
                    # Determine relevance
                    relevant = self._is_education_relevant(context, required_level)
                    
                    if relevant:
                        points = config['points']
                        detected_education = f"{keyword.title()} ({points} pts)"
                        return points, detected_education
                    else:
                        # Tangentially related - halve points
                        points = config['points'] // 2
                        detected_education = f"{keyword.title()} - tangential ({points} pts)"
                        return points, detected_education
        
        return 0, "Kein einschlägiger Abschluss (0 pts)"
    
    def _is_education_relevant(self, context: str, required_level: str) -> bool:
        """Check if education is relevant to job domain"""
        relevant_keywords = [
            'maschinenbau', 'mechanik', 'technik', 'ingenieur', 'fertigung',
            'zerspanung', 'metall', 'industrie', 'feinwerk', 'werkzeug'
        ]
        
        return any(keyword in context for keyword in relevant_keywords)
    
    def _score_bonus_certifications(self, cv_lower: str, critical_certs: List[str]) -> int:
        """Score bonus certifications (max 5 points)"""
        bonus = 0
        
        for cert in critical_certs:
            if cert in cv_lower:
                bonus += 1
        
        return min(5, bonus)
    
    def _extract_relevant_experience(self, cv_content: str, skills: List[str]) -> List[str]:
        """Extract top relevant experience entries"""
        relevant = []
        lines = cv_content.split('\n')
        
        for line in lines:
            line_lower = line.lower()
            if any(skill.split()[0].lower() in line_lower for skill in skills):
                if len(line.strip()) > 20:  # Meaningful content
                    relevant.append(line.strip())
        
        return relevant[:4]  # Top 4 relevant experiences
    
    def _extract_irrelevant_experience(self, cv_content: str, skills: List[str]) -> List[str]:
        """Extract irrelevant experience that should be ignored"""
        irrelevant = []
        irrelevant_keywords = [
            'verkäufer', 'verkauf', 'vertrieb', 'büro', 'verwaltung',
            'gastronomie', 'restaurant', 'einzelhandel', 'beratung'
        ]
        
        lines = cv_content.split('\n')
        for line in lines:
            line_lower = line.lower()
            if any(keyword in line_lower for keyword in irrelevant_keywords):
                if len(line.strip()) > 10:
                    irrelevant.append(line.strip())
        
        return irrelevant[:3]  # Top 3 irrelevant experiences
    
    def rank_candidates(self, evaluations: List[CandidateEvaluation]) -> List[CandidateEvaluation]:
        """Rank candidates by total score"""
        return sorted(evaluations, key=lambda x: x.total_score, reverse=True)
    
    def generate_ranking_report(self, job_description: str, cv_contents: List[Tuple[str, str]]) -> Dict[str, Any]:
        """Generate complete ranking report for a job"""
        # Extract job requirements
        job_requirements = self.extract_job_requirements(job_description)
        
        # Evaluate all candidates
        evaluations = []
        for candidate_name, cv_content in cv_contents:
            evaluation = self.evaluate_candidate(cv_content, job_requirements)
            evaluations.append(evaluation)
        
        # Rank candidates
        ranked_candidates = self.rank_candidates(evaluations)
        
        return {
            "skill_set": job_requirements.skill_set,
            "candidates": [
                {
                    "candidate_name": eval.candidate_name,
                    "experience_points": eval.experience_points,
                    "education_points": eval.education_points,
                    "bonus_points": eval.bonus_points,
                    "total_score": eval.total_score,
                    "skill_breakdown": eval.skill_breakdown,
                    "education_detected": eval.education_detected,
                    "top_relevant_experience": eval.top_relevant_experience,
                    "irrelevant_experience_ignored": eval.irrelevant_experience_ignored
                }
                for eval in ranked_candidates
            ]
        }
