#!/usr/bin/env python3
"""
Create Admin User Script for BAUCH HR Management System
Creates an admin user for initial system access
"""

import os
import sys
import getpass
from hr_database import HRDatabase

def create_admin_user():
    """Create an admin user for the system"""
    print("🔐 BAUCH HR Management System - Admin User Creation")
    print("=" * 55)
    
    # Initialize database
    try:
        hr_db = HRDatabase()
        print("✅ Database connection established")
    except Exception as e:
        print(f"❌ Error connecting to database: {e}")
        sys.exit(1)
    
    # Get admin user details
    print("\nPlease provide admin user details:")
    
    while True:
        username = input("Username: ").strip()
        if username:
            # Check if user already exists
            try:
                existing_user = hr_db.get_user_by_username(username)
                if existing_user:
                    print(f"❌ User '{username}' already exists. Please choose a different username.")
                    continue
                break
            except:
                break
        else:
            print("❌ Username cannot be empty")
    
    while True:
        email = input("Email: ").strip()
        if email and '@' in email:
            # Check if email already exists
            try:
                existing_user = hr_db.get_user_by_email(email)
                if existing_user:
                    print(f"❌ Email '{email}' already exists. Please choose a different email.")
                    continue
                break
            except:
                break
        else:
            print("❌ Please enter a valid email address")
    
    while True:
        password = getpass.getpass("Password (min 8 characters): ")
        if len(password) >= 8:
            confirm_password = getpass.getpass("Confirm password: ")
            if password == confirm_password:
                break
            else:
                print("❌ Passwords do not match")
        else:
            print("❌ Password must be at least 8 characters long")
    
    # Create the admin user
    try:
        user = hr_db.create_user(username, email, password)
        print(f"\n✅ Admin user '{username}' created successfully!")
        print(f"📧 Email: {email}")
        print(f"🆔 User ID: {user.id}")
        
        # Display login information
        print("\n" + "=" * 55)
        print("🎉 Setup Complete!")
        print("You can now login to the system with:")
        print(f"   Username: {username}")
        print(f"   Password: [the password you entered]")
        print("\n🌐 Access the application at:")
        print("   Development: http://localhost:5000")
        print("   Production: http://your-domain.com")
        print("\n🔧 Features available:")
        print("   ✅ Bilingual CV processing (German/English)")
        print("   ✅ Advanced job matching algorithms")
        print("   ✅ Secure password management")
        print("   ✅ Language switching interface")
        print("   ✅ Enhanced security features")
        
    except Exception as e:
        print(f"❌ Error creating admin user: {e}")
        sys.exit(1)


def check_database_setup():
    """Check if database is properly set up"""
    try:
        hr_db = HRDatabase()
        # Try to query users table
        users = hr_db.get_all_users()
        return True
    except Exception as e:
        print(f"❌ Database setup issue: {e}")
        print("💡 The database tables will be created automatically when you create the first user.")
        return True  # Continue anyway, tables will be created


def main():
    """Main function"""
    print("Checking database setup...")
    if not check_database_setup():
        sys.exit(1)
    
    print("Database is ready.")
    create_admin_user()


if __name__ == "__main__":
    main()
