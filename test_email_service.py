"""
Test suite for EmailService
Tests email functionality with mocking to avoid sending real emails
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import os
import tempfile
import logging
from email_service import EmailService


class TestEmailService(unittest.TestCase):
    """Test cases for EmailService class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_config = {
            'mail_server': 'smtp.test.com',
            'mail_port': 587,
            'mail_username': '<EMAIL>',
            'mail_password': 'testpassword',
            'mail_use_tls': True,
            'default_sender': '<EMAIL>'
        }
        
        # Create email service with test configuration
        self.email_service = EmailService(**self.test_config)
        
        # Set up logging to capture log messages
        self.log_capture = []
        self.test_handler = logging.Handler()
        self.test_handler.emit = lambda record: self.log_capture.append(record.getMessage())
        self.email_service.logger.addHandler(self.test_handler)
        self.email_service.logger.setLevel(logging.DEBUG)
    
    def tearDown(self):
        """Clean up after tests"""
        self.email_service.logger.removeHandler(self.test_handler)
    
    def test_initialization_with_parameters(self):
        """Test EmailService initialization with direct parameters"""
        service = EmailService(
            mail_server='smtp.example.com',
            mail_port=465,
            mail_username='<EMAIL>',
            mail_password='password123',
            mail_use_tls=False,
            default_sender='<EMAIL>'
        )
        
        self.assertEqual(service.mail_server, 'smtp.example.com')
        self.assertEqual(service.mail_port, 465)
        self.assertEqual(service.mail_username, '<EMAIL>')
        self.assertEqual(service.mail_password, 'password123')
        self.assertEqual(service.mail_use_tls, False)
        self.assertEqual(service.default_sender, '<EMAIL>')
    
    @patch.dict(os.environ, {
        'MAIL_SERVER': 'smtp.env.com',
        'MAIL_PORT': '465',
        'MAIL_USERNAME': '<EMAIL>',
        'MAIL_PASSWORD': 'envpassword',
        'MAIL_DEFAULT_SENDER': '<EMAIL>'
    })
    def test_initialization_with_environment_variables(self):
        """Test EmailService initialization with environment variables"""
        service = EmailService()
        
        self.assertEqual(service.mail_server, 'smtp.env.com')
        self.assertEqual(service.mail_port, 465)
        self.assertEqual(service.mail_username, '<EMAIL>')
        self.assertEqual(service.mail_password, 'envpassword')
        self.assertEqual(service.default_sender, '<EMAIL>')
    
    def test_incomplete_configuration(self):
        """Test behavior with incomplete email configuration"""
        incomplete_service = EmailService(mail_server='smtp.test.com')
        
        result = incomplete_service.send_email(
            recipient='<EMAIL>',
            subject='Test',
            body_html='<p>Test</p>'
        )
        
        self.assertFalse(result)
        self.assertTrue(any('Email configuration incomplete' in msg for msg in self.log_capture))
    
    @patch('email_service.smtplib.SMTP')
    def test_send_email_success(self, mock_smtp):
        """Test successful email sending"""
        # Mock SMTP server
        mock_server = MagicMock()
        mock_smtp.return_value.__enter__.return_value = mock_server
        
        result = self.email_service.send_email(
            recipient='<EMAIL>',
            subject='Test Subject',
            body_html='<p>Test HTML content</p>',
            body_text='Test plain content'
        )
        
        self.assertTrue(result)
        mock_smtp.assert_called_once_with('smtp.test.com', 587)
        mock_server.starttls.assert_called_once()
        mock_server.login.assert_called_once_with('<EMAIL>', 'testpassword')
        mock_server.send_message.assert_called_once()
    
    @patch('email_service.smtplib.SMTP')
    def test_send_email_without_text_body(self, mock_smtp):
        """Test email sending with HTML only (auto-generates text)"""
        mock_server = MagicMock()
        mock_smtp.return_value.__enter__.return_value = mock_server
        
        result = self.email_service.send_email(
            recipient='<EMAIL>',
            subject='Test Subject',
            body_html='<p>Test <br>HTML content</p>'
        )
        
        self.assertTrue(result)
        mock_server.send_message.assert_called_once()
    
    @patch('email_service.smtplib.SMTP')
    def test_send_email_smtp_error(self, mock_smtp):
        """Test email sending with SMTP error"""
        mock_smtp.side_effect = Exception("SMTP connection failed")
        
        result = self.email_service.send_email(
            recipient='<EMAIL>',
            subject='Test Subject',
            body_html='<p>Test content</p>'
        )
        
        self.assertFalse(result)
        self.assertTrue(any('Failed to send email' in msg for msg in self.log_capture))
    
    def test_send_email_no_sender(self):
        """Test email sending without sender configuration"""
        service = EmailService(
            mail_server='smtp.test.com',
            mail_username='<EMAIL>',
            mail_password='password'
        )
        
        result = service.send_email(
            recipient='<EMAIL>',
            subject='Test Subject',
            body_html='<p>Test content</p>'
        )
        
        self.assertFalse(result)
    
    def test_send_bulk_emails_empty_recipients(self):
        """Test bulk email sending with empty recipients list"""
        result = self.email_service.send_bulk_emails(
            recipients=[],
            subject='Test Subject',
            template='<p>Hello {name}</p>'
        )
        
        self.assertEqual(result, {'success': 0, 'failed': 0})
    
    @patch.object(EmailService, 'send_email')
    def test_send_bulk_emails_synchronous(self, mock_send_email):
        """Test synchronous bulk email sending"""
        mock_send_email.return_value = True
        
        recipients = [
            {'email': '<EMAIL>', 'name': 'User One'},
            {'email': '<EMAIL>', 'name': 'User Two'}
        ]
        
        result = self.email_service.send_bulk_emails(
            recipients=recipients,
            subject='Test Subject',
            template='<p>Hello {name}</p>',
            async_send=False
        )
        
        self.assertEqual(result['success'], 2)
        self.assertEqual(result['failed'], 0)
        self.assertEqual(len(result['recipients']), 2)
        self.assertEqual(mock_send_email.call_count, 2)
    
    @patch.object(EmailService, 'send_email')
    def test_send_bulk_emails_with_failures(self, mock_send_email):
        """Test bulk email sending with some failures"""
        # First call succeeds, second fails
        mock_send_email.side_effect = [True, False]
        
        recipients = [
            {'email': '<EMAIL>', 'name': 'User One'},
            {'email': '<EMAIL>', 'name': 'User Two'}
        ]
        
        result = self.email_service.send_bulk_emails(
            recipients=recipients,
            subject='Test Subject',
            template='<p>Hello {name}</p>',
            async_send=False
        )
        
        self.assertEqual(result['success'], 1)
        self.assertEqual(result['failed'], 1)
        self.assertEqual(len(result['recipients']), 2)
    
    def test_send_bulk_emails_missing_email(self):
        """Test bulk email sending with recipients missing email field"""
        recipients = [
            {'name': 'User One'},  # Missing email
            {'email': '<EMAIL>', 'name': 'User Two'}
        ]
        
        with patch.object(self.email_service, 'send_email', return_value=True):
            result = self.email_service.send_bulk_emails(
                recipients=recipients,
                subject='Test Subject',
                template='<p>Hello {name}</p>',
                async_send=False
            )
        
        self.assertEqual(result['success'], 1)
        self.assertEqual(result['failed'], 1)
    
    def test_send_bulk_emails_template_error(self):
        """Test bulk email sending with template formatting error"""
        recipients = [
            {'email': '<EMAIL>', 'name': 'User One'}
        ]
        
        # Template requires 'missing_var' which is not provided
        result = self.email_service.send_bulk_emails(
            recipients=recipients,
            subject='Test Subject',
            template='<p>Hello {name}, your {missing_var} is ready</p>',
            async_send=False
        )
        
        self.assertEqual(result['success'], 0)
        self.assertEqual(result['failed'], 1)
    
    @patch.object(EmailService, 'send_email')
    def test_send_bulk_emails_asynchronous(self, mock_send_email):
        """Test asynchronous bulk email sending"""
        mock_send_email.return_value = True
        
        recipients = [
            {'email': '<EMAIL>', 'name': 'User One'}
        ]
        
        result = self.email_service.send_bulk_emails(
            recipients=recipients,
            subject='Test Subject',
            template='<p>Hello {name}</p>',
            async_send=True
        )
        
        self.assertEqual(result['status'], 'sending')
        self.assertEqual(result['total'], 1)
    
    def test_get_default_templates_english(self):
        """Test getting default templates in English"""
        templates = self.email_service.get_default_templates('en')
        
        self.assertIn('application_received', templates)
        self.assertIn('interview_invitation', templates)
        self.assertIn('status_update', templates)
        
        # Check that templates contain expected placeholders
        self.assertIn('{name}', templates['application_received'])
        self.assertIn('{job_title}', templates['application_received'])
    
    def test_get_default_templates_german(self):
        """Test getting default templates in German"""
        templates = self.email_service.get_default_templates('de')
        
        self.assertIn('application_received', templates)
        self.assertIn('interview_invitation', templates)
        self.assertIn('status_update', templates)
        
        # Check German content
        self.assertIn('Sehr geehrte(r)', templates['application_received'])
        self.assertIn('Mit freundlichen Grüßen', templates['application_received'])
    
    def test_get_default_templates_fallback(self):
        """Test template fallback to English for unsupported language"""
        templates = self.email_service.get_default_templates('fr')  # French not supported
        
        # Should fallback to English
        self.assertIn('Dear {name}', templates['application_received'])
        self.assertNotIn('Sehr geehrte(r)', templates['application_received'])


def run_email_service_tests():
    """Run the email service test suite"""
    print("🧪 Testing Email Service")
    print("-" * 40)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestEmailService)
    
    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print(f"\n📊 Test Results:")
    print(f"   Tests run: {result.testsRun}")
    print(f"   Failures: {len(result.failures)}")
    print(f"   Errors: {len(result.errors)}")
    
    if result.failures:
        print(f"\n❌ Failures:")
        for test, traceback in result.failures:
            print(f"   {test}: {traceback}")
    
    if result.errors:
        print(f"\n💥 Errors:")
        for test, traceback in result.errors:
            print(f"   {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\n{'✅ All tests passed!' if success else '❌ Some tests failed!'}")
    
    return success


if __name__ == '__main__':
    run_email_service_tests()
