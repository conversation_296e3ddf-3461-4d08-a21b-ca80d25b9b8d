#!/usr/bin/env python3
"""
Debug the exact issue with CV matching
"""

from matcher import CV<PERSON>atch<PERSON>
from hr_database_working import HRDatabase


def debug_exact_calculation():
    """Debug the exact calculation step by step"""
    print("🔍 Debugging Exact Calculation")
    print("=" * 50)
    
    # Initialize
    hr_db = HRDatabase()
    cv_matcher = CVMatcher()
    
    # Get job and CVs
    job = hr_db.get_job_by_title("test4")
    cvs = hr_db.get_cvs_for_job("test4")
    
    print(f"📋 Job: {job.title}")
    print(f"📝 Job Description: {job.description}")
    print()
    
    # Test each CV individually
    for cv in cvs:
        name = "<PERSON>" if "<PERSON>" in cv.filename else "Max Müller"
        print(f"👤 Testing: {name} ({cv.filename})")
        print("-" * 40)
        
        # Calculate individual components
        tf_idf = cv_matcher.calculate_tf_idf_similarity(job.description, cv.content)
        keyword = cv_matcher.calculate_keyword_match(job.description, cv.content)
        skill = cv_matcher.calculate_skill_match(job.description, cv.content)
        
        # Calculate overall score manually
        overall = (skill * 0.5) + (keyword * 0.3) + (tf_idf * 0.2)
        
        print(f"📊 Individual Scores:")
        print(f"   TF-IDF:    {tf_idf:.4f} ({tf_idf*100:.1f}%) - Weight: 20%")
        print(f"   Keywords:  {keyword:.4f} ({keyword*100:.1f}%) - Weight: 30%")
        print(f"   Skills:    {skill:.4f} ({skill*100:.1f}%) - Weight: 50%")
        print(f"   Overall:   {overall:.4f} ({overall*100:.1f}%)")
        print()
        
        # Test the match method with just this CV
        single_result = cv_matcher.match(job.description, [cv.content])
        print(f"🎯 Match Method Result: {single_result[0][0]:.4f} ({single_result[0][0]*100:.1f}%)")
        print()
    
    # Test with both CVs together
    print("🔄 Testing Both CVs Together:")
    print("-" * 30)
    
    cv_contents = [cv.content for cv in cvs]
    match_results = cv_matcher.match(job.description, cv_contents)
    
    for i, (score, _) in enumerate(match_results):
        cv = cvs[i]
        name = "Maria Schmidt" if "Maria" in cv.filename else "Max Müller"
        print(f"{i+1}. {name}: {score:.4f} ({score*100:.1f}%)")
    
    # Check if the order is correct
    maria_idx = next(i for i, cv in enumerate(cvs) if "Maria" in cv.filename)
    max_idx = next(i for i, cv in enumerate(cvs) if "Max" in cv.filename)
    
    maria_score = match_results[maria_idx][0]
    max_score = match_results[max_idx][0]
    
    print(f"\n🔍 Direct Comparison:")
    print(f"   Maria: {maria_score:.4f} ({maria_score*100:.1f}%)")
    print(f"   Max:   {max_score:.4f} ({max_score*100:.1f}%)")
    
    if maria_score > max_score:
        print("   ✅ Ranking is correct: Maria > Max")
    else:
        print("   ❌ Ranking is incorrect: Max > Maria")
        print(f"   🔧 Difference: {(max_score - maria_score)*100:.1f}%")


def test_skill_detection():
    """Test skill detection specifically"""
    print("\n🎯 Testing Skill Detection")
    print("=" * 40)
    
    cv_matcher = CVMatcher()
    
    job_desc = """
    Stellenbeschreibung: Softwareentwickler (m/w/d)
    Wir suchen einen erfahrenen Softwareentwickler (m/w/d) zur Verstärkung unseres IT-Teams in
    Berlin.
    Aufgaben:
    - Entwicklung und Wartung von Webanwendungen mit Java und Spring Boot
    - Arbeit mit relationalen Datenbanken wie PostgreSQL
    - Zusammenarbeit mit Frontend-Entwicklern zur Integration von Benutzeroberflächen
    - Mitwirkung bei der Konzeption neuer Softwarelösungen
    Anforderungen:
    - Abgeschlossenes Studium der Informatik oder eine vergleichbare Qualifikation
    - Mindestens 3 Jahre Berufserfahrung in der Softwareentwicklung
    - Sehr gute Kenntnisse in Java, Spring Boot und REST-APIs
    - Erfahrung mit Git, CI/CD und agilen Methoden (Scrum)
    - Gute Deutsch- und Englischkenntnisse in Wort und Schrift
    """
    
    # Test with sample CV content
    maria_cv_sample = """
    Maria Schmidt
    Softwareentwicklerin
    
    Berufserfahrung:
    2020-heute: Senior Java Entwicklerin
    - Entwicklung von Webanwendungen mit Java und Spring Boot
    - Arbeit mit PostgreSQL-Datenbanken
    - Entwicklung von REST-Services
    - Nutzung von Git und Jenkins für CI/CD
    - Arbeit in einem agilen Scrum-Team
    
    Ausbildung:
    B.Sc. Informatik, Universität Berlin
    
    Kenntnisse:
    - Java, Spring Boot, REST-APIs
    - PostgreSQL, Git, Jenkins
    - Scrum, agile Methoden
    """
    
    max_cv_sample = """
    Max Müller
    Systemadministrator
    
    Berufserfahrung:
    2018-heute: IT-Administrator
    - Windows Server Administration
    - Active Directory Verwaltung
    - Netzwerk-Konfiguration
    - IT-Support
    
    Ausbildung:
    Fachinformatiker Systemintegration
    
    Kenntnisse:
    - Windows Server, Active Directory
    - Netzwerk, IT-Support
    """
    
    print("📊 Skill Detection Results:")
    print()
    
    for name, cv_content in [("Maria Schmidt", maria_cv_sample), ("Max Müller", max_cv_sample)]:
        print(f"👤 {name}:")
        skill_score = cv_matcher.calculate_skill_match(job_desc, cv_content)
        print(f"   Skill Score: {skill_score:.4f} ({skill_score*100:.1f}%)")
        
        # Get detailed explanation
        explanation = cv_matcher.get_match_explanation(job_desc, cv_content)
        print(f"   Common Keywords: {explanation['common_keywords'][:5]}")
        print()


if __name__ == "__main__":
    debug_exact_calculation()
    test_skill_detection()
