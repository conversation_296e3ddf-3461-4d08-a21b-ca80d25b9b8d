#!/usr/bin/env python3
"""
Secure Email System Setup for BAUCH HR Management System
This script helps you configure Listmonk with proper security settings
"""

import os
import secrets
import getpass
import shutil
from pathlib import Path


def generate_secure_password(length=16):
    """Generate a secure random password"""
    alphabet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def get_email_provider_settings():
    """Get SMTP settings for common email providers"""
    providers = {
        "1": {
            "name": "Gmail",
            "host": "smtp.gmail.com",
            "port": 587,
            "tls": True,
            "instructions": [
                "1. Enable 2-factor authentication on your Gmail account",
                "2. Go to Google Account settings > Security > App passwords",
                "3. Generate an app password for 'Mail'",
                "4. Use this app password instead of your regular password"
            ]
        },
        "2": {
            "name": "Outlook/Hotmail",
            "host": "smtp-mail.outlook.com",
            "port": 587,
            "tls": True,
            "instructions": [
                "1. Use your regular Outlook email and password",
                "2. Make sure 'Less secure app access' is enabled if needed"
            ]
        },
        "3": {
            "name": "Yahoo Mail",
            "host": "smtp.mail.yahoo.com",
            "port": 587,
            "tls": True,
            "instructions": [
                "1. Enable 2-factor authentication",
                "2. Generate an app password for mail",
                "3. Use the app password instead of your regular password"
            ]
        },
        "4": {
            "name": "Custom SMTP",
            "host": "",
            "port": 587,
            "tls": True,
            "instructions": [
                "1. Contact your email provider for SMTP settings",
                "2. You'll need: SMTP server, port, username, and password"
            ]
        }
    }
    return providers


def setup_environment_file():
    """Create secure .env file"""
    print("\n🔐 Setting up secure environment configuration...")
    print("=" * 60)
    
    # Check if .env already exists
    if os.path.exists('.env'):
        overwrite = input("⚠️  .env file already exists. Overwrite? (y/N): ").lower()
        if overwrite != 'y':
            print("Skipping .env setup.")
            return False
    
    # Generate secure values
    secret_key = secrets.token_hex(32)
    listmonk_password = generate_secure_password(20)
    db_password = generate_secure_password(16)
    
    print(f"✅ Generated secure SECRET_KEY")
    print(f"✅ Generated secure Listmonk password: {listmonk_password}")
    print(f"✅ Generated secure database password: {db_password}")
    
    # Get email configuration
    print("\n📧 Email Provider Configuration")
    print("-" * 30)
    providers = get_email_provider_settings()
    
    for key, provider in providers.items():
        print(f"{key}. {provider['name']}")
    
    choice = input("\nSelect your email provider (1-4): ").strip()
    
    if choice not in providers:
        print("❌ Invalid choice. Using custom SMTP.")
        choice = "4"
    
    provider = providers[choice]
    
    print(f"\n📋 Instructions for {provider['name']}:")
    for instruction in provider['instructions']:
        print(f"   {instruction}")
    
    # Get email credentials
    print(f"\n🔑 Enter your email credentials for {provider['name']}:")
    
    if choice == "4":  # Custom SMTP
        smtp_host = input("SMTP Host: ").strip()
        smtp_port = input("SMTP Port (587): ").strip() or "587"
    else:
        smtp_host = provider['host']
        smtp_port = str(provider['port'])
    
    smtp_username = input("Email Address: ").strip()
    smtp_password = getpass.getpass("Email Password/App Password: ").strip()
    sender_email = input(f"Sender Email ({smtp_username}): ").strip() or smtp_username
    
    # Create .env file
    env_content = f"""# BAUCH HR Management System - Environment Configuration
# Generated by setup_secure_email.py
# KEEP THIS FILE SECURE - DO NOT COMMIT TO VERSION CONTROL

# Flask Application
FLASK_ENV=production
SECRET_KEY={secret_key}
DEBUG=False

# Database
DATABASE_URL=sqlite:///hr_database.db

# Listmonk Configuration
LISTMONK_URL=http://localhost:9000
LISTMONK_USERNAME=admin
LISTMONK_PASSWORD={listmonk_password}

# SMTP Configuration (for Listmonk)
SMTP_HOST={smtp_host}
SMTP_PORT={smtp_port}
SMTP_USERNAME={smtp_username}
SMTP_PASSWORD={smtp_password}
SMTP_TLS=true

# Email Sender
DEFAULT_SENDER_EMAIL={sender_email}
DEFAULT_SENDER_NAME=BAUCH HR Team

# Listmonk Database
LISTMONK_DB_HOST=localhost
LISTMONK_DB_PORT=5432
LISTMONK_DB_USER=listmonk_user
LISTMONK_DB_PASSWORD={db_password}
LISTMONK_DB_NAME=listmonk

# Security
SESSION_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax
PERMANENT_SESSION_LIFETIME=3600

# File Upload
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=uploads

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/hr_system.log
"""
    
    try:
        with open('.env', 'w') as f:
            f.write(env_content)
        print("\n✅ Created secure .env file")
        return True
    except Exception as e:
        print(f"\n❌ Failed to create .env file: {e}")
        return False


def setup_listmonk_config():
    """Create secure Listmonk configuration"""
    print("\n⚙️  Setting up Listmonk configuration...")
    print("=" * 50)
    
    config_path = Path("email_system/config.toml")
    secure_template = Path("email_system/config.secure.toml")
    
    if not secure_template.exists():
        print("❌ Secure template not found. Please run this script from the project root.")
        return False
    
    if config_path.exists():
        overwrite = input("⚠️  Listmonk config.toml already exists. Overwrite? (y/N): ").lower()
        if overwrite != 'y':
            print("Skipping Listmonk config setup.")
            return False
    
    try:
        # Copy secure template to config.toml
        shutil.copy2(secure_template, config_path)
        print("✅ Created Listmonk configuration from secure template")
        print(f"📝 Please edit {config_path} and update the following:")
        print("   - admin_password (line 9)")
        print("   - database password (line 16)")
        print("   - SMTP credentials (lines 25-30)")
        return True
    except Exception as e:
        print(f"❌ Failed to create Listmonk config: {e}")
        return False


def main():
    """Main setup function"""
    print("🚀 BAUCH HR Management System - Secure Email Setup")
    print("=" * 60)
    print("This script will help you configure secure email settings.")
    print("Make sure you have your email provider credentials ready.")
    
    # Create necessary directories
    os.makedirs("logs", exist_ok=True)
    os.makedirs("uploads", exist_ok=True)
    os.makedirs("email_system", exist_ok=True)
    
    success = True
    
    # Setup environment file
    if not setup_environment_file():
        success = False
    
    # Setup Listmonk configuration
    if not setup_listmonk_config():
        success = False
    
    if success:
        print("\n🎉 Setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Review and update the generated configuration files")
        print("2. Start PostgreSQL database (if using)")
        print("3. Run Listmonk: cd email_system && ./listmonk --install")
        print("4. Start Listmonk: cd email_system && ./listmonk")
        print("5. Access Listmonk web interface: http://localhost:9000")
        print("6. Test your email configuration")
        
        print("\n🔒 Security reminders:")
        print("- Never commit .env file to version control")
        print("- Use strong, unique passwords")
        print("- Enable 2FA on your email accounts")
        print("- Regularly update your credentials")
        print("- Monitor email sending logs")
    else:
        print("\n❌ Setup completed with errors. Please check the messages above.")


if __name__ == "__main__":
    main()
