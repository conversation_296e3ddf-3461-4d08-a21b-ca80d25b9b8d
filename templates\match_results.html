{% extends 'base.html' %}

{% block title %}Match Results - HR Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-chart-bar me-2"></i>Match Results
    </h1>
    <div class="btn-group" role="group">
        {% if results %}
        <button class="btn btn-success" onclick="showExtractModal('{{ job.title }}', {{ results|length }})">
            <i class="fas fa-file-excel me-1"></i> Extract to Excel
        </button>
        {% endif %}
        <a href="{{ url_for('match') }}" class="btn btn-outline-primary">
            <i class="fas fa-redo me-1"></i> New Match
        </a>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0">Job: {{ job.title }}</h5>
    </div>
    <div class="card-body">
        <h6>Description:</h6>
        <p>{{ job.description|truncate(300) }}</p>
    </div>
</div>

<div class="card">
    <div class="card-header bg-success text-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-list-ol me-2"></i>Ranked Candidates
        </h5>
    </div>
    <div class="card-body">
        {% if results %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th scope="col">Rank</th>
                            <th scope="col">Candidate</th>
                            <th scope="col">Match Score</th>
                            <th scope="col">Visual Score</th>
                            <th scope="col">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for result in results|sort(attribute='score', reverse=True) %}
                            <tr>
                                <th scope="row">{{ loop.index }}</th>
                                <td>{{ result.filename }}</td>
                                <td>{{ "%.2f"|format(result.score) }}%</td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        {% set score_class = 'bg-danger' if result.score < 50 else ('bg-warning' if result.score < 75 else 'bg-success') %}
                                        <div class="progress-bar {{ score_class }}" role="progressbar"
                                             style="width: {{ result.score }}%;"
                                             aria-valuenow="{{ result.score }}" aria-valuemin="0" aria-valuemax="100">
                                            {{ "%.0f"|format(result.score) }}%
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if result.cv_id %}
                                    <a href="{{ url_for('view_cv', cv_id=result.cv_id) }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> View CV
                                    </a>
                                    {% else %}
                                    <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <div class="alert alert-info mt-4">
                <i class="fas fa-info-circle me-2"></i>
                <strong>How to interpret scores:</strong>
                <ul class="mb-0 mt-2">
                    <li><span class="badge bg-success">75-100%</span> - Excellent match, highly recommended candidate</li>
                    <li><span class="badge bg-warning">50-74%</span> - Good match, consider interviewing</li>
                    <li><span class="badge bg-danger">0-49%</span> - Poor match, may not meet job requirements</li>
                </ul>
            </div>
        {% else %}
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>No matching results found. Please make sure there are CVs uploaded for this job.
            </div>
        {% endif %}
    </div>
</div>

<!-- Extract to Excel Modal -->
<div class="modal fade" id="extractModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-file-excel me-2"></i>Extract Match Results to Excel
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Extract candidate data from match results for job: <strong id="extractJobTitle"></strong></p>
                <p class="text-muted">Total matched candidates: <span id="extractTotalCVs"></span></p>

                <div class="mb-3">
                    <label for="extractCount" class="form-label">Number of candidates to extract:</label>
                    <select class="form-select" id="extractCount">
                        <option value="5">Top 5 candidates</option>
                        <option value="10">Top 10 candidates</option>
                        <option value="15">Top 15 candidates</option>
                        <option value="all">All candidates</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label class="form-label">Data to extract:</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="extractName" checked>
                        <label class="form-check-label" for="extractName">Name</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="extractEmail" checked>
                        <label class="form-check-label" for="extractEmail">Email</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="extractPhone" checked>
                        <label class="form-check-label" for="extractPhone">Phone</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="extractExperience" checked>
                        <label class="form-check-label" for="extractExperience">Experience</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="extractSkills" checked>
                        <label class="form-check-label" for="extractSkills">Skills</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="extractEducation">
                        <label class="form-check-label" for="extractEducation">Education</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="extractMatchScore" checked>
                        <label class="form-check-label" for="extractMatchScore">Match Score</label>
                    </div>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Note:</strong> Candidates will be extracted in order of their match score (highest first).
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="extractToExcel()">
                    <i class="fas fa-download me-1"></i>Download Excel
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentJobTitle = '';

function showExtractModal(jobTitle, cvCount) {
    currentJobTitle = jobTitle;
    document.getElementById('extractJobTitle').textContent = jobTitle;
    document.getElementById('extractTotalCVs').textContent = cvCount;

    // Update the select options based on available CVs
    const selectElement = document.getElementById('extractCount');
    selectElement.innerHTML = '';

    if (cvCount >= 5) {
        selectElement.innerHTML += '<option value="5">Top 5 candidates</option>';
    }
    if (cvCount >= 10) {
        selectElement.innerHTML += '<option value="10">Top 10 candidates</option>';
    }
    if (cvCount >= 15) {
        selectElement.innerHTML += '<option value="15">Top 15 candidates</option>';
    }
    selectElement.innerHTML += '<option value="all">All ' + cvCount + ' candidates</option>';

    new bootstrap.Modal(document.getElementById('extractModal')).show();
}

function extractToExcel() {
    const count = document.getElementById('extractCount').value;
    const fields = [];

    if (document.getElementById('extractName').checked) fields.push('name');
    if (document.getElementById('extractEmail').checked) fields.push('email');
    if (document.getElementById('extractPhone').checked) fields.push('phone');
    if (document.getElementById('extractExperience').checked) fields.push('experience');
    if (document.getElementById('extractSkills').checked) fields.push('skills');
    if (document.getElementById('extractEducation').checked) fields.push('education');
    if (document.getElementById('extractMatchScore').checked) fields.push('match_score');

    if (fields.length === 0) {
        alert('Please select at least one field to extract.');
        return;
    }

    // Create download URL
    const params = new URLSearchParams({
        job: currentJobTitle,
        count: count,
        fields: fields.join(',')
    });

    // Trigger download
    window.location.href = '/extract-excel?' + params.toString();

    // Close modal
    bootstrap.Modal.getInstance(document.getElementById('extractModal')).hide();
}
</script>

{% endblock %}
