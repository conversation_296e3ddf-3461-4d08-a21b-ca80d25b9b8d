# 🚀 BAUCH HR System - Quick Reference

## Essential Commands

### Service Management
```bash
# Check service status
systemctl --user status bauch-hr.service

# Start service
systemctl --user start bauch-hr.service

# Stop service
systemctl --user stop bauch-hr.service

# Restart service
systemctl --user restart bauch-hr.service

# View live logs
journalctl --user -u bauch-hr.service -f
```

### Application Management
```bash
# Navigate to project
cd ~/bauch-hr-system

# Activate virtual environment
source venv/bin/activate

# Run in development mode
python app.py

# Run with gunicorn (production)
gunicorn -w 4 -b 127.0.0.1:8000 app:app

# Create admin user
python create_admin_user.py
```

### Updates & Maintenance
```bash
# Update from GitHub
cd ~/bauch-hr-system
git pull origin main
systemctl --user restart bauch-hr.service

# Create backup
~/backup-bauch.sh

# Check disk space
df -h

# Check memory usage
free -h
```

### Troubleshooting
```bash
# Test if app responds
curl http://localhost:8000

# Check if port is listening
ss -tlnp | grep 8000

# View recent logs
journalctl --user -u bauch-hr.service -n 50

# Check file permissions
ls -la ~/bauch-hr-system/
```

## Default Credentials
- **Username:** admin
- **Password:** admin123
- **⚠️ Change immediately after first login!**

## Important File Locations
- **Project:** `~/bauch-hr-system/`
- **Database:** `~/bauch-hr-system/hr_database.db`
- **Uploads:** `~/bauch-hr-system/uploads/`
- **Config:** `~/bauch-hr-system/.env`
- **Service:** `~/.config/systemd/user/bauch-hr.service`
- **Web Config:** `~/html/.htaccess`
- **Backups:** `~/backups/`

## URLs
- **Application:** `https://YOURUSERNAME.YOURSERVER.uberspace.de`
- **Local Test:** `http://localhost:5000` (dev mode)
- **Production:** `http://localhost:8000` (gunicorn)

## Emergency Recovery
```bash
# If service won't start
systemctl --user daemon-reload
systemctl --user restart bauch-hr.service

# If database is corrupted
cd ~/bauch-hr-system
rm hr_database.db
python create_admin_user.py

# If permissions are wrong
chmod 755 ~/bauch-hr-system
chmod 755 ~/bauch-hr-system/uploads
chmod 600 ~/bauch-hr-system/.env
```

## Support Checklist
Before asking for help:
- [ ] Check service status
- [ ] Review recent logs
- [ ] Test local access
- [ ] Verify file permissions
- [ ] Check disk space
