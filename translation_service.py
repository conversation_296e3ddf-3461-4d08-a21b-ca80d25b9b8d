"""
Translation Service for Bilingual HR Management System
Provides translation utilities and UI text management
"""

from typing import Dict, Optional
from googletrans import Translator
import logging


class TranslationService:
    def __init__(self):
        self.translator = Translator()
        self.logger = logging.getLogger(__name__)
        
        # Cache for translations to avoid repeated API calls
        self.translation_cache = {}
        
        # UI translations dictionary
        self.ui_translations = {
            'en': {
                # Navigation
                'home': 'Home',
                'jobs': 'Jobs',
                'cvs': 'CVs',
                'match': 'Match',
                'match_cvs': 'Match CVs',
                'logout': 'Logout',
                
                # Common actions
                'add': 'Add',
                'edit': 'Edit',
                'delete': 'Delete',
                'save': 'Save',
                'cancel': 'Cancel',
                'upload': 'Upload',
                'download': 'Download',
                'search': 'Search',
                'filter': 'Filter',
                'view': 'View',
                'back': 'Back',
                
                # Job management
                'add_job': 'Add Job',
                'job_title': 'Job Title',
                'job_description': 'Job Description',
                'job_platform': 'Platform',
                'job_url': 'Job URL',
                'start_date': 'Start Date',
                'end_date': 'End Date',
                'status': 'Status',
                'responsible_person': 'Responsible Person',
                'main_responsible': 'Main Responsible',
                'additional_responsible': 'Additional Responsible',
                'days_remaining': 'Days Remaining',
                'cv_count': 'CV Count',
                
                # CV management
                'upload_cv': 'Upload CV',
                'candidate_name': 'Candidate Name',
                'cv_file': 'CV File',
                'select_job': 'Select Job',
                'file_upload': 'File Upload',
                
                # Matching
                'match_results': 'Match Results',
                'match_score': 'Match Score',
                'overall_match': 'Overall Match',
                'content_similarity': 'Content Similarity',
                'keyword_match': 'Keyword Match',
                'skill_match': 'Skill Match',
                'language_bonus': 'Language Bonus',
                'recommendations': 'Recommendations',
                
                # Personal information
                'name': 'Name',
                'email': 'Email',
                'phone': 'Phone',
                'experience': 'Experience',
                'skills': 'Skills',
                'education': 'Education',
                
                # Status messages
                'success': 'Success',
                'error': 'Error',
                'warning': 'Warning',
                'info': 'Info',
                'loading': 'Loading...',
                'no_data': 'No data available',
                'not_found': 'Not found',
                'invalid_input': 'Invalid input',
                
                # Language
                'language': 'Language',
                'english': 'English',
                'german': 'German',
                'bilingual': 'Bilingual',
                'language_detected': 'Language Detected',
                'switch_language': 'Switch Language',
                
                # Forms
                'required_field': 'Required field',
                'optional_field': 'Optional field',
                'please_select': 'Please select...',
                'choose_file': 'Choose file',
                'no_file_chosen': 'No file chosen',
                
                # Dashboard
                'dashboard': 'Dashboard',
                'total_jobs': 'Total Jobs',
                'total_cvs': 'Total CVs',
                'recent_activity': 'Recent Activity',
                'quick_actions': 'Quick Actions',
                
                # Footer
                'copyright': 'BAUCH HR Management System',
                'all_rights_reserved': 'All rights reserved',

                # Additional messages
                'cv_uploaded_successfully': 'CV for {} uploaded successfully!',
                'job_added_successfully': 'Job "{}" added successfully!',
                'match_completed': 'Matching completed successfully',
                'no_matches_found': 'No matches found',
                'processing_cv': 'Processing CV...',
                'analyzing_content': 'Analyzing content...',

                # Password and security
                'change_password': 'Change Password',
                'current_password': 'Current Password',
                'new_password': 'New Password',
                'confirm_new_password': 'Confirm New Password',
                'password_requirements': 'Password must be at least 8 characters long',
                'security_tips': 'Security Tips',
                'tip_password_length': 'Use at least 8 characters',
                'tip_password_complexity': 'Include uppercase, lowercase, numbers, and symbols',
                'tip_password_unique': 'Use a unique password for this account',
                'tip_password_regular_change': 'Change your password regularly',
                'password_weak': 'Weak',
                'password_medium': 'Medium',
                'password_good': 'Good',
                'password_strong': 'Strong',
                'passwords_match': 'Passwords match',
                'passwords_dont_match': 'Passwords do not match',
                'passwords_must_match': 'Passwords must match',
                'password_too_weak': 'Password is too weak. Please choose a stronger password.',
                'changing_password': 'Changing Password',
                'password_changed_successfully': 'Password changed successfully!',
                'current_password_incorrect': 'Current password is incorrect',
                'account_locked': 'Account is temporarily locked due to failed login attempts'
            },
            'de': {
                # Navigation
                'home': 'Startseite',
                'jobs': 'Stellenanzeigen',
                'cvs': 'Lebensläufe',
                'match': 'Abgleich',
                'match_cvs': 'Lebensläufe abgleichen',
                'logout': 'Abmelden',
                
                # Common actions
                'add': 'Hinzufügen',
                'edit': 'Bearbeiten',
                'delete': 'Löschen',
                'save': 'Speichern',
                'cancel': 'Abbrechen',
                'upload': 'Hochladen',
                'download': 'Herunterladen',
                'search': 'Suchen',
                'filter': 'Filtern',
                'view': 'Anzeigen',
                'back': 'Zurück',
                
                # Job management
                'add_job': 'Stellenanzeige hinzufügen',
                'job_title': 'Stellentitel',
                'job_description': 'Stellenbeschreibung',
                'job_platform': 'Plattform',
                'job_url': 'Stellen-URL',
                'start_date': 'Startdatum',
                'end_date': 'Enddatum',
                'status': 'Status',
                'responsible_person': 'Verantwortliche Person',
                'main_responsible': 'Hauptverantwortlicher',
                'additional_responsible': 'Zusätzlich Verantwortliche',
                'days_remaining': 'Verbleibende Tage',
                'cv_count': 'Anzahl Lebensläufe',
                
                # CV management
                'upload_cv': 'Lebenslauf hochladen',
                'candidate_name': 'Kandidatenname',
                'cv_file': 'Lebenslauf-Datei',
                'select_job': 'Stellenanzeige auswählen',
                'file_upload': 'Datei-Upload',
                
                # Matching
                'match_results': 'Abgleich-Ergebnisse',
                'match_score': 'Übereinstimmungs-Score',
                'overall_match': 'Gesamtübereinstimmung',
                'content_similarity': 'Inhaltsähnlichkeit',
                'keyword_match': 'Schlüsselwort-Übereinstimmung',
                'skill_match': 'Fähigkeiten-Übereinstimmung',
                'language_bonus': 'Sprachbonus',
                'recommendations': 'Empfehlungen',
                
                # Personal information
                'name': 'Name',
                'email': 'E-Mail',
                'phone': 'Telefon',
                'experience': 'Berufserfahrung',
                'skills': 'Fähigkeiten',
                'education': 'Ausbildung',
                
                # Status messages
                'success': 'Erfolgreich',
                'error': 'Fehler',
                'warning': 'Warnung',
                'info': 'Information',
                'loading': 'Lädt...',
                'no_data': 'Keine Daten verfügbar',
                'not_found': 'Nicht gefunden',
                'invalid_input': 'Ungültige Eingabe',
                
                # Language
                'language': 'Sprache',
                'english': 'Englisch',
                'german': 'Deutsch',
                'bilingual': 'Zweisprachig',
                'language_detected': 'Erkannte Sprache',
                'switch_language': 'Sprache wechseln',
                
                # Forms
                'required_field': 'Pflichtfeld',
                'optional_field': 'Optionales Feld',
                'please_select': 'Bitte auswählen...',
                'choose_file': 'Datei auswählen',
                'no_file_chosen': 'Keine Datei ausgewählt',
                
                # Dashboard
                'dashboard': 'Dashboard',
                'total_jobs': 'Stellenanzeigen gesamt',
                'total_cvs': 'Lebensläufe gesamt',
                'recent_activity': 'Letzte Aktivitäten',
                'quick_actions': 'Schnellaktionen',
                
                # Footer
                'copyright': 'BAUCH HR Management System',
                'all_rights_reserved': 'Alle Rechte vorbehalten',

                # Additional messages
                'cv_uploaded_successfully': 'Lebenslauf für {} erfolgreich hochgeladen!',
                'job_added_successfully': 'Stellenanzeige "{}" erfolgreich hinzugefügt!',
                'match_completed': 'Abgleich erfolgreich abgeschlossen',
                'no_matches_found': 'Keine Übereinstimmungen gefunden',
                'processing_cv': 'Lebenslauf wird verarbeitet...',
                'analyzing_content': 'Inhalt wird analysiert...',

                # Password and security
                'change_password': 'Passwort ändern',
                'current_password': 'Aktuelles Passwort',
                'new_password': 'Neues Passwort',
                'confirm_new_password': 'Neues Passwort bestätigen',
                'password_requirements': 'Passwort muss mindestens 8 Zeichen lang sein',
                'security_tips': 'Sicherheitstipps',
                'tip_password_length': 'Verwenden Sie mindestens 8 Zeichen',
                'tip_password_complexity': 'Verwenden Sie Groß-, Kleinbuchstaben, Zahlen und Symbole',
                'tip_password_unique': 'Verwenden Sie ein einzigartiges Passwort für dieses Konto',
                'tip_password_regular_change': 'Ändern Sie Ihr Passwort regelmäßig',
                'password_weak': 'Schwach',
                'password_medium': 'Mittel',
                'password_good': 'Gut',
                'password_strong': 'Stark',
                'passwords_match': 'Passwörter stimmen überein',
                'passwords_dont_match': 'Passwörter stimmen nicht überein',
                'passwords_must_match': 'Passwörter müssen übereinstimmen',
                'password_too_weak': 'Passwort ist zu schwach. Bitte wählen Sie ein stärkeres Passwort.',
                'changing_password': 'Passwort wird geändert',
                'password_changed_successfully': 'Passwort erfolgreich geändert!',
                'current_password_incorrect': 'Aktuelles Passwort ist falsch',
                'account_locked': 'Konto ist aufgrund fehlgeschlagener Anmeldeversuche vorübergehend gesperrt'
            }
        }
    
    def translate_text(self, text: str, target_language: str, source_language: str = 'auto') -> str:
        """Translate text using Google Translate API"""
        if not text or not text.strip():
            return text
        
        # Check cache first
        cache_key = f"{text}_{source_language}_{target_language}"
        if cache_key in self.translation_cache:
            return self.translation_cache[cache_key]
        
        try:
            # Map language codes
            lang_map = {'german': 'de', 'english': 'en', 'de': 'de', 'en': 'en'}
            target_lang = lang_map.get(target_language, target_language)
            source_lang = lang_map.get(source_language, source_language)
            
            # Translate
            result = self.translator.translate(text, dest=target_lang, src=source_lang)
            translated_text = result.text
            
            # Cache the result
            self.translation_cache[cache_key] = translated_text
            
            return translated_text
            
        except Exception as e:
            self.logger.error(f"Translation error: {e}")
            return text  # Return original text if translation fails
    
    def get_ui_text(self, key: str, language: str = 'en') -> str:
        """Get UI text in specified language"""
        lang_code = 'de' if language in ['german', 'de'] else 'en'
        
        if lang_code in self.ui_translations and key in self.ui_translations[lang_code]:
            return self.ui_translations[lang_code][key]
        
        # Fallback to English if German translation not found
        if key in self.ui_translations['en']:
            return self.ui_translations['en'][key]
        
        # Return key if no translation found
        return key.replace('_', ' ').title()
    
    def get_all_ui_texts(self, language: str = 'en') -> Dict[str, str]:
        """Get all UI texts for a language"""
        lang_code = 'de' if language in ['german', 'de'] else 'en'
        return self.ui_translations.get(lang_code, self.ui_translations['en'])
    
    def translate_job_description(self, job_description: str, target_language: str) -> str:
        """Translate job description with context awareness"""
        if not job_description:
            return job_description
        
        # Split into paragraphs for better translation
        paragraphs = job_description.split('\n\n')
        translated_paragraphs = []
        
        for paragraph in paragraphs:
            if paragraph.strip():
                translated = self.translate_text(paragraph.strip(), target_language)
                translated_paragraphs.append(translated)
        
        return '\n\n'.join(translated_paragraphs)
    
    def translate_cv_content(self, cv_content: str, target_language: str) -> str:
        """Translate CV content with structure preservation"""
        if not cv_content:
            return cv_content
        
        # Split into lines and translate while preserving structure
        lines = cv_content.split('\n')
        translated_lines = []
        
        for line in lines:
            line = line.strip()
            if line:
                # Skip lines that are mostly numbers, dates, or email addresses
                if not self._should_translate_line(line):
                    translated_lines.append(line)
                else:
                    translated = self.translate_text(line, target_language)
                    translated_lines.append(translated)
            else:
                translated_lines.append('')
        
        return '\n'.join(translated_lines)
    
    def _should_translate_line(self, line: str) -> bool:
        """Determine if a line should be translated"""
        # Don't translate lines that are mostly:
        # - Email addresses
        # - Phone numbers
        # - Dates
        # - URLs
        # - Numbers
        
        import re
        
        # Email pattern
        if re.search(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', line):
            return False
        
        # Phone pattern
        if re.search(r'[\+]?[\d\s\-\(\)]{8,}', line):
            return False
        
        # Date pattern
        if re.search(r'\b\d{1,2}[\.\/\-]\d{1,2}[\.\/\-]\d{2,4}\b', line):
            return False
        
        # URL pattern
        if re.search(r'https?://\S+', line):
            return False
        
        # Mostly numbers
        if len(re.findall(r'\d', line)) > len(line) * 0.5:
            return False
        
        return True
    
    def get_language_name(self, language_code: str, display_language: str = 'en') -> str:
        """Get language name in specified display language"""
        language_names = {
            'en': {
                'en': 'English',
                'de': 'German',
                'german': 'German',
                'english': 'English',
                'mixed': 'Mixed',
                'unknown': 'Unknown'
            },
            'de': {
                'en': 'Englisch',
                'de': 'Deutsch',
                'german': 'Deutsch',
                'english': 'Englisch',
                'mixed': 'Gemischt',
                'unknown': 'Unbekannt'
            }
        }
        
        display_lang = 'de' if display_language in ['german', 'de'] else 'en'
        return language_names[display_lang].get(language_code, language_code)


# Global translation service instance
translation_service = TranslationService()


# Utility functions for easy integration
def translate(text: str, target_language: str, source_language: str = 'auto') -> str:
    """Quick translation function"""
    return translation_service.translate_text(text, target_language, source_language)


def get_text(key: str, language: str = 'en') -> str:
    """Quick UI text retrieval function"""
    return translation_service.get_ui_text(key, language)


def get_language_display_name(language_code: str, display_language: str = 'en') -> str:
    """Quick language name function"""
    return translation_service.get_language_name(language_code, display_language)
