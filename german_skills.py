"""
German Skills and Keywords Module
Contains German-specific skill keywords, patterns, and language processing utilities
"""

from typing import List, Dict, Set


class GermanSkills:
    def __init__(self):
        # German technical skills
        self.technical_skills = [
            # Programming languages (same in German)
            'python', 'java', 'javascript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust',
            'typescript', 'kotlin', 'swift', 'scala', 'r', 'matlab', 'sql',
            
            # German IT terms
            'programmierung', 'softwareentwicklung', 'webentwicklung', 'datenbank',
            'algorithmus', 'datenstrukturen', 'objektorientiert', 'agile entwicklung',
            'scrum', 'kanban', 'devops', 'continuous integration', 'versionskontrolle',
            'git', 'docker', 'kubernetes', 'cloud computing', 'aws', 'azure',
            
            # Web technologies with German terms
            'html', 'css', 'react', 'angular', 'vue.js', 'node.js', 'express',
            'django', 'flask', 'spring', 'hibernate', 'laravel', 'symfony',
            'bootstrap', 'jquery', 'frontend', 'backend', 'fullstack',
            'webdesign', 'responsive design', 'benutzeroberfläche', 'ui/ux',
            
            # Databases with German terms
            'mysql', 'postgresql', 'mongodb', 'redis', 'sqlite', 'oracle',
            'datenbankdesign', 'datenbankadministration', 'sql server',
            'nosql', 'big data', 'datenanalyse', 'business intelligence',
            
            # Tools and platforms
            'linux', 'windows', 'macos', 'jenkins', 'gitlab', 'github',
            'jira', 'confluence', 'slack', 'teams', 'visual studio',
            'intellij', 'eclipse', 'vscode', 'projektmanagement-tools',
            
            # German-specific methodologies
            'agile methoden', 'wasserfallmodell', 'v-modell', 'testgetriebene entwicklung',
            'verhaltensgetriebene entwicklung', 'kontinuierliche integration',
            'kontinuierliche bereitstellung', 'microservices', 'monolith',
            
            # Quality assurance in German
            'qualitätssicherung', 'softwaretest', 'unit tests', 'integrationstests',
            'systemtests', 'akzeptanztests', 'automatisierte tests', 'manuelles testen',
            'testautomatisierung', 'selenium', 'junit', 'testng'
        ]
        
        # German soft skills
        self.soft_skills = [
            # Leadership and management
            'führung', 'führungskompetenz', 'teamleitung', 'projektleitung',
            'personalführung', 'mitarbeiterführung', 'management', 'coaching',
            'mentoring', 'delegation', 'entscheidungsfindung', 'strategisches denken',
            
            # Communication skills
            'kommunikation', 'kommunikationsfähigkeit', 'präsentation',
            'präsentationsfähigkeit', 'verhandlung', 'verhandlungsgeschick',
            'kundenbetreuung', 'kundenkommunikation', 'schriftliche kommunikation',
            'mündliche kommunikation', 'interkulturelle kommunikation',
            
            # Teamwork and collaboration
            'teamarbeit', 'teamfähigkeit', 'zusammenarbeit', 'kooperation',
            'kollaboration', 'konfliktlösung', 'konfliktmanagement',
            'empathie', 'soziale kompetenz', 'zwischenmenschliche fähigkeiten',
            
            # Problem solving and analysis
            'problemlösung', 'problemlösungskompetenz', 'analytisches denken',
            'analyse', 'kritisches denken', 'kreativität', 'innovation',
            'lösungsorientierung', 'strukturiertes arbeiten', 'systematisches vorgehen',
            
            # Project and time management
            'projektmanagement', 'zeitmanagement', 'selbstorganisation',
            'prioritätensetzung', 'planung', 'organisation', 'koordination',
            'multitasking', 'stressresistenz', 'belastbarkeit',
            
            # Learning and adaptation
            'lernbereitschaft', 'weiterbildungsbereitschaft', 'anpassungsfähigkeit',
            'flexibilität', 'eigeninitiative', 'selbstständigkeit',
            'verantwortungsbewusstsein', 'zuverlässigkeit', 'sorgfalt',
            
            # Customer and business skills
            'kundenorientierung', 'serviceorientierung', 'geschäftssinn',
            'unternehmerisches denken', 'kostenbewusstsein', 'qualitätsbewusstsein'
        ]
        
        # German education keywords
        self.education_keywords = [
            'universität', 'hochschule', 'fachhochschule', 'technische universität',
            'bachelor', 'master', 'diplom', 'magister', 'promotion', 'doktor',
            'studium', 'studiengang', 'fachrichtung', 'schwerpunkt',
            'abschluss', 'abschlussnote', 'note', 'zeugnis', 'zertifikat',
            'ausbildung', 'berufsausbildung', 'lehre', 'praktikum',
            'weiterbildung', 'fortbildung', 'schulung', 'kurs', 'seminar',
            'abitur', 'fachabitur', 'realschulabschluss', 'hauptschulabschluss',
            'gymnasium', 'realschule', 'hauptschule', 'gesamtschule',
            'berufschule', 'berufskolleg', 'fachschule'
        ]
        
        # German experience keywords
        self.experience_keywords = [
            'berufserfahrung', 'arbeitserfahrung', 'praxiserfahrung',
            'berufstätigkeit', 'beschäftigung', 'anstellung', 'tätigkeit',
            'position', 'stelle', 'arbeitsplatz', 'job', 'beruf',
            'arbeitgeber', 'unternehmen', 'firma', 'betrieb', 'organisation',
            'abteilung', 'bereich', 'team', 'projekt', 'aufgabe',
            'verantwortung', 'zuständigkeit', 'kompetenz', 'erfolg',
            'leistung', 'ergebnis', 'achievement', 'errungenschaft'
        ]
        
        # German job titles and positions
        self.job_titles = [
            # IT positions
            'softwareentwickler', 'programmierer', 'entwickler', 'coder',
            'frontend entwickler', 'backend entwickler', 'fullstack entwickler',
            'webentwickler', 'app entwickler', 'mobile entwickler',
            'systemadministrator', 'netzwerkadministrator', 'datenbankadministrator',
            'it-administrator', 'systemingenieur', 'netzwerkingenieur',
            'it-berater', 'it-consultant', 'technischer berater',
            'projektmanager', 'projektleiter', 'teamleiter', 'abteilungsleiter',
            'it-manager', 'technischer leiter', 'cto', 'it-direktor',
            
            # Engineering positions
            'ingenieur', 'bauingenieur', 'maschinenbauingenieur', 'elektroingenieur',
            'wirtschaftsingenieur', 'verfahrensingenieur', 'umweltingenieur',
            'konstrukteur', 'entwicklungsingenieur', 'projektingenieur',
            'qualitätsingenieur', 'fertigungsingenieur', 'produktionsingenieur',
            
            # Business positions
            'geschäftsführer', 'manager', 'direktor', 'leiter', 'vorstand',
            'vertriebsleiter', 'marketingleiter', 'personalleiter', 'finanzleiter',
            'controller', 'buchhalter', 'sachbearbeiter', 'assistent',
            'sekretär', 'empfangsmitarbeiter', 'kundenberater', 'verkäufer'
        ]
    
    def get_all_skills(self) -> List[str]:
        """Get all German skills combined"""
        return self.technical_skills + self.soft_skills
    
    def get_skill_categories(self) -> Dict[str, List[str]]:
        """Get skills organized by categories"""
        return {
            'technical': self.technical_skills,
            'soft': self.soft_skills,
            'education': self.education_keywords,
            'experience': self.experience_keywords,
            'positions': self.job_titles
        }
    
    def is_technical_skill(self, skill: str) -> bool:
        """Check if a skill is technical"""
        return skill.lower() in [s.lower() for s in self.technical_skills]
    
    def is_soft_skill(self, skill: str) -> bool:
        """Check if a skill is a soft skill"""
        return skill.lower() in [s.lower() for s in self.soft_skills]
    
    def find_skills_in_text(self, text: str) -> Dict[str, List[str]]:
        """Find all German skills mentioned in text"""
        text_lower = text.lower()
        found_skills = {
            'technical': [],
            'soft': [],
            'all': []
        }
        
        # Find technical skills
        for skill in self.technical_skills:
            if skill.lower() in text_lower:
                found_skills['technical'].append(skill)
                found_skills['all'].append(skill)
        
        # Find soft skills
        for skill in self.soft_skills:
            if skill.lower() in text_lower:
                found_skills['soft'].append(skill)
                found_skills['all'].append(skill)
        
        return found_skills


# German stop words for text processing
GERMAN_STOP_WORDS = {
    'der', 'die', 'das', 'den', 'dem', 'des', 'ein', 'eine', 'einer', 'eines', 'einem',
    'und', 'oder', 'aber', 'doch', 'sondern', 'jedoch', 'dennoch', 'trotzdem',
    'ich', 'du', 'er', 'sie', 'es', 'wir', 'ihr', 'sie', 'mich', 'dich', 'sich',
    'mir', 'dir', 'ihm', 'ihr', 'uns', 'euch', 'ihnen', 'mein', 'dein', 'sein',
    'ihr', 'unser', 'euer', 'ihr', 'dieser', 'diese', 'dieses', 'jener', 'jene',
    'jenes', 'welcher', 'welche', 'welches', 'alle', 'alles', 'einige', 'etliche',
    'manche', 'mehrere', 'viele', 'wenige', 'andere', 'anderer', 'anderes',
    'ist', 'sind', 'war', 'waren', 'bin', 'bist', 'sein', 'haben', 'hat', 'hatte',
    'hatten', 'werden', 'wird', 'wurde', 'wurden', 'kann', 'könnte', 'konnte',
    'konnten', 'soll', 'sollte', 'sollten', 'will', 'wollte', 'wollten', 'muss',
    'musste', 'mussten', 'darf', 'durfte', 'durften', 'mag', 'mochte', 'mochten',
    'in', 'an', 'auf', 'aus', 'bei', 'mit', 'nach', 'von', 'zu', 'für', 'über',
    'unter', 'vor', 'hinter', 'neben', 'zwischen', 'durch', 'gegen', 'ohne',
    'um', 'bis', 'seit', 'während', 'wegen', 'trotz', 'statt', 'anstatt',
    'als', 'wie', 'wenn', 'weil', 'da', 'damit', 'dass', 'ob', 'obwohl', 'falls',
    'nicht', 'kein', 'keine', 'keiner', 'keines', 'keinem', 'keinen', 'nie',
    'niemals', 'nichts', 'niemand', 'nirgends', 'nirgendwo', 'auch', 'noch',
    'schon', 'bereits', 'immer', 'oft', 'manchmal', 'selten', 'nie', 'heute',
    'gestern', 'morgen', 'hier', 'dort', 'da', 'wo', 'wohin', 'woher', 'warum',
    'weshalb', 'wieso', 'wann', 'wie', 'was', 'wer', 'wen', 'wem', 'wessen'
}


# Utility functions
def get_german_skills() -> GermanSkills:
    """Get instance of German skills"""
    return GermanSkills()


def extract_german_skills_from_text(text: str) -> List[str]:
    """Extract German skills from text"""
    skills = GermanSkills()
    found = skills.find_skills_in_text(text)
    return found['all']
